#!/bin/bash
set -e

# Connect to the default postgres database and run the SQL script
psql -v ON_ERROR_STOP=1 --username "admin" --dbname "postgres" <<-EOSQL
  DROP DATABASE IF EXISTS aura;
  CREATE DATABASE aura
    WITH OWNER = admin
    TEMPLATE = template0
    ENCODING = 'UTF8'
    LC_COLLATE = 'hu_HU.UTF-8'
    LC_CTYPE = 'hu_HU.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

  \connect aura
  ALTER DATABASE aura SET timezone TO 'Europe/Budapest';
  SET TIME ZONE 'Europe/Budapest';
EOSQL

# Additional script to verify the creation
psql -v ON_ERROR_STOP=1 --username "admin" --dbname "postgres" -c "\l"
