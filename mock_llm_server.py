#!/usr/bin/env python3
"""
Mock LLM Server for Testing SSE Streaming

This script simulates an LLM server that responds to OpenAI-compatible API calls
with streaming responses, allowing us to test the SSE streaming functionality
without requiring actual LLM API keys.
"""

import json
import time
import uuid
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class MockLLMHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """Handle POST requests to /v1/chat/completions"""
        if self.path != '/v1/chat/completions':
            self.send_error(404, "Not Found")
            return

        # Read the request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        try:
            request_data = json.loads(post_data.decode('utf-8'))
        except json.JSONDecodeError:
            self.send_error(400, "Invalid JSON")
            return

        # Check if this is a streaming request
        is_streaming = request_data.get('stream', False)
        accept_header = self.headers.get('Accept', '')

        if is_streaming or 'text/event-stream' in accept_header:
            self.handle_streaming_request(request_data)
        else:
            self.handle_regular_request(request_data)

    def handle_streaming_request(self, request_data):
        """Handle streaming SSE requests"""
        print(f"[MOCK LLM] Handling streaming request: {request_data.get('messages', [])[-1].get('content', 'No message')[:50]}...")

        # Set SSE headers
        self.send_response(200)
        self.send_header('Content-Type', 'text/event-stream')
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Connection', 'keep-alive')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        # Generate a response based on the user message
        user_message = ""
        messages = request_data.get('messages', [])
        if messages:
            user_message = messages[-1].get('content', '')

        response_text = self.generate_response(user_message)

        # Stream the response word by word
        self.stream_response(response_text, request_data.get('model', 'mock-model'))

    def handle_regular_request(self, request_data):
        """Handle regular JSON requests"""
        print(f"[MOCK LLM] Handling regular request: {request_data.get('messages', [])[-1].get('content', 'No message')[:50]}...")

        # Generate a response
        user_message = ""
        messages = request_data.get('messages', [])
        if messages:
            user_message = messages[-1].get('content', '')

        response_text = self.generate_response(user_message)

        # Return complete response
        response = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request_data.get('model', 'mock-model'),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(user_message.split()) + len(response_text.split())
            }
        }

        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode('utf-8'))

    def generate_response(self, user_message):
        """Generate a mock response based on the user message"""
        user_lower = user_message.lower()

        if 'streaming' in user_lower:
            return ("Server-Sent Events (SSE) streaming is a web standard that allows a server to push real-time "
                   "updates to a client over a single HTTP connection. When you make a request with "
                   "'Accept: text/event-stream', the server keeps the connection open and sends data as "
                   "events in the format 'data: <content>\\n\\n'. This is perfect for real-time applications "
                   "like chat systems, live notifications, or progress updates. The browser automatically "
                   "handles reconnection if the connection drops, making it very robust for real-time communication.")

        elif 'communication' in user_lower or 'real-time' in user_lower:
            return ("Real-time communication involves the immediate exchange of information between systems "
                   "with minimal delay. There are several approaches: WebSockets provide full-duplex communication "
                   "with very low latency, Server-Sent Events (SSE) offer server-to-client streaming over HTTP, "
                   "and WebRTC enables peer-to-peer communication for audio, video, and data. Each has its use cases: "
                   "WebSockets for interactive applications, SSE for live updates and notifications, and WebRTC "
                   "for media streaming and file sharing. The choice depends on your specific requirements for "
                   "latency, bandwidth, browser support, and the direction of data flow.")

        elif 'machine learning' in user_lower or 'ml' in user_lower:
            return ("Machine Learning is a subset of artificial intelligence that enables computers to learn "
                   "and improve from experience without being explicitly programmed. It works by using algorithms "
                   "to analyze data, identify patterns, and make predictions or decisions. There are three main types: "
                   "supervised learning (learning from labeled data), unsupervised learning (finding patterns in "
                   "unlabeled data), and reinforcement learning (learning through trial and error with rewards). "
                   "Common applications include recommendation systems, image recognition, natural language processing, "
                   "and predictive analytics. The field has exploded in recent years due to increased computing power, "
                   "larger datasets, and improved algorithms.")

        elif 'quantum' in user_lower:
            return ("Quantum computing is a revolutionary computing paradigm that leverages quantum mechanical "
                   "phenomena like superposition and entanglement to process information. Step by step: "
                   "1) Classical bits can only be 0 or 1, but quantum bits (qubits) can exist in superposition "
                   "of both states simultaneously. 2) This allows quantum computers to explore multiple solutions "
                   "in parallel. 3) Entanglement creates correlations between qubits that don't exist classically. "
                   "4) Quantum algorithms like Shor's algorithm can factor large numbers exponentially faster "
                   "than classical computers. 5) Current challenges include quantum decoherence and error rates. "
                   "While still experimental, quantum computing promises breakthroughs in cryptography, optimization, "
                   "drug discovery, and materials science.")

        elif 'hello' in user_lower or 'hi' in user_lower:
            return ("Hello! I'm a mock LLM server that simulates real AI responses for testing purposes. "
                   "I'm designed to help test the SSE streaming functionality of your chat application. "
                   "Try asking me about streaming, real-time communication, machine learning, or quantum computing "
                   "to see different responses. Each response is streamed word by word to simulate how a real "
                   "LLM would behave, allowing you to test the full streaming pipeline from server to client.")

        elif 'test' in user_lower:
            return ("This is a test response from the mock LLM server! I'm simulating how a real language model "
                   "would respond to your queries. This message is being streamed to you chunk by chunk through "
                   "Server-Sent Events (SSE), just like how real AI APIs work. You should see this text appearing "
                   "gradually as each word is 'streamed' from the server. This allows your application to provide "
                   "a more engaging user experience with real-time response generation. The streaming works by "
                   "sending each word as a separate SSE event with proper formatting.")

        else:
            return (f"Thank you for your message: '{user_message}'. This is a mock response from the simulated LLM server. "
                   "I'm designed to test the streaming functionality of your chat application. Try asking about "
                   "specific topics like 'streaming', 'machine learning', 'quantum computing', or 'real-time communication' "
                   "to get more detailed responses. Each response is streamed word by word to simulate real AI behavior "
                   "and test your SSE streaming implementation thoroughly.")

    def stream_response(self, response_text, model):
        """Stream the response word by word using SSE format"""
        words = response_text.split()
        chat_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"

        try:
            for i, word in enumerate(words):
                # Create SSE chunk
                chunk = {
                    "id": chat_id,
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": model,
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "content": word + " " if i < len(words) - 1 else word
                        },
                        "finish_reason": None
                    }]
                }

                # Send as SSE event
                self.wfile.write(f"data: {json.dumps(chunk)}\n\n".encode('utf-8'))
                self.wfile.flush()

                # Small delay to simulate real streaming
                time.sleep(0.05)  # 50ms delay between words

            # Send completion chunk
            final_chunk = {
                "id": chat_id,
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }

            self.wfile.write(f"data: {json.dumps(final_chunk)}\n\n".encode('utf-8'))
            self.wfile.write("data: [DONE]\n\n".encode('utf-8'))
            self.wfile.flush()

        except Exception as e:
            print(f"[MOCK LLM] Error during streaming: {e}")

    def do_GET(self):
        """Handle GET requests (health check)"""
        if self.path == '/health' or self.path == '/':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "service": "Mock LLM Server",
                "timestamp": datetime.now().isoformat(),
                "endpoints": ["/v1/chat/completions"]
            }
            self.wfile.write(json.dumps(response).encode('utf-8'))
        else:
            self.send_error(404, "Not Found")

    def log_message(self, format, *args):
        """Override to provide custom logging"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")

def run_server(port=11434):
    """Run the mock LLM server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockLLMHandler)

    print(f"🚀 Mock LLM Server starting on port {port}")
    print(f"📡 Health check: http://localhost:{port}/health")
    print(f"🤖 Chat endpoint: http://localhost:{port}/v1/chat/completions")
    print(f"📋 Send POST requests with OpenAI-compatible format")
    print(f"🔄 Streaming: Include 'Accept: text/event-stream' header or set 'stream': true")
    print(f"⚡ Press Ctrl+C to stop")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n🛑 Mock LLM Server shutting down...")
        httpd.shutdown()

if __name__ == '__main__':
    import sys

    port = 11434  # Default Ollama port
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Usage: python3 mock_llm_server.py [port]")
            sys.exit(1)

    run_server(port)
