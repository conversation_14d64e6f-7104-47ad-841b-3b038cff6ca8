FROM node:24.2-alpine

ARG VITE_PORT

WORKDIR /app

RUN --mount=type=secret,id=npm_token \
    NPM_TOKEN=$(cat /run/secrets/npm_token 2>/dev/null || echo "") && \
    if [ -n "$NPM_TOKEN" ]; then \
        echo -e "@dev:registry=https://gitlab.backfielddigital.com/api/v4/packages/npm/\n" \
        "//gitlab.backfielddigital.com/api/v4/packages/npm/:_authToken=$NPM_TOKEN" \
        > /app/.npmrc; \
    fi

COPY package.json ./
RUN npm install

COPY . /app

EXPOSE $VITE_PORT
