import { createRouter, createWebHistory } from 'vue-router'

import loadLayoutMiddleware from './layouts/loadLayoutMiddleware'
import { useUserStore } from './stores/user'
import NotFound from './views/404.vue'
import Home from './views/Home.vue'
import Login from './views/Login.vue'
import SignUp from './views/SignUp.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true },
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      layout: 'loggedOutLayout', 
    },
  },
  {
    path: '/sing-up',
    name: 'SignUp',
    component: SignUp,
    meta: {
      layout: 'loggedOutLayout',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { requiresAuth: true }
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach((to, _from, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next({ name: 'Login' })
  } else {
    next()
  }
})

router.beforeEach(loadLayoutMiddleware)
export default router
