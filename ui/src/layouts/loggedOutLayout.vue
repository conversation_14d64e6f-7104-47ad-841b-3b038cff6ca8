<template>
  <main class="logged-out-layout">
    <div class="logged-out-container">
      <div class="header">
        <img src="/chat_icon.svg">
        <h4>aura</h4>
      </div>

      <slot />
    </div>

    <toast />
  </main>
</template>

<script setup lang="ts">
import Toast from 'primevue/toast'
</script>

<style lang="scss" scoped>
.logged-out-layout {
  width: 100%;
  height: 100%;
  background-color: $gray-50;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $space-s;
}
.logged-out-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: fit-content;
  max-width: 400px;
  border-radius: $radius-large;
  border: 1px solid $border;
  overflow: hidden;
}
.header {
  display: flex;
  align-items: center;
  gap: $space-s;
  width: 100%;
  padding: $space-s $space-m;

  h4 {
    margin: 0;
    font-size: $heading-4;
    color: $text;
    font-weight: bold;
    letter-spacing: $space-xs;
  }
}

@media screen and (max-width: 768px) {

}
</style>
