<template>
  <div class="logged-in-layout">
    <side-panel />

    <main>
      <slot />
    </main>

    <toast />
  </div>
</template>

<script setup lang="ts">
import Toast from 'primevue/toast'
import { watch } from 'vue'

import SidePanel from '@/components/Navigation/SidePanel.vue'
import { useChatStore } from '@/stores/chat'

const chatStore = useChatStore()

watch(() => chatStore.activeChat?.title, (newTitle) => {
  document.title = newTitle || 'Aura AI'
}, { immediate: true })
</script>

<style lang="scss" scoped>
.logged-in-layout {
  width: 100%;
  height: 100%;
  display: flex;
}
main {
  background-color: $light-500;
  max-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  scrollbar-color: $gray-400 transparent;
  overflow-y: auto;
}
</style>
