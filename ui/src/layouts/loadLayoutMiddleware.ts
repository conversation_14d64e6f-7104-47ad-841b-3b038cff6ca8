import { type Component } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

import LoggedInLayout from './loggedInLayout.vue'
import LoggedOutLayout from './loggedOutLayout.vue'

const layoutMap: Record<string, Component> = {
  loggedInLayout: LoggedInLayout,
  loggedOutLayout: LoggedOutLayout,
}

const loadLayoutMiddleware = (route: RouteLocationNormalized) => {
  const layout: string = (route.meta.layout as string) || 'loggedInLayout'
  route.meta.layoutComponent = layoutMap[layout] || LoggedInLayout
}

export default loadLayoutMiddleware
