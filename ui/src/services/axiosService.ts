import type { AxiosError, AxiosResponse, Method, RawAxiosRequestConfig } from 'axios'
import axios from 'axios'
import type { ToastServiceMethods } from 'primevue/toastservice'

import en from '../locales/en.json'
import { ApiError, ParsingError } from './ApiError'
import { showToast } from './toastService'


interface ServerResponse<DataType = unknown> {
  data?: DataType
  messages?: Record<keyof typeof en, string[]>
  error?: Record<string, string[]>
}

interface ApiResponse<T> {
  parsedData: T
  status: number
  messages?: Record<keyof typeof en, string[]>
}


const isAxiosError = (error: unknown): error is AxiosError<ServerResponse<unknown>> => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'isAxiosError' in error &&
    error.isAxiosError === true &&
    'response' in error
  )
}


const handleApiError = (error: AxiosError<ServerResponse<unknown>>, toast: ToastServiceMethods | null): never => {
  let errorMessage: string | Record<string, string[]> = 'An error occurred'
  let errorDetails: Record<string, string[]>
  const statusCode = error.status

  const serverMessages = error.response?.data?.error
  if (serverMessages && typeof serverMessages === 'object' && Object.keys(serverMessages).length > 0) {
    errorDetails = serverMessages
    errorMessage = serverMessages
    for (const key in errorMessage) {
      if (Object.prototype.hasOwnProperty.call(errorMessage, key)) {
        if (Array.isArray(errorMessage[key])) {
          (errorMessage[key] as string[]).forEach((message: string) => {
            showToast(toast, 'error', 'Error', message)
          })
        }
      }
    }
    throw new ApiError('API_ERROR', errorDetails, statusCode)
  } else if (serverMessages && typeof serverMessages === 'string') {
    errorMessage = serverMessages
    showToast(toast, 'error', 'Error', errorMessage)
    throw new ApiError('API_ERROR', { detail: [errorMessage] }, statusCode)
  } else if (statusCode === 429) {
    errorMessage = 'Too Many Requests'
    showToast(toast, 'error', 'Error', errorMessage)
    throw new ApiError('TOO_MANY_REQUESTS', { detail: [errorMessage] }, statusCode)
  } else {
    // A `showToast` előtt ellenőrizzük, hogy az errorMessage string-e
    const messageToShow = typeof errorMessage === 'string' ? errorMessage : 'An error occurred'
    showToast(toast, 'error', 'Error', messageToShow)
    throw new ApiError('API_ERROR', { detail: [messageToShow] }, statusCode)
  }
}


export const handleApiRequest = async <T>(
  requestFn: () => Promise<AxiosResponse>,
  toast: ToastServiceMethods | null
): Promise<ApiResponse<T>> => {
  try {
    const response: AxiosResponse = await requestFn()

    let parsedData: T
    try {
      parsedData = typeof response.data?.data === 'string'
        ? JSON.parse(response.data.data)
        : response.data
    } catch (error) {
      showToast(toast, 'error', 'Error', 'Failed to parse server response')
      if (error instanceof Error) {
        throw new ParsingError(error.message)
      } else {
        throw new ParsingError('Unknown parsing error')
      }
    }
    return { parsedData, status: response.status, messages: response.data?.messages }
  } catch (error: unknown) {
    if (error instanceof ParsingError) {
      throw error
    }

    if (isAxiosError(error)) {
      handleApiError(error as AxiosError<ServerResponse<unknown>>, toast)
      throw error
    } else {
      throw error
    }
  }
}


const makeApiRequest = async <T>(
  method: Method,
  route: string,
  data: unknown = null,
  params: Record<string, unknown> = {},
  toast: ToastServiceMethods | null,
  headers: Record<string, string> = { Accept: 'application/json' }
): Promise<ApiResponse<T>> => {

  const apiBaseUrl = 'https://api.aura.local-env.com'

  if (!apiBaseUrl) {
    const errorMsg = 'API base URL is not configured. Set VITE_API_URL environment variable.'
    console.error(errorMsg)
    showToast(toast, 'error', 'Configuration Error', errorMsg)
    throw new Error(errorMsg)
  }

  const fullUrl = `${apiBaseUrl}${route}`


  const config: RawAxiosRequestConfig = {
    method: method,
    url: fullUrl,
    data: data,
    params: params,
    headers: headers,
    withCredentials: true,
  }

  return handleApiRequest<T>(() => axios(config), toast)
}

export const postAxios = <T>(
  route: string,
  data: unknown = null,
  toast: ToastServiceMethods | null,
  headers?: Record<string, string>
): Promise<ApiResponse<T>> =>
    makeApiRequest<T>('post', route, data, {}, toast, headers ?? { Accept: 'application/json' })

export const getAxios = <T>(
  route: string,
  params: Record<string, unknown> = {},
  toast: ToastServiceMethods | null,
  headers?: Record<string, string>
): Promise<ApiResponse<T>> =>
    makeApiRequest<T>('get', route, null, params, toast, headers ?? { Accept: 'application/json' })

export const patchAxios = <T>(
  route: string,
  data: unknown = null,
  toast: ToastServiceMethods | null,
  headers?: Record<string, string>
): Promise<ApiResponse<T>> =>
    makeApiRequest<T>('patch', route, data, {}, toast, headers ?? { Accept: 'application/json' })

export const putAxios = <T>(
  route: string,
  data: unknown = null,
  toast: ToastServiceMethods | null,
  headers?: Record<string, string>
): Promise<ApiResponse<T>> =>
    makeApiRequest<T>('put', route, data, {}, toast, headers ?? { Accept: 'application/json' })

export const deleteAxios = <T>(
  route: string,
  toast: ToastServiceMethods | null,
  headers?: Record<string, string>
): Promise<ApiResponse<T>> =>
    makeApiRequest<T>('delete', route, null, {}, toast, headers ?? { Accept: 'application/json' })
