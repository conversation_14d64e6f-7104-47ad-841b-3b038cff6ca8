
export class ApiError extends Error {
  apiMessages?: Record<string, string[]>
  statusCode?: number
  constructor(message: string, apiMessages?: Record<string, string[]>, statusCode?: number) {
    super(message)
    this.name = 'ApiError'
    this.apiMessages = apiMessages
    this.statusCode = statusCode
  }
}

export class ParsingError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ParsingError'
  }
}
