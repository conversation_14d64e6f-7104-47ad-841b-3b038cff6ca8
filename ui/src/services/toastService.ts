/* eslint-disable @typescript-eslint/no-explicit-any */
interface ToastOptions {
  severity: 'success' | 'info' | 'warn' | 'error';
  summary: string;
  detail: string;
  life?: number;
}

type ToastType = any;

export const showToast = (toast: ToastType, severity: ToastOptions['severity'], summary: string, detail: string, life: number = 4000) => {
  if (!toast) return
  toast.add({
    severity: severity,
    summary: summary,
    detail: detail,
    life: life
  })
}
