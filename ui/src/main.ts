import '@/styles/global.scss'
import 'primevue/resources/themes/md-light-indigo/theme.css'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import PrimeVue from 'primevue/config'
import ToastService from 'primevue/toastservice'
import Tooltip from 'primevue/tooltip'
import { createApp } from 'vue'

import App from './App.vue'
import i18n from './plugins/i18n'
import router from './router'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
const app = createApp(App)

app.use(PrimeVue)
app.use(router)
app.use(i18n)
app.use(ToastService)
app.use(pinia)
app.directive('tooltip', Tooltip)
app.mount('#app')

