@forward "./Sass/border-radius";
@forward "./Sass/color";
@forward "./Sass/font-size";
@forward "./Sass/spacing";
@forward "./Sass/box-shadow";
@forward "./Sass/timing";
@forward "./Sass/z-index";

@font-face {
  font-family: 'Nunito';
  src: url('@fonts/Nunito/static/Nunito-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Nunito';
  src: url('@fonts/Nunito/static/Nunito-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Nunito';
  src: url('@fonts/Nunito/static/Nunito-Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Nunito';
  src: url('@fonts/Nunito/Nunito-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

/* fallback */
@font-face {
  font-family: 'Material Symbols Rounded';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  // src: url('@iconfonts/MaterialSymbolsRounded.woff2') format('woff2');
  src: url('@iconfonts/MaterialSymbolsRounded.woff2') format('woff2');
}

.material-symbols-rounded {
  font-family: 'Material Symbols Rounded';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}


/*  Jobb lenne woff vagy woff2 -t használni a ttf et át kell hozzá konvertálni
ha ez megtörténik akkor a ttf -et törölni kell
és ezt kell használni hozzá

@font-face {
  font-family: 'Nunito';
  src: url('@fonts/Nunito/static/Nunito-Regular.woff2') format('woff2'),
       url('@fonts/Nunito/static/Nunito-Regular.woff') format('woff'),
       url('@fonts/Nunito/static/Nunito-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
*/

/* Alapértelmezett font beállítása az egész oldalra */
html, body {
  font-family: 'Nunito', sans-serif;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}
* {
  font-family: 'Nunito', sans-serif;
  box-sizing: border-box;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

#app {
  height: 100%;
  width: 100%;
}