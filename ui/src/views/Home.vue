<template>
  <div class="page">
    <div class="chat">
      <chat :chat-history="transformedMessages" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import chat from '@/components/Chat/Chat.vue'
import { useChatStore } from '@/stores/chat'
import type { ChatMessage, Csitcsett } from '@/types/Chat'


const chatStore = useChatStore()
const activeChat = computed(() => chatStore.activeChat)

const transformedMessages = computed(() => {
  return (activeChat.value?.messages ?? []).map((message: ChatMessage): Csitcsett => ({
    role: message.role,
    text: message.content,
  })) ?? []
})

</script>

<style lang="scss" scoped>
h1 {
  font-size: $heading-1;
  background: -webkit-linear-gradient($primary, $purple);
  background-clip: text;
  margin: $space-m 0;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 $space-xxs $space-m $space-xs;
}
.chat {
  width: 100%;
  height: 100%;
}
</style>
