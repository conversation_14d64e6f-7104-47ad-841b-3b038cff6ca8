<template>
  <div class="login">
    <div class="inputs">
      <jinn-input
        v-model="userEmail"
        placeholder="Email"
        :check-for-errors="!!error"
        :invalid="!!error"
        autofocus
        @keyup.enter="signUp"
      />
      <jinn-password
        v-model="userPassword"
        :placeholder="$t('general.password')"
        toggle-mask
        :errors="error"
        type="newPass"
        @keyup.enter="signUp"
      />

      <div v-if="error" class="error">
        <span class="material-symbols-rounded icon">error</span>
        <span>{{ error }}</span>
      </div>
    </div>
    <div class="actions">
      <jinn-button
        :label="$t('actions.cancel')"
        type="tertiary"
        @click="cancelSignUp"
      />
      <jinn-button
        :label="$t('general.signUp')"
        @click="signUp"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { JinnButton, JinnInput, JinnPassword } from '@dev/jinn-core-components'
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

import { ApiError } from '@/services/ApiError'
import { postAxios } from '@/services/axiosService'

const toast = useToast()
const router = useRouter()
const userEmail = ref('')
const userPassword = ref('')
const error = ref<string>('')

const cancelSignUp = () => {
  router.push({ name: 'Login' })
}

const checkEmailFormat = (email: string) => {
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    error.value = 'Invalid email format.'
  } else {
    error.value = ''
  }
}

const signUp = async () => {
  if (!userEmail.value || !userPassword.value) {
    error.value = 'Email and password are required.'
  }
  checkEmailFormat(userEmail.value)
  if (error.value) {
    return
  }

  try {
    await postAxios('/auth/register', {
      email: userEmail.value,
      password: userPassword.value
    }, toast)
    router.push({ name: 'Login' })
    toast.add({ severity: 'success', summary: 'Success', detail: 'Registration successful!', life: 5000 })
  } catch (err: unknown) {
    if (err instanceof ApiError && err.apiMessages) {
      error.value = Object.values(err.apiMessages).flat().join('\n')
    } else if (err instanceof Error) {
      error.value = err.message
    } else {
      error.value = 'Unknown error'
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
}
.inputs {
  display: flex;
  flex-direction: column;
  gap: $space-xs;
  width: 100%;
  padding: $space-xs $space-m;
}
.actions {
  display: flex;
  justify-content: space-between;
  gap: $space-xs;
  width: 100%;
  padding: $space-m;
}
.error {
  display: flex;
  align-items: center;
  gap: $space-xs;
  color: $text;
  background: $red-100;
  padding: $space-xs $space-s;
  border-radius: $radius-default;

  .icon {
    color: $error;
  }
}
</style>