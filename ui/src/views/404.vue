<template>
  <div class="404-page">
    <div class="text-container">
      <span class="material-symbols-rounded">search_off</span>
      <h4>{{ $t('404.title') }}</h4>
      <p>{{ $t('404.desc') }}</p>
      <jinn-button
        :label="$t('404.backToHome')"
        @click="$router.push({name: 'Home'})"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { JinnButton } from '@dev/jinn-core-components'
</script>

<style lang="scss" scoped>
.text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $space-s;
  color: text;
  max-width: 600px;
  padding: $space-l;
  border: 1px solid $border;
  text-align: center;

  h4 {
    margin: 0;
    font-size: $heading-4;
  }
  p {
    margin: 0;

  }
}
</style>