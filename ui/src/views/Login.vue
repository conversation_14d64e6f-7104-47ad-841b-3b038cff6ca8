<template>
  <div class="login">
    <div class="inputs">
      <jinn-input
        v-model="userEmail"
        placeholder="Email"
        :check-for-errors="!!error"
        :invalid="!!error"
        autofocus
        @keyup.enter="login"
      />
      <jinn-password
        v-model="userPassword"
        :placeholder="$t('general.password')"
        toggle-mask
        :errors="error"
        type="oldPass"
        @keyup.enter="login"
      />
  
      <div v-if="error" class="error">
        <span class="material-symbols-rounded icon">error</span>
        <span>{{ error }}</span>
      </div>
    </div>
    <div class="actions">
      <jinn-button
        :label="$t('general.signUp')"
        type="tertiary"
        @click="signUp"
      />
      <jinn-button
        :label="$t('general.login')"
        @click="login"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { JinnButton, JinnInput, JinnPassword } from '@dev/jinn-core-components'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

import { ApiError } from '@/services/ApiError'
import { postAxios } from '@/services/axiosService'
import { useUserStore } from '@/stores/user'
import type { User } from '@/types/User'

const router = useRouter()
const userEmail = ref('')
const userPassword = ref('')
const error = ref<string | unknown>('')

const userStore = useUserStore()
const login = async () => {
  try {
    if (!userEmail.value || !userPassword.value) {
      error.value = 'Email and password are required.'
      return
    }
    const { parsedData } = await postAxios('/auth/login', {
      email: userEmail.value,
      password: userPassword.value
    }, null)

    const user = (parsedData as { data: User }).data
    userStore.setUser(user)
    router.push({ name: 'Home' })
  } catch (err: unknown) {
    if (err instanceof ApiError && err.apiMessages) {
      error.value = Object.values(err.apiMessages).flat().join('\n')
    } else if (err instanceof Error) {
      error.value = err.message
    } else {
      error.value = 'Unknown error'
    }
  }
}

const signUp = () => {
  router.push({ name: 'SignUp' })
}
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
}
.inputs {
  display: flex;
  flex-direction: column;
  gap: $space-s;
  width: 100%;
  padding: $space-xs $space-m;
}
.actions {
  display: flex;
  justify-content: space-between;
  gap: $space-xs;
  width: 100%;
  padding: $space-m;
}
.error {
  display: flex;
  align-items: center;
  gap: $space-xs;
  color: $text;
  background: $red-100;
  padding: $space-xs $space-s;
  border-radius: $radius-default;

  .icon {
    color: $error;
  }
}
</style>