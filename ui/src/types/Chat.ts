export type Chat = {
  id: string
  title: string
  mode: string
  user_id: string
  created_at: string
  updated_at: string
  deleted_at?: string | null
  messages?: ChatMessage[]
}

export type ChatMessage = {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  status: 'pending' | 'completed' | 'error'
  chat_id: string
  created_at?: string
  updated_at?: string
  deleted_at?: string | null
}

export type Csitcsett = {
  text: string
  role: 'user' | 'assistant' | 'system'
}