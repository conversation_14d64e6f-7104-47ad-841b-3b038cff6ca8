<template>
  <modal @close="$emit('close')">
    <template #header>
      <span>{{ $t('chat.rename') }}</span>
    </template>

    <template #content>
      <jinn-input
        v-model="newName"
        :placeholder="$t('general.name')"
        :required="true"
        :autofocus="true"
        @keyup.enter="rename"
      />
    </template>

    <template #footer>
      <jinn-button :label="$t('actions.cancel')" type="tertiary" @click="$emit('close')" />
      <jinn-button
        :disabled="!validName"
        :label="$t('actions.rename')"
        type="tertiary"
        @click="rename"
      />
    </template>
  </modal>  
</template>

<script setup lang="ts">
import { JinnButton, JinnInput } from '@dev/jinn-core-components'
import { computed, ref } from 'vue'

import Modal from '@/components/Modal/Modal.vue'
import { useChatStore } from '@/stores/chat'
import type { Chat } from '@/types/Chat'

const props = defineProps < {
  id: Chat['id']
} > ()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const validName = computed(() => {
  const name = newName.value.trim()
  return name !== '' && name !== oldName
})

const store = useChatStore()
const oldName = store.getChatById(props.id)?.title ?? ''
const newName = ref<string>(oldName)

const rename = () => {
  if (!validName.value) return
  store.renameChat(props.id, newName.value)
  emit('close')
}
</script>

<style lang="scss" scoped>
  
</style>