<template>
  <div class="prime-modal">
    <prime-modal
      v-model:visible="visible"
      modal
      header="Header"
      position="top"
      :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      @hide="$emit('close')"
    >
      <template #header>
        <slot name="header" />
      </template>

      <template v-if="$slots.helper">
        <div class="helper-text">
          <slot name="helper" />
        </div>
      </template>

      <slot name="content" />

      <template v-if="$slots.footer" #footer>
        <slot name="footer" />
      </template>
    </prime-modal>
  </div>
</template>

<script setup lang="ts">
import primeModal from 'primevue/dialog'
import { ref } from 'vue'

defineEmits(['close'])

const visible = ref<boolean>(true)
</script>

<style lang="scss">
.p-dialog {
  width: auto!important;
  position: absolute!important;
  top: 120px;
}
.p-dialog-top {
}
.p-dialog-mask {
  z-index: 1001!important;
}
.p-dialog-header {
  padding: $space-s;
  border-bottom: 1px solid $border;
  cursor: move;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;

  span {
    font-size: $body-xl;
    font-weight: bold;
  }
}
.p-dialog-header:active {
  cursor: grabbing;
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}
.p-dialog-content {
  padding: $space-s;
}
.p-dialog-footer {
  padding: $space-s;
  border-top: 1px solid $border;
  display: flex;
  justify-content: flex-end;
}
.helper-text {
  margin-bottom: $space-s;
  color: $placeholder-text;
}
</style>
