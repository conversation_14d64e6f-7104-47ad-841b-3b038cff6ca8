<template>
  <jinn-dropdown direction-y="top" :teleport-to="generalStore.sidePanelState ? 'body' : '.sidepanel'">
    <template #trigger>
      <jinn-list-item>
        <template #leading>
          <span class="material-symbols-rounded leading-icon">language</span>
        </template>
        <template #content>
          <span>{{ $t(`languages.${selectedLocale}`) }}</span>
        </template>
        <template #trailing>
          <span class="material-symbols-rounded trailing-icon">
            {{ 'arrow_drop_up' }}
          </span>
        </template>
      </jinn-list-item>
    </template>

    <template #content>
      <div class="dropdown-content">
        <jinn-list-item
          v-for="(localeItem, index) in availableLocales"
          :key="`language-${index}`"
          @item-click="selectLocale(localeItem)"
        >
          <template #content>
            <span class="text">{{ $t(`languages.${localeItem}`) }}</span>
          </template>
          <template #trailing>
            <span v-if="selectedLocale === localeItem" class="material-symbols-rounded trailing-icon check">
              check
            </span>
          </template>
        </jinn-list-item>
      </div>
    </template>
  </jinn-dropdown>
</template>

<script setup lang="ts">
import { JinnDropdown, JinnListItem } from '@dev/jinn-core-components'
import { ref } from 'vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { availableLocales, type SupportedLocale } from '@/plugins/locales'
import { useGeneralStore } from '@/stores/general'

const { locale } = useI18n()
const showOptions = ref(false)
const LOCAL_STORAGE_KEY = 'user-preferred-locale'
const savedLocale = localStorage.getItem(LOCAL_STORAGE_KEY)
const generalStore = useGeneralStore()

if (savedLocale && availableLocales.includes(savedLocale as SupportedLocale)) {
  locale.value = savedLocale as SupportedLocale
}

const selectedLocale = computed<SupportedLocale>({
  get: () => locale.value as SupportedLocale,
  set: (value) => {
    locale.value = value
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, value)
    } catch (e) {
      console.error('Failed to save locale to localStorage', e)
    }
  },
})

const selectLocale = (localeItem: SupportedLocale) => {
  selectedLocale.value = localeItem
  showOptions.value = false
}
</script>

<style lang="scss" scoped>
.leading-icon,
.trailing-icon {
  width: 40px;
}
.check {
  color: $primary;
}
.logout-button-texts {
  display: flex;
  flex-direction: column;
  gap: $space-xxs;
  align-items: flex-start;

  p {
    margin: 0;
  }
  span {
    font-size: $body-s;
    color: $black-500;
  }
}
.dropdown-content {
  width: 300px;
}
.text {
  padding-left: $space-xs;
}
</style>