<template>
  <div class="content-container">
    <header class="content-header">
      <div class="flex-container">
        <span class="material-symbols-rounded">chat_bubble_outline</span>
        <p>{{ $t('chat.chats') }}</p>
      </div>

      <jinn-button
        type="text-black"
        icon="local_fire_department"
        @click="deleteAll()"
      />
    </header>

    <div class="chat-list">
      <jinn-list-item
        v-for="(item, i) in items"
        :key="`list-item-${i}`"
        :disabled="disabled"
        :active="store.activeChat?.id === item.id"
        class="jinn-list-item"
        :class="{'isOpen': isOpen[i]}"
        @item-click="setActive(item.id)"
      >
        <template #content>
          <span class="text">{{ item.title }}</span>
        </template>

        <template #trailing>
          <jinn-dropdown
            direction-y="bottom"
            direction-x="left"
            :teleport-to="generalStore.sidePanelState ? 'body' : '.sidepanel'"
            @update:is-open="isOpen[i] = $event"
          >
            <template #trigger>
              <span class="material-symbols-rounded more-icon">more_vert</span>
            </template>

            <template #content>
              <div class="dropdown-content">
                <jinn-list-item
                  v-for="(option, index) in options"
                  :key="`option-${index}`"
                  @item-click="performAction(option.action, item.id)"
                >
                  <template #leading>
                    <span class="material-symbols-rounded leading-icon">{{ option.icon }}</span>
                  </template>
                  <template #content>
                    <span class="one-option-text">{{ option.name }}</span>
                  </template>
                </jinn-list-item>
              </div>
            </template>
          </jinn-dropdown>
        </template>
      </jinn-list-item>
    </div>
  </div>
</template>

<script setup lang="ts">
import { JinnButton, JinnDropdown, JinnListItem } from '@dev/jinn-core-components'
import { ref } from 'vue'

import { useChatStore } from '@/stores/chat'
import { useGeneralStore } from '@/stores/general'
import { type Chat } from '@/types/Chat'

const store = useChatStore()
const generalStore = useGeneralStore()
const isOpen = ref<boolean[]>([])

withDefaults(
  defineProps<{
    items?: Chat[]
    disabled?: boolean
  }>(),
  {
    items: () => [],
    disabled: false,
  },
)
const emit = defineEmits<{
  (e: 'rename', id: Chat['id']): void
  (e: 'delete', id: Chat['id']): void
  (e: 'delete-all'): void
}>()

const setActive = (id: Chat['id']) => {
  store.setActiveChat(id)
}

const deleteAll = () => {
  emit('delete-all')
}

const options = ref([
  { name: 'Rename', action: 'rename', icon: 'edit' },
  { name: 'Delete', action: 'delete', icon: 'delete' },
])

const performAction = (action: string, id: Chat['id']) => {
  if (action === 'delete') {
    emit('delete', id)
  } else if (action === 'rename') {
    emit('rename', id)
  }
}
</script>

<style lang="scss" scoped>
.content-container {
  height: 100%;
}
.text {
  padding-left: $space-xs;
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.more-icon {
  width: 40px;
  display: none;
  opacity: 0;
  transition: all $duration-short ease-in-out;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: calc($space-xs + $space-xxs) $space-s;
  color: $text;

  p {
    margin: 0;
    font-size: $body-m;
  }
  .flex-container {
    display: flex;
    align-items: center;
    gap: $space-xs;
  }
  span {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $heading-5;
  }
}
.jinn-list-item {
  &:hover {
    .text {
      max-width: 200px;
    }
    .more-icon {
      display: inline-block;
      opacity: 1;
    }
  }
  &.isOpen {
    .text {
      max-width: 200px;
    }
    .more-icon {
      display: inline-block;
      opacity: 1;
    }
  }
}
.chat-list {
  display: flex;
  flex-direction: column;
  gap: $space-xs;
  padding: $space-xxs $space-s;
  max-height: calc(100vh - 310px);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.dropdown-content {
  min-width: 170px;
}
.leading-icon {
  width: 40px;
}
.one-option-text {
  padding-right: $space-xs;
}
</style>

<style lang="scss">
.dropdown-option {
  z-index: 1001!important;
}
</style>