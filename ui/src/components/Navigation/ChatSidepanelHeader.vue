<template>
  <div class="chat-sidepanel-header">
    <div class="logo">
      <img src="/chat_icon.svg" alt="logo">
      <h4>aura</h4>
    </div>

    <jinn-button type="text-black" icon="add_circle_outline" @click="$emit('add')" />
  </div>
</template>

<script setup lang="ts">
import { JinnButton } from '@dev/jinn-core-components'

defineEmits<{
  (e: 'add'): void
}>()
</script>

<style lang="scss" scoped>
.chat-sidepanel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.logo {
  display: flex;
  align-items: center;
  gap: $space-s;

  h4 {
    font-size: $heading-4;
    margin: 0;
    letter-spacing: $space-xs;
  }
}
</style>
