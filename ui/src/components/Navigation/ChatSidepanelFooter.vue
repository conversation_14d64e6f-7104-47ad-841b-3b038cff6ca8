<template>
  <div class="chat-sidepanel-footer">
    <div class="language-select">
      <language-select />
    </div>

    <div class="logout">
      <jinn-list-item @item-click="logOut()">
        <template #leading>
          <span class="material-symbols-rounded leading-icon">logout</span>
        </template>
        <template #content>
          <div class="logout-button-texts">
            <p>{{ $t('general.logout') }}</p>
            <span>{{ $t('general.loggedInAs') }}:</span>
            <!-- TODO: valtozo behelyettesites -->
            <span>{{ user?.email }}</span>
          </div>
        </template>
      </jinn-list-item>
    </div>
  </div>
</template>

<script setup lang="ts">
import { JinnListItem } from '@dev/jinn-core-components'
import { useRouter } from 'vue-router'

import LanguageSelect from '@/components/LanguageSelect.vue'
import { postAxios } from '@/services/axiosService'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const user = userStore.user

const router = useRouter()
const logOut = async () => {
  try {
    await postAxios('/auth/logout', {}, null)
    userStore.clearUser()
    router.push({ name: 'Login' })
  } catch (error) {
    console.error('Logout error:', error)
  }
}
</script>

<style lang="scss" scoped>
.leading-icon,
.trailing-icon {
  width: 40px;
}
.check {
  color: $primary;
}
.logout-button-texts {
  display: flex;
  flex-direction: column;
  gap: $space-xxs;
  align-items: flex-start;

  p {
    margin: 0;
  }
  span {
    font-size: $body-s;
    color: $black-500;
  }
}
.dropdown-content {
  width: 300px;
}
.text {
  padding-left: $space-xs;
}
</style>
