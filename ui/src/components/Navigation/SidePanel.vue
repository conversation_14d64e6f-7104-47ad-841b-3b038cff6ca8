<template>
  <jinn-side-panel 
    class="sidepanel" 
    :default-open="generalStore.sidePanelState"
    open-on-hover
    @update:is-open="generalStore.toggleSidePanel($event)"
  >
    <template #header="{ isOpen }">
      <transition name="slide-fade">
        <chat-sidepanel-header v-if="isOpen" @add="addChat" />
      </transition>
    </template>

    <template #content="{ isOpen }">
      <transition name="slide-fade">
        <chat-sidepanel-content
          v-if="isOpen"
          :items="chats"
          @delete-all="clearChats"
          @delete="deleteChat"
          @rename="renameChat"
        />
      </transition>
    </template>

    <template #footer="{ isOpen }">
      <transition name="slide-fade">
        <chat-sidepanel-footer v-if="isOpen" />
      </transition>
    </template>
  </jinn-side-panel>

  <rename-modal v-if="renameOpen" :id="renameChatId" @close="renameOpen = !renameOpen" />
</template>

<script setup lang="ts">
import { JinnSidePanel } from '@dev/jinn-core-components'
import { computed, ref } from 'vue'

import RenameModal from '@/components/Modal/RenameModal.vue'
import { useChatStore } from '@/stores/chat'
import { useGeneralStore } from '@/stores/general'
import type { Chat } from '@/types/Chat'

import ChatSidepanelContent from './ChatSidepanelContent.vue'
import ChatSidepanelFooter from './ChatSidepanelFooter.vue'
import ChatSidepanelHeader from './ChatSidepanelHeader.vue'

const chatStore = useChatStore()
const generalStore = useGeneralStore()
const chats = computed(() => chatStore.chats)
const renameOpen = ref(false)

const addChat = () => {
  chatStore.clearActiveChat()
}

const clearChats = () => {
  chatStore.clearChats()
}

const deleteChat = (id: Chat['id']) => {
  chatStore.deleteChat(id)
}

let renameChatId: Chat['id']
const renameChat = (id: Chat['id']) => {
  renameOpen.value = true
  renameChatId = id
}
</script>

<style lang="scss" scoped>
.sidepanel {
  :deep(footer) {
    border-top: 1px solid $border;
  }
}
</style>
