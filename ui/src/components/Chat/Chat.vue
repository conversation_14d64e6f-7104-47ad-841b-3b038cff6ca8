<!-- eslint-disable vue/attribute-hyphenation-->
<template>
  <div class="chat-container">
    <chat-empty-state
      v-if="!chatStore.chatInitalized"
    />
    <deep-chat
      :history="props.chatHistory"
      :connect="connectToChat"
      :style="baseStyles"
      :avatars="avatars"
      :messageStyles="messageStyles"
      :submitButtonStyles="submitButtonStyles"
      :customButtons="customButtons"
      :textInput="textInput"
      :mixedFiles="mixedFiles"
      :attachmentContainerStyle="attachmentContainerStyle"
      :auxiliaryStyle="auxiliaryChatStyles"
      class="chat-panel"
      :class="{'empty-chat': !chatStore.chatInitalized}"
    />
  </div>
</template>

<script setup lang="ts">
import 'deep-chat'

import { fetchEventSource } from '@microsoft/fetch-event-source'
import { computed, ref } from 'vue'

import ChatEmptyState from '@/components/Chat/ChatEmptyState.vue'
import {
  attachmentContainerStyle,
  auxiliaryChatStyles,
  avatars,
  baseStyles,
  customButtons,
  customButtonState,
  messageStyles,
  mixedFiles,
  submitButtonStyles,
  textInput} from '@/components/Chat/ChatStyles'
import { useChatStore } from '@/stores/chat'
import type { Csitcsett } from '@/types/Chat'

const chatStore = useChatStore()

interface MessageBody {
  messages: {
    role: 'user' | 'assistant' | 'system'
    text: string
  }[]
}

interface Signals {
  onOpen: () => void
  onResponse: (response: { text?: string; error?: string; overwrite?: boolean }) => void
  onClose: () => void
  stopClicked: {
    listener: () => void
  }
  newUserMessage: {
    listeners: () => void
  }
}

interface MessageData {
  content: string
  done: boolean
  messageId: string
  delta?: string
  role: 'user' | 'assistant' | 'system'
  status?: 'completed' | 'pending' | 'failed'
  chatId?: string
  createdAt?: string
  id?: string
  userId?: string
  error?: string
}

let activeChatId = computed(() => chatStore.activeChat?.id)
const connectToChat = {
  stream: true,
  handler: (body: MessageBody, signals: Signals) => {
    try {
      let responseId = ''
      let chatId = ref(activeChatId.value?.toString())

      const mode = customButtonState.value === 'active' ? 'deep_research' : 'general'

      const eventHandlers: Record<string, (args: { id: string, data: MessageData }) => void> = {
        'message.created': ({ id, data }) => {
          chatStore.initalizeChat()
          if (data.role === 'assistant') {
            responseId = id

            chatId.value = activeChatId.value ?? data.chatId
          }
        },
        'message.streaming': ({ data }) => {
          signals.onResponse({
            text: data.delta ?? data.content ?? '',
          })
        },
        'message.completed': ({ data }) => {
          signals.onResponse({
            text: data.content,
            overwrite: true,
          })
          chatStore.setActiveChat(chatId.value ?? '')
        },
        'message.failed': ({ data }) => {
          signals.onResponse({
            error: data.error ?? 'Message failed',
          })
        },
      }

      //TODO: config file-bol az url-t
      const baseUrl = 'https://api.aura.local-env.com/chat'
      const requestUrl = `${baseUrl}${activeChatId.value ? `/${activeChatId.value}` : ''}`

      fetchEventSource(requestUrl, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          message: body.messages[body.messages.length - 1].text,
          mode,
        }),
        async onopen(response) {
          if (response.ok) {
            signals.onOpen()
          } else {
            signals.onResponse({ error: 'error' })
          }
        },
        onmessage({ id, event, data }) {
          let messageData: MessageData

          if (responseId && id !== responseId) {
            return
          }
          try {
            messageData = JSON.parse(data)
          } catch (e) {
            console.error('Error parsing message:', e)
            signals.onResponse({ error: 'Error parsing message' })
            return
          }

          const handler = eventHandlers[event]
          if (handler) {
            handler({ id, data: messageData })
          } else {
            console.warn('Unknown event:', event)
          }
        },
        onerror(message) {
          signals.onResponse({ error: message })
        },
        onclose() {
          signals.onClose()
        }
      })
      signals.stopClicked.listener = () => {
        console.log('Stop clicked')
      }
    } catch (err) {
      console.error('Error connecting to chat:', err)
      signals.onResponse({ error: 'Error connecting to chat' })
    }
  }
}

const props = withDefaults(defineProps<{
  chatHistory?: Csitcsett[]
}>(), {
  chatHistory: () => []
})
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  gap: $space-s;
  width: 100%;
  height: 100%;
}
.empty-chat {
  height: 220px!important;
}
</style>
