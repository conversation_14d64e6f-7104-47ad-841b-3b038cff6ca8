import { ref } from 'vue'

export const customButtonState = ref('default')

export const baseStyles = {
  backgroundColor: '#FAFBFC',
  border: 'none',
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
  fontFamily: '"Nunito", sans-serif'
}

export const messageStyles = {
  default: {
    shared: {
      outerContainer: {
        margin: '0 auto',
        maxWidth: '1050px',
      },
      innerContainer: {
        width: '100%',
      },
      bubble: {
        backgroundColor: 'unset',
        padding: '8px 16px',
        marginTop: '16px',
        marginBottom: '16px',
        borderRadius: '16px',
      }
    },
    user: {
      bubble: {
        color: 'black',
        backgroundColor: '#DDBDE6',
        borderBottomRightRadius: '0px',
        maxWidth: '70%',
      }
    },
    ai: {
      bubble: {
        maxWidth: '100%',
        backgroundColor: 'transparent',
      }
    }
  }
}

export const avatars = {
  ai: {
    src: '/ai_message_icon.svg',
    styles: {
      avatar: {
        marginTop: '7px',
        marginRight: '-2px',
        paddingTop: '0px'
      }
    }
  },
  user: {
    styles: {
      avatar: {
        display: 'none',
      }
    }
  }
}

export const textInput = {
  styles: {
    'container': {
      width: '100%',
      minHeight: '40px',
      maxWidth: '1020px',
      border: '1px solid #00000014',
      borderRadius: '16px',
      padding: '16px 24px 62px 24px',
      boxShadow: '0px -35px 40px -10px rgba(255,255,255,1)',
      '-webkitBoxShadow': '0px -35px 40px -10px rgba(255,255,255,1)',
      '-mozBoxShadow': '0px -35px 40px -10px rgba(255,255,255,1)',
      margin: '0',
    },
    text: { color: 'black', padding: '0.5rem 0.7rem' }
  }
}

export const submitButtonStyles = {
  submit: {
    container: {
      default: {
        margin: '0 24px 8px 24px',
        height: '22px',
        padding: '9px 8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }
    },
    svg: {
      content: '<svg width="21" height="18" viewBox="0 0 21 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.00999999 18L21 9L0.00999999 0L0 7L15 9L0 11L0.00999999 18Z" fill="#1976D2"/></svg>',
    }
  },
  stop: {
    container: {
      default: {
        padding: '8px 8px 10px 8px',
      }
    },
    svg: {
      content: '<svg width="21" height="18" viewBox="0 0 21 18" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="2" width="15" height="15" rx="4" fill="#BABABA"/></svg>',
    }
  }
}

export const customButtons = [
  {
    position: 'inside-left',
    styles: {
      button: {
        default: {
          container: {
            default: {
              border: '1px solid #e2e2e2',
              height: '22px',
              padding: '8px',
              borderRadius: '10px',
              margin: '0 24px 8px 72px'
            }
          },
          svg: {
            'content': '\n <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 0 25 25" fill="none">\n <path d="M12 22.3201C17.5228 22.3201 22 17.8429 22 12.3201C22 6.79722 17.5228 2.32007 12 2.32007C6.47715 2.32007 2 6.79722 2 12.3201C2 17.8429 6.47715 22.3201 12 22.3201Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>\n <path d="M2 12.3201H22" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>\n <path d="M12 22.3201C13.933 22.3201 15.5 17.8429 15.5 12.3201C15.5 6.79722 13.933 2.32007 12 2.32007C10.067 2.32007 8.5 6.79722 8.5 12.3201C8.5 17.8429 10.067 22.3201 12 22.3201Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>\n  </svg>'
          },
          text: {
            content: 'Deep research'
          }
        }
      }
    },
    onClick: (state: string) => {
      customButtonState.value = state === 'default' ? 'active' : 'default'
      return customButtonState.value
    }
  }
]

export const mixedFiles = {
  button: {
    position: 'inside-left',
    styles: {
      container: {
        default: {
          margin: '0 24px 8px 24px',
          height: '24px',
          padding: '8px',
        }
      },
      svg: {
        styles: {
          default: {
            margin: '8px'
          },
        }
      }
    }
  }
}

export const attachmentContainerStyle = {
  backgroundColor: 'white',
  border: '1px solid #00000014',
  borderBottom: 'none',
  borderRadius: '0',
  borderTopRightRadius: '16px',
  borderTopLeftRadius: '16px',
  width: '100%',
  maxWidth: '1070px',
  padding: '16px 24px',
  minHeight: '100px',
  bottom: '0',
  left: '0',
  top: '-95px',
  zIndex: '2',
  boxSizing: 'border-box',
  boxShadow: '0px -35px 40px -10px rgba(255,255,255,1)',
  '-webkitBoxShadow': '0px -35px 40px -10px rgba(255,255,255,1)',
  '-mozBoxShadow': '0px -35px 40px -10px rgba(255,255,255,1)',
}

export const auxiliaryChatStyles = `
.outer-message-container:last-child {
  padding-bottom: 128px;
}
.outer-message-container:first-child {
  padding-top: 32px;
}
.file-attachment {
  height: 3.35rem;
  width: 3.35rem;
}
.file-attachment:has(.border-bound-attachment) {
  width: 14.85rem;
  height: 3.35rem;
}
.image-attachment {
  border-radius: 8px;
}
.border-bound-attachment {
  padding: 0 8px;
  font-size: 14px;
  border-radius: 8px;
  border: 1px solid #DDBDE6;
  background-color: #F4E9F7;
  width: calc(100% - 10px);
}
.file-attachment-text-container::before {
  content: '📄';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.2rem;
  height: 2.2rem;
  margin-right: 4px;
  background: #DDBDE6;
  border-radius: 4px;
  color: #fff;
  font-size: 1.2rem;
  vertical-align: middle;
}
.any-file-attachment-text {
  margin: 0;
  max-width: 75%;
}
#input {
  width: 100%;
  max-width: 1070px;
  justify-content: unset;
}
`