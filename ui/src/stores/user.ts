import { defineStore } from 'pinia'
import { computed,type Ref, ref } from 'vue'

import type { User } from '@/types/User'

const initialUser: User | null = null
const now = Math.floor(Date.now() / 1000)

export const useUserStore = defineStore('user', () => {
  const user: Ref<User | null> = ref(initialUser)

  const isLoggedIn = computed(() => {
    return user.value !== null &&
      user.value.expiresAt >= now
  })

  const setUser = (newUser: User) => {
    user.value = newUser
  }

  const clearUser = () => {
    user.value = null
  }

  return { user, setUser, clearUser, isLoggedIn }
}, {
  persist: {
    beforeHydrate: (context) => {
      console.log('💧 Hydrating store:', context.store.$id)
    }
  }
})