import { defineStore } from 'pinia'
import { type Ref, ref } from 'vue'

const initialPanelState: boolean = false

export const useGeneralStore = defineStore('general', () => {
  const sidePanelState: Ref<boolean> = ref(initialPanelState)

  const toggleSidePanel = (state: boolean) => {
    sidePanelState.value = state
  }

  return { sidePanelState, toggleSidePanel }
}, {
  persist: {
    beforeHydrate: (context) => {
      console.log('💧 Hydrating store:', context.store.$id)
    }
  }
})