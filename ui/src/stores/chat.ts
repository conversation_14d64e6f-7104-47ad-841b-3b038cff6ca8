import { useEventSource } from '@vueuse/core'
import { defineStore } from 'pinia'
import { type Ref, ref, watch } from 'vue'

import { deleteAxios, getAxios, putAxios } from '@/services/axiosService'
import type { Chat } from '@/types/Chat'

const initialChats: Chat[] | [] =  []

export const useChatStore = defineStore('chat', () => {
  const chats: Ref<Chat[] | []> = ref(initialChats)
  const activeChat: Ref<Chat | null> = ref(null)
  const chatInitalized = ref(false)

  const addChat = (chat: Chat) => {
    chats.value = [...chats.value, chat]
    setActiveChat(chat.id)
  }

  const clearChats = async() => {
    await deleteAxios('/chat', null)
    activeChat.value = null
  }

  const setChats = (newChats: Chat[]) => {
    chats.value = [...newChats]
  }

  const clearActiveChat = () => {
    activeChat.value = null
    chatInitalized.value = false
  }

  const renameChat = async (chatId: Chat['id'], newName: string) => {
    try {
      await putAxios(`/chat/${chatId}`, { title: newName }, null)
    } catch (error) {
      console.error('Error renaming chat:', error)
    }
  }

  const deleteChat = async (chatId: Chat['id']) => {
    try {
      await deleteAxios(`/chat/${chatId}`, null)
    } catch (error) {
      console.error('Error deleting chats(s):', error)
    }
  }

  const setActiveChat = async (chatId: Chat['id']) => {
    try {
      const { parsedData } = await getAxios(`/chat/${chatId}`, {}, null)
      activeChat.value = (parsedData as { data: Chat }).data
      initalizeChat()
    } catch (error) {
      console.error('Error fetching chats:', error)
    }
  }

  const initalizeChat = () => {
    chatInitalized.value = true
  }

  const getChatById = (chatId: Chat['id']) => {
    return chats.value.find(chat => chat.id === chatId) || null
  }

  const loadChatsFromBackend = async () => {
    try {
      const { parsedData } = await getAxios('/chat', {}, null)
      chats.value = (parsedData as { data: Chat[] }).data
    } catch (error) {
      console.error('Error fetching chats:', error)
    }
  }

  const listenToChatUpdates = () => {
    const { data } = useEventSource(
      'https://api.aura.local-env.com/chat',
      ['chat.list_updated'] as const,
      { withCredentials: true },
    )

    watch(
      () => data.value,
      (data: string) => setChats(JSON.parse(data ?? '[]') as Chat[]),
      { immediate: true },
    )
  }

  loadChatsFromBackend()
  listenToChatUpdates()

  return {
    chats,
    activeChat,
    addChat,
    clearChats,
    setChats,
    renameChat,
    deleteChat,
    setActiveChat,
    clearActiveChat,
    getChatById,
    initalizeChat,
    chatInitalized,
  }
}, {
  persist: {
    beforeHydrate: (context) => {
      console.log('💧 Hydrating store:', context.store.$id)
    }
  }
})
