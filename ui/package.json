{"name": "aura-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@dev/jinn-core-components": "1.8.31", "@microsoft/fetch-event-source": "2.0.1", "@vueuse/core": "13.5.0", "axios": "1.10.0", "deep-chat": "2.2.1", "pinia": "3.0.3", "pinia-plugin-persistedstate": "4.3.0", "primevue": "3.53.1", "vue": "3.5.16", "vue-i18n": "11.1.6", "vue-router": "4.5.1"}, "devDependencies": {"@eslint/js": "9.29.0", "@intlify/unplugin-vue-i18n": "6.0.8", "@types/node": "24.0.1", "@vitejs/plugin-vue": "5.2.4", "@vue/tsconfig": "0.7.0", "eslint": "9.29.0", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-unused-imports": "4.1.4", "eslint-plugin-vue": "10.2.0", "globals": "16.2.0", "sass": "1.89.2", "typescript": "5.8.3", "typescript-eslint": "8.34.1", "vite": "6.3.5", "vue-tsc": "2.2.10"}}