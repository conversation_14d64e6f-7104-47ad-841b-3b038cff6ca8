import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const port = parseInt(env.VITE_PORT, 10) || 5173

  return {
    plugins: [vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => ['deep-chat'].includes(tag),
        },
      },
    }),
    VueI18nPlugin({
      include: resolve(__dirname, './src/locales/**'),
      runtimeOnly: false,
    })
    ],
    resolve: {
      alias: {
        '@dev/jinn-core-components': resolve(
          __dirname,
          'node_modules/@dev/jinn-core-components/dist/genai/jinn-core-components',
        ),
        '@': resolve('.', 'src'),
        '/assets': resolve(__dirname, 'src/assets'),
        '/images': resolve(__dirname, 'public/images'),
        '@fonts': resolve(__dirname, 'src/assets/fonts'),
        '@iconfonts': resolve(__dirname, 'src/assets/iconfonts'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: (content, filename) => {
            const normalizedFilename = filename.replace(/\\/g, '/')
            if (normalizedFilename.endsWith('/src/styles/global.scss')) {
              return content
            }
            return '@use "/src/styles/global.scss" as *;\n' + content
          },
          includePaths: [
            resolve(__dirname, 'src/styles'),
            resolve(__dirname, 'src/assets/fonts'),
            resolve(__dirname, 'src/assets/iconfonts'),
          ],
        },
      },
    },
    server: {
      host: '0.0.0.0',
      allowedHosts: ['aura.local-env.com', 'localhost'],
      port, // Default to 3000 if VITE_PORT is not set
      hmr: {
        host: 'aura.local-env.com', // Allow external connections for HMR
      },
    },
  }
})
