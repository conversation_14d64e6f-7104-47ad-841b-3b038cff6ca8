import pluginJs from '@eslint/js'
import simpleImportSort from 'eslint-plugin-simple-import-sort'
import unusedImports from 'eslint-plugin-unused-imports'
import pluginVue from 'eslint-plugin-vue'
import globals from 'globals'
import tseslint from 'typescript-eslint'

export default [
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  ...pluginVue.configs['flat/recommended'],
  {
    files: ['**/*.{js,mjs,cjs,ts,vue}']
  },
  {
    files: ['**/*.js'],
    languageOptions: { sourceType: 'commonjs' }
  },
  {
    languageOptions: { globals: globals.browser }
  },
  {
    files: ['**/*.vue'], languageOptions: { parserOptions: { parser: tseslint.parser } }
  },
  {
    files: ['**/*.js', '**/*.vue', '**/*.ts'],
    ignores: ['node_modules/**', 'public/**', 'vendor/**', 'dist/**'],
    plugins: {
      'simple-import-sort': simpleImportSort,
      'unused-imports': unusedImports,
    },
    rules: {
      'func-style': ['error', 'expression'],
      'vue/component-name-in-template-casing': [ 'error', 'kebab-case', {
        registeredComponentsOnly: true,
        ignores: [],
      },
      ],
      'vue/prefer-import-from-vue': 'error',
      'vue/no-import-compiler-macros': 'error',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'warn',
      'unused-imports/no-unused-vars': ['warn', {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
      ],
      'simple-import-sort/imports': 'error',
      'simple-import-sort/exports': 'error',
      'no-undef': 'error',
      'no-var': 'error',
      'vue/max-attributes-per-line': ['error', {
        singleline: { max: 3 },
        multiline: { max: 1 },
      },
      ],
      'vue/max-len': [ 'error', {
        code: 150,
        template: 120,
        tabWidth: 2,
        comments: 120,
        ignorePattern: '^import\\s.+\\sfrom\\s.+;$',
        ignoreComments: true,
        ignoreTrailingComments: false,
        ignoreUrls: true,
        ignoreStrings: false,
        ignoreTemplateLiterals: false,
        ignoreRegExpLiterals: true,
        ignoreHTMLAttributeValues: false,
        ignoreHTMLTextContents: false,
      },
      ],
      'vue/multi-word-component-names': 'off',
      semi: ['error', 'never'],
      indent: ['error', 2],
      quotes: ['error', 'single'],
    },
    languageOptions: {
      globals: {
        axios: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
        jest: 'readonly',
        describe: 'readonly',
        it: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly',
        expect: 'readonly',
        module: 'readonly',
        global: 'readonly',
      },
    },
  },
]
