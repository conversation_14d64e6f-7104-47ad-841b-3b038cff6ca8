package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	port := "8080"
	if len(os.Args) > 1 {
		port = os.Args[1]
	}

	// Set gin mode
	gin.SetMode(gin.DebugMode)

	// Create router
	r := gin.Default()

	// Setup CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "Accept"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(config))

	// Setup routes
	setupRoutes(r)

	// Start server
	srv := &http.Server{
		Addr:    ":" + port,
		Handler: r,
	}

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		fmt.Printf("🚀 Mock LLM Server starting on port %s\n", port)
		fmt.Printf("📡 Health check: http://localhost:%s/api/v1/health\n", port)
		fmt.Printf("📁 File upload: http://localhost:%s/api/v1/files/upload\n", port)
		fmt.Printf("❓ Simple questions: http://localhost:%s/api/v1/questions/simple\n", port)
		fmt.Printf("🧠 Advanced questions: http://localhost:%s/api/v1/questions/advanced\n", port)
		fmt.Printf("⚡ Press Ctrl+C to stop\n")

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	<-quit
	fmt.Println("\n🛑 Mock LLM Server shutting down...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	fmt.Println("Server exited")
}
