package main

import (
	"fmt"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// Types for the API responses
type APIResponse struct {
	Success    bool      `json:"success"`
	WorkflowID string    `json:"workflow_id,omitempty"`
	Timestamp  time.Time `json:"timestamp"`
	Error      string    `json:"error,omitempty"`
	Message    string    `json:"message,omitempty"`
	RequestID  string    `json:"request_id,omitempty"`
}

type ErrorResponse struct {
	Error     string                 `json:"error"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id"`
}

type FileUploadResponse struct {
	Success   bool      `json:"success"`
	FileID    string    `json:"file_id"`
	FileName  string    `json:"file_name"`
	Timestamp time.Time `json:"timestamp"`
}

type File struct {
	FileID     string    `json:"file_id"`
	FileName   string    `json:"file_name"`
	UploadTime time.Time `json:"upload_time"`
	Processed  bool      `json:"processed"`
}

type FilesResponse struct {
	Files []File `json:"files"`
}

type QuestionRequest struct {
	Question     string                 `json:"question"`
	SessionID    string                 `json:"session_id"`
	Language     string                 `json:"language,omitempty"`
	Config       map[string]interface{} `json:"config,omitempty"`
	WorkflowType string                 `json:"workflow_type,omitempty"`
}

type Agent struct {
	Status              string  `json:"status"`
	Progress            float64 `json:"progress"`
	Output              string  `json:"output,omitempty"`
	ExecutionTime       float64 `json:"execution_time,omitempty"`
	CurrentTask         string  `json:"current_task,omitempty"`
	EstimatedCompletion string  `json:"estimated_completion,omitempty"`
}

type Progress struct {
	CompletionPercentage   float64  `json:"completion_percentage"`
	ActiveAgents           []string `json:"active_agents"`
	CompletedSteps         []string `json:"completed_steps"`
	CurrentStep            string   `json:"current_step"`
	EstimatedRemainingTime float64  `json:"estimated_remaining_time"`
}

type Metrics struct {
	ElapsedTime        float64 `json:"elapsed_time"`
	EstimatedTotalTime float64 `json:"estimated_total_time"`
	CPUUsage           float64 `json:"cpu_usage"`
	MemoryUsage        float64 `json:"memory_usage"`
}

type RealTimeUpdates struct {
	WebSocketURL string `json:"websocket_url"`
}

type WorkflowStatus struct {
	WorkflowID      string           `json:"workflow_id"`
	Status          string           `json:"status"`
	CurrentPhase    string           `json:"current_phase"`
	FinalAnswer     string           `json:"final_answer"`
	Progress        Progress         `json:"progress"`
	Agents          map[string]Agent `json:"agents"`
	Metrics         Metrics          `json:"metrics"`
	RealTimeUpdates RealTimeUpdates  `json:"real_time_updates"`
}

type HealthComponent struct {
	Status           string  `json:"status"`
	ResponseTime     float64 `json:"response_time,omitempty"`
	LastCheck        string  `json:"last_check,omitempty"`
	Connections      int     `json:"connections,omitempty"`
	Size             string  `json:"size,omitempty"`
	BufferSize       int     `json:"buffer_size,omitempty"`
	LastFlush        string  `json:"last_flush,omitempty"`
	TrainingExamples int     `json:"training_examples,omitempty"`
	LastOptimization string  `json:"last_optimization,omitempty"`
}

type SystemInfo struct {
	Uptime                int64   `json:"uptime"`
	MemoryUsage           string  `json:"memory_usage"`
	CPUUsage              float64 `json:"cpu_usage"`
	ActiveWorkflows       int     `json:"active_workflows"`
	TotalQueriesProcessed int     `json:"total_queries_processed"`
}

type HealthResponse struct {
	Status      string                     `json:"status"`
	Timestamp   time.Time                  `json:"timestamp"`
	Version     string                     `json:"version"`
	Environment string                     `json:"environment"`
	Components  map[string]HealthComponent `json:"components"`
	SystemInfo  SystemInfo                 `json:"system_info"`
}

type WebSocketMessage struct {
	Type       string                 `json:"type"`
	WorkflowID string                 `json:"workflow_id"`
	Timestamp  time.Time              `json:"timestamp"`
	Data       map[string]interface{} `json:"data"`
}

// Storage for mock data
var (
	files     = make(map[string][]File)
	workflows = make(map[string]*WorkflowStatus)
	upgrader  = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for testing
		},
	}
)

// Helper functions for generating varied responses
func generateRandomResponse() string {
	responses := []string{
		"Based on comprehensive analysis of your question, I can provide you with a detailed and nuanced response that addresses multiple dimensions of your inquiry. The complexity of the topic requires us to examine several interconnected factors that influence the overall understanding. Through careful consideration of various perspectives and methodologies, we can arrive at a more complete picture that encompasses both theoretical frameworks and practical applications. This multifaceted approach ensures that we're not only addressing the immediate question but also providing context that enhances your overall comprehension of the subject matter.",

		"After conducting an extensive review of relevant literature and cross-referencing multiple authoritative sources, I've identified several key insights that directly relate to your question. The research reveals fascinating patterns and trends that have emerged over time, suggesting that this topic has evolved significantly due to various contributing factors. These developments have implications that extend beyond the immediate scope of your question, creating ripple effects that influence related areas of study and practice. Understanding these interconnections is crucial for developing a holistic perspective on the matter.",

		"Your question touches on a particularly interesting area that has garnered significant attention from researchers and practitioners alike. The evolving landscape of this field presents both opportunities and challenges that deserve careful examination. Recent developments have introduced new variables that must be considered when formulating a comprehensive response. The interplay between traditional approaches and emerging methodologies creates a dynamic environment where multiple solutions may be viable, depending on specific circumstances and objectives. This complexity requires us to remain flexible in our thinking while maintaining rigorous analytical standards.",

		"The depth and breadth of your inquiry warrant a thorough exploration that considers both historical context and contemporary perspectives. Through systematic analysis of available data and expert opinions, we can construct a response that balances theoretical understanding with practical implications. The multidisciplinary nature of this topic means that insights from various fields contribute to a more complete understanding. By synthesizing these diverse viewpoints, we can offer recommendations that are both evidence-based and contextually appropriate for your specific situation.",

		"This question opens up a fascinating area of exploration that intersects with multiple domains of knowledge and practice. The complexity inherent in this topic requires us to approach it from several angles, considering both quantitative data and qualitative insights that have emerged from ongoing research. The landscape continues to evolve rapidly, with new discoveries and innovations constantly reshaping our understanding. These developments create both opportunities for advancement and challenges that must be navigated carefully to achieve optimal outcomes.",
	}

	// Add some randomness to make responses feel more dynamic
	baseResponse := responses[rand.Intn(len(responses))]

	// Occasionally add additional context
	if rand.Float32() < 0.4 {
		additionalContext := []string{
			" Furthermore, it's important to note that emerging trends in this area suggest we may see significant changes in the coming years that could reshape our current understanding.",
			" Additionally, recent case studies have provided valuable insights that complement the theoretical framework, offering practical examples of successful implementation strategies.",
			" Moreover, the interdisciplinary nature of this topic means that developments in related fields often provide unexpected insights that enhance our overall comprehension.",
			" It's also worth considering the potential long-term implications of current approaches, as they may influence future developments in ways that are not immediately apparent.",
			" The global perspective on this matter reveals interesting variations in approach and outcomes that enrich our understanding of the underlying principles.",
		}
		baseResponse += additionalContext[rand.Intn(len(additionalContext))]
	}

	return baseResponse
}

func splitIntoWords(text string) []string {
	return strings.Fields(text)
}

func setupRoutes(r *gin.Engine) {
	api := r.Group("/api/v1")

	// File endpoints
	api.POST("/files/upload", handleFileUpload)
	api.GET("/files/:session_id", handleGetFiles)

	// Question endpoints
	api.POST("/questions/simple", handleSimpleQuestion)
	api.POST("/questions/advanced", handleAdvancedQuestion)

	// Workflow endpoints
	api.GET("/workflows/:workflow_id/status", handleWorkflowStatus)
	api.DELETE("/workflows/:workflow_id", handleCancelWorkflow)

	// Health endpoint
	api.GET("/health", handleHealth)

	// WebSocket endpoint
	api.GET("/ws/workflows/:workflow_id", handleWebSocket)
}

func generateRequestID() string {
	return "req_" + uuid.New().String()[:12]
}

func generateFileID() string {
	return "file_" + uuid.New().String()[:12]
}

func generateWorkflowID() string {
	return "wf_" + uuid.New().String()[:12]
}

func handleFileUpload(c *gin.Context) {
	sessionID := c.PostForm("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "Session ID is required",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "File is required",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}
	defer file.Close()

	fileName := c.PostForm("file_name")
	if fileName == "" {
		fileName = header.Filename
	}

	// Simulate file size validation
	if header.Size > 10*1024*1024 { // 10MB limit
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "validation_error",
			Message: "File size exceeds limit",
			Details: map[string]interface{}{
				"field":    "file",
				"code":     "FILE_SIZE_EXCEEDED",
				"max_size": 10485760,
			},
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	fileID := generateFileID()
	newFile := File{
		FileID:     fileID,
		FileName:   fileName,
		UploadTime: time.Now(),
		Processed:  rand.Float32() > 0.5, // Randomly set processed status
	}

	if files[sessionID] == nil {
		files[sessionID] = []File{}
	}
	files[sessionID] = append(files[sessionID], newFile)

	c.JSON(http.StatusOK, FileUploadResponse{
		Success:   true,
		FileID:    fileID,
		FileName:  fileName,
		Timestamp: time.Now(),
	})
}

func handleGetFiles(c *gin.Context) {
	sessionID := c.Param("session_id")
	sessionFiles := files[sessionID]

	if sessionFiles == nil {
		sessionFiles = []File{}
	}

	c.JSON(http.StatusOK, FilesResponse{
		Files: sessionFiles,
	})
}

func handleSimpleQuestion(c *gin.Context) {
	var req QuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "Invalid request format",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	if len(req.Question) < 10 || len(req.Question) > 1000 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "validation_error",
			Message: "Invalid question format",
			Details: map[string]interface{}{
				"field":      "question",
				"code":       "QUESTION_LENGTH_INVALID",
				"min_length": 10,
				"max_length": 1000,
			},
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	if req.SessionID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "Session ID is required",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	workflowID := generateWorkflowID()

	// Create and store workflow
	createMockWorkflow(workflowID, "simple")

	c.JSON(http.StatusOK, APIResponse{
		Success:    true,
		WorkflowID: workflowID,
		Timestamp:  time.Now(),
	})
}

func handleAdvancedQuestion(c *gin.Context) {
	var req QuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "Invalid request format",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	if len(req.Question) < 10 || len(req.Question) > 2000 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "validation_error",
			Message: "Invalid question format",
			Details: map[string]interface{}{
				"field":      "question",
				"code":       "QUESTION_LENGTH_INVALID",
				"min_length": 10,
				"max_length": 2000,
			},
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	if req.SessionID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "validation_error",
			Message:   "Session ID is required",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	workflowID := generateWorkflowID()

	// Create and store workflow
	createMockWorkflow(workflowID, "advanced")

	c.JSON(http.StatusOK, APIResponse{
		Success:    true,
		WorkflowID: workflowID,
		Timestamp:  time.Now(),
	})
}

func createMockWorkflow(workflowID, workflowType string) {
	agentOutputs := map[string][]string{
		"task_manager": {
			"Execution plan created with 5 comprehensive analysis steps",
			"Strategic workflow initiated with multi-phase approach",
			"Advanced processing pipeline established with 7 key checkpoints",
		},
		"researcher": {
			"Found 12 highly relevant academic sources and 8 industry reports",
			"Comprehensive literature review completed across 15 peer-reviewed papers",
			"Cross-referenced 23 authoritative sources with verified credibility scores",
		},
		"librarian": {
			"Analyzing research papers with advanced semantic categorization",
			"Processing academic databases with intelligent content extraction",
			"Synthesizing knowledge from multidisciplinary sources",
		},
		"data_processor": {
			"Data modeling and trend analysis with statistical significance testing",
			"Advanced analytics pipeline processing with machine learning insights",
			"Complex data correlation analysis revealing emerging patterns",
		},
		"writer": {
			"Waiting for specialist results to compose comprehensive response",
			"Preparing structured narrative with evidence-based conclusions",
			"Synthesizing findings into coherent analytical framework",
		},
	}

	agents := map[string]Agent{
		"task_manager": {
			Status:        "completed",
			Progress:      100.0,
			Output:        agentOutputs["task_manager"][rand.Intn(len(agentOutputs["task_manager"]))],
			ExecutionTime: 8.4 + rand.Float64()*5,
		},
		"researcher": {
			Status:        "completed",
			Progress:      100.0,
			Output:        agentOutputs["researcher"][rand.Intn(len(agentOutputs["researcher"]))],
			ExecutionTime: 45.2 + rand.Float64()*20,
		},
		"librarian": {
			Status:              "working",
			Progress:            80.0 + rand.Float64()*15,
			CurrentTask:         agentOutputs["librarian"][rand.Intn(len(agentOutputs["librarian"]))],
			EstimatedCompletion: time.Now().Add(time.Duration(1+rand.Intn(3)) * time.Minute).Format(time.RFC3339),
		},
		"data_processor": {
			Status:              "working",
			Progress:            45.0 + rand.Float64()*30,
			CurrentTask:         agentOutputs["data_processor"][rand.Intn(len(agentOutputs["data_processor"]))],
			EstimatedCompletion: time.Now().Add(time.Duration(2+rand.Intn(4)) * time.Minute).Format(time.RFC3339),
		},
		"writer": {
			Status:      "pending",
			Progress:    0.0,
			CurrentTask: agentOutputs["writer"][rand.Intn(len(agentOutputs["writer"]))],
		},
	}

	if workflowType == "simple" {
		// Simpler workflow for simple questions with varied outputs
		simpleOutputs := map[string][]string{
			"researcher": {
				"Researching core concepts with targeted analysis",
				"Conducting focused investigation on key topics",
				"Exploring fundamental principles and applications",
			},
			"writer": {
				"Waiting for research insights to formulate response",
				"Preparing concise summary of research findings",
				"Organizing information into structured answer",
			},
		}

		agents = map[string]Agent{
			"researcher": {
				Status:              "working",
				Progress:            75.0 + rand.Float64()*20,
				CurrentTask:         simpleOutputs["researcher"][rand.Intn(len(simpleOutputs["researcher"]))],
				EstimatedCompletion: time.Now().Add(time.Duration(30+rand.Intn(90)) * time.Second).Format(time.RFC3339),
			},
			"writer": {
				Status:      "pending",
				Progress:    0.0,
				CurrentTask: simpleOutputs["writer"][rand.Intn(len(simpleOutputs["writer"]))],
			},
		}
	}

	workflow := &WorkflowStatus{
		WorkflowID:   workflowID,
		Status:       "executing",
		CurrentPhase: "analysis",
		FinalAnswer:  "",
		Progress: Progress{
			CompletionPercentage:   65.0 + rand.Float64()*20,
			ActiveAgents:           []string{"data_processor", "librarian"},
			CompletedSteps:         []string{"planning", "research"},
			CurrentStep:            "data_analysis",
			EstimatedRemainingTime: 69.3 + rand.Float64()*40,
		},
		Agents: agents,
		Metrics: Metrics{
			ElapsedTime:        89.2 + rand.Float64()*30,
			EstimatedTotalTime: 158.5 + rand.Float64()*60,
			CPUUsage:           45.2 + rand.Float64()*20,
			MemoryUsage:        67.8 + rand.Float64()*15,
		},
		RealTimeUpdates: RealTimeUpdates{
			WebSocketURL: fmt.Sprintf("ws://localhost:8080/api/v1/ws/workflows/%s", workflowID),
		},
	}

	workflows[workflowID] = workflow

	// Simulate workflow progression
	go simulateWorkflowProgress(workflowID)
}

func simulateWorkflowProgress(workflowID string) {
	time.Sleep(5 * time.Second) // Initial delay

	workflow := workflows[workflowID]
	if workflow == nil {
		return
	}

	// Simulate completion
	workflow.Status = "completed"
	workflow.CurrentPhase = "completed"
	workflow.FinalAnswer = generateRandomResponse()
	workflow.Progress.CompletionPercentage = 100.0
	workflow.Progress.CurrentStep = "completed"
	workflow.Progress.EstimatedRemainingTime = 0

	// Update all agents to completed
	for name, agent := range workflow.Agents {
		agent.Status = "completed"
		agent.Progress = 100.0
		agent.CurrentTask = ""
		agent.EstimatedCompletion = ""
		if agent.Output == "" {
			agent.Output = fmt.Sprintf("%s completed successfully", name)
		}
		workflow.Agents[name] = agent
	}

	workflow.Metrics.ElapsedTime = workflow.Metrics.EstimatedTotalTime
	workflows[workflowID] = workflow
}

func handleWorkflowStatus(c *gin.Context) {
	workflowID := c.Param("workflow_id")

	workflow := workflows[workflowID]
	if workflow == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:     "not_found",
			Message:   "Workflow not found",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

func handleCancelWorkflow(c *gin.Context) {
	workflowID := c.Param("workflow_id")

	workflow := workflows[workflowID]
	if workflow == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:     "not_found",
			Message:   "Workflow not found",
			Timestamp: time.Now(),
			RequestID: generateRequestID(),
		})
		return
	}

	workflow.Status = "cancelled"
	workflows[workflowID] = workflow

	response := map[string]interface{}{
		"success":           true,
		"workflow_id":       workflowID,
		"status":            "cancelled",
		"message":           "Workflow cancelled successfully",
		"cancellation_time": time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

func handleHealth(c *gin.Context) {
	response := HealthResponse{
		Status:      "healthy",
		Timestamp:   time.Now(),
		Version:     "2.0.0",
		Environment: "development",
		Components: map[string]HealthComponent{
			"multi_agent_system": {
				Status:       "healthy",
				ResponseTime: 45.2,
				LastCheck:    time.Now().Add(-5 * time.Second).Format(time.RFC3339),
			},
			"vector_database": {
				Status:       "healthy",
				Connections:  15,
				ResponseTime: 12.3,
				Size:         "2.3GB",
			},
			"metrics_collector": {
				Status:     "healthy",
				BufferSize: 245,
				LastFlush:  time.Now().Add(-30 * time.Second).Format(time.RFC3339),
			},
			"dspy_optimizer": {
				Status:           "healthy",
				TrainingExamples: 567,
				LastOptimization: "2025-05-24T10:30:00Z",
			},
		},
		SystemInfo: SystemInfo{
			Uptime:                86400,
			MemoryUsage:           "2.1GB",
			CPUUsage:              45.2,
			ActiveWorkflows:       len(workflows),
			TotalQueriesProcessed: 12547,
		},
	}

	c.JSON(http.StatusOK, response)
}

func handleWebSocket(c *gin.Context) {
	workflowID := c.Param("workflow_id")

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		fmt.Printf("WebSocket upgrade error: %v\n", err)
		return
	}
	defer conn.Close()

	fmt.Printf("WebSocket connected for workflow: %s\n", workflowID)

	// Send initial status
	workflow := workflows[workflowID]
	if workflow != nil {
		sendStatusUpdate(conn, workflowID, workflow)

		// Simulate real-time updates
		go simulateRealTimeUpdates(conn, workflowID)
	}

	// Keep connection alive and handle incoming messages
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			fmt.Printf("WebSocket read error: %v\n", err)
			break
		}
	}
}

func sendStatusUpdate(conn *websocket.Conn, workflowID string, workflow *WorkflowStatus) {
	message := WebSocketMessage{
		Type:       "status_update",
		WorkflowID: workflowID,
		Timestamp:  time.Now(),
		Data: map[string]interface{}{
			"current_phase":        workflow.CurrentPhase,
			"progress":             workflow.Progress.CompletionPercentage,
			"active_agents":        workflow.Progress.ActiveAgents,
			"latest_output":        "Processing your request...",
			"estimated_completion": time.Now().Add(2 * time.Minute).Format(time.RFC3339),
		},
	}

	if err := conn.WriteJSON(message); err != nil {
		fmt.Printf("WebSocket write error: %v\n", err)
	}
}

func simulateRealTimeUpdates(conn *websocket.Conn, workflowID string) {
	// Simulate agent updates
	time.Sleep(1 * time.Second)

	agentOutputs := []string{
		"Research phase completed with 12 high-quality academic sources and industry insights",
		"Comprehensive analysis finished with cross-referenced data from multiple domains",
		"Investigation concluded with validated findings from authoritative references",
		"Research synthesis completed with evidence-based conclusions and recommendations",
	}

	agentUpdate := WebSocketMessage{
		Type:       "agent_update",
		WorkflowID: workflowID,
		Timestamp:  time.Now(),
		Data: map[string]interface{}{
			"agent_id":       "researcher",
			"status":         "completed",
			"progress":       100.0,
			"output":         agentOutputs[rand.Intn(len(agentOutputs))],
			"execution_time": 45.2 + rand.Float64()*20,
			"next_agent":     "data_processor",
		},
	}

	if err := conn.WriteJSON(agentUpdate); err != nil {
		return
	}

	// Simulate final result streaming
	time.Sleep(1 * time.Second)

	finalResult := generateRandomResponse()
	words := splitIntoWords(finalResult)

	for _, word := range words {
		resultChunk := WebSocketMessage{
			Type:       "final_result",
			WorkflowID: workflowID,
			Timestamp:  time.Now(),
			Data: map[string]interface{}{
				"chunk": word + " ",
			},
		}

		if err := conn.WriteJSON(resultChunk); err != nil {
			return
		}
		time.Sleep(time.Duration(50+rand.Intn(100)) * time.Millisecond) // Variable delay for more realistic streaming
	}

	// Send workflow complete
	time.Sleep(1 * time.Second)

	complete := WebSocketMessage{
		Type:       "workflow_complete",
		WorkflowID: workflowID,
		Timestamp:  time.Now(),
		Data: map[string]interface{}{
			"status":         "completed",
			"final_answer":   finalResult,
			"execution_time": 158.47,
			"quality_score":  0.91,
		},
	}

	conn.WriteJSON(complete)
}
