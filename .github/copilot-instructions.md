# Go Coding Style Guidelines

## Core Principles

### Idiomatic Go Patterns
- Follow standard Go conventions and idioms
- Use Go's built-in types and patterns (channels, goroutines, interfaces)
- Prefer composition over inheritance
- Use meaningful package names that describe functionality

### Error Handling
- Always handle errors explicitly with explicit returns
- Use the standard Go error pattern: `if err != nil { return err }`
- Don't ignore errors - handle them appropriately
- Return errors as the last return value
- Use early returns to avoid deep nesting

### Code Formatting
- All code must be formatted according to `gofmt` standards
- Use `goimports` for import organization
- Keep line length reasonable (under 100 characters when possible)

### Comments
- Use comments only for hard to understand or complex parts
- Avoid obvious comments that restate the code
- Focus on explaining WHY, not WHAT
- Use proper Go doc comment format

## Structure and Complexity

### Early Returns
- Use early returns wherever possible to reduce nesting
- Validate inputs at the beginning of functions, only in handlers
- Handle error cases first, then the happy path

### Avoid Nesting
- Minimize nested statements and control structures
- Prefer guard clauses over nested if statements
- Use early returns to flatten code structure
- Extract complex conditions into well-named boolean variables

### Function Design
- Keep functions small and focused on a single responsibility
- Split large functions into smaller, more focused ones
- If a function reduces cognitive complexity by being split, do it
- Make code as compact as possible without sacrificing readability
- Use descriptive function names that explain their purpose

### Variable Naming
- Use short, clear variable names for small scopes
- Use longer, descriptive names for larger scopes
- Follow Go naming conventions (camelCase for unexported, PascalCase for exported)

## Interface-Based Architecture

### Component Design Pattern
- Create separate interfaces and structs for handlers, services, repositories, and similar components
- Use PascalCase for exported interfaces (e.g., `AuthHandler`, `UserService`, `UserRepository`)
- Use camelCase for unexported struct implementations (e.g., `authHandler`, `userService`, `userRepository`)
- Define interfaces that focus on behavior, not implementation details

### Interface Naming
- Use descriptive names that clearly indicate the component's purpose
- Prefer noun-based names for interfaces (e.g., `Handler`, `Service`, `Repository`)
- Add domain-specific prefixes when needed (e.g., `AuthHandler`, `UserService`)

### Struct Implementation
- Keep struct fields unexported unless they need to be accessed externally
- Use dependency injection through constructor functions
- Group related dependencies together in struct fields

### Constructor Functions
- Use `New` prefix for constructor functions (e.g., `NewAuthHandler`, `NewUserService`)
- Return the interface type, not the concrete struct type
- Accept interface dependencies, not concrete types

### Function Ordering
- Order methods within interfaces and structs consistently:
  1. **Exported public methods** - Interface methods and public functionality
  2. **Non-exported private methods** - Internal business logic and operations
  3. **Non-exported helper functions** - Utility functions for error handling and data processing

## Examples

### Interface-Based Component Design

#### Handler Pattern
```go
// Exported interface
type AuthHandler interface {
    Login(w http.ResponseWriter, r *http.Request)
    Logout(w http.ResponseWriter, r *http.Request)
    Register(w http.ResponseWriter, r *http.Request)
}

// Unexported implementation
type authHandler struct {
    authService AuthService
    logger      Logger
}

// Constructor returns interface type
func NewAuthHandler(authService AuthService, logger Logger) AuthHandler {
    return &authHandler{
        authService: authService,
        logger:      logger,
    }
}

func (h *authHandler) Login(w http.ResponseWriter, r *http.Request) {
    // Implementation here
}
```

#### Service Pattern
```go
// Exported interface
type UserService interface {
    CreateUser(ctx context.Context, user *User) error
    GetUserByID(ctx context.Context, id string) (*User, error)
    UpdateUser(ctx context.Context, user *User) error
    DeleteUser(ctx context.Context, id string) error
}

// Unexported implementation
type userService struct {
    userRepo UserRepository
    logger   Logger
}

// Constructor accepts interfaces, returns interface
func NewUserService(userRepo UserRepository, logger Logger) UserService {
    return &userService{
        userRepo: userRepo,
        logger:   logger,
    }
}

func (s *userService) CreateUser(ctx context.Context, user *User) error {
    if user.Email == "" {
        return ErrInvalidEmail
    }

    return s.userRepo.Create(ctx, user)
}

func (s *userService) GetUserByID(ctx context.Context, id string) (*User, error) {
    if id == "" {
        return nil, ErrInvalidUserID
    }

    return s.userRepo.GetByID(ctx, id)
}

func (s *userService) validateUser(user *User) error {
    if user.Email == "" {
        return ErrInvalidEmail
    }
    if user.Name == "" {
        return ErrInvalidName
    }
    return nil
}
```

#### Repository Pattern
```go
// Exported interface
type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByID(ctx context.Context, id string) (*User, error)
    GetByEmail(ctx context.Context, email string) (*User, error)
    Update(ctx context.Context, user *User) error
    Delete(ctx context.Context, id string) error
}

// Unexported implementation
type userRepository struct {
    db *sql.DB
}

func NewUserRepository(db *sql.DB) UserRepository {
    return &userRepository{
        db: db,
    }
}

func (r *userRepository) Create(ctx context.Context, user *User) error {
    query := r.buildInsertQuery()
    _, err := r.db.ExecContext(ctx, query, user.ID, user.Email, user.Name)
    if err != nil {
        return r.handleInsertError(err)
    }
    return nil
}

func (r *userRepository) GetByID(ctx context.Context, id string) (*User, error) {
    query := r.buildSelectQuery("id")
    var user User
    err := r.db.QueryRowContext(ctx, query, id).Scan(&user.ID, &user.Email, &user.Name)
    if err != nil {
        return nil, r.handleQueryError(err, "get by id")
    }
    return &user, nil
}

func (r *userRepository) buildInsertQuery() string {
    return `INSERT INTO users (id, email, name) VALUES ($1, $2, $3)`
}

func (r *userRepository) buildSelectQuery(field string) string {
    return fmt.Sprintf(`SELECT id, email, name FROM users WHERE %s = $1`, field)
}

func (r *userRepository) handleQueryError(err error, operation string) error {
    if err == sql.ErrNoRows {
        return ErrUserNotFound
    }
    return fmt.Errorf("failed to %s: %w", operation, err)
}
```

### Preferred Pattern
```go
func ProcessUser(userID string) error {
    if userID == "" {
        return ErrInvalidUserID
    }

    user, err := fetchUser(userID)
    if err != nil {
        return fmt.Errorf("failed to fetch user: %w", err)
    }

    if !user.IsActive {
        return ErrUserInactive
    }

    return processActiveUser(user)
}
```

### Avoid
```go
func ProcessUser(userID string) error {
    if userID != "" {
        user, err := fetchUser(userID)
        if err == nil {
            if user.IsActive {
                // deeply nested logic here
                return processActiveUser(user)
            } else {
                return ErrUserInactive
            }
        } else {
            return fmt.Errorf("failed to fetch user: %w", err)
        }
    } else {
        return ErrInvalidUserID
    }
}
```
