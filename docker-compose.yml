services:

  postgres:
    build:
      context: postgres
      dockerfile: Dockerfile
      tags:
        - registry.backfielddigital.com/dev/aura/postgres:latest
    image: registry.backfielddigital.com/dev/aura/postgres:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-postgres
    command: >
      -c shared_buffers=4GB
      -c effective_cache_size=8GB
      -c work_mem=1GB
      -c maintenance_work_mem=2GB
      -c random_page_cost=2
      -c max_parallel_workers=8
      -c max_parallel_workers_per_gather=6
      -c max_locks_per_transaction=512
    restart: unless-stopped
    volumes:
      - ./data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    networks:
      - default
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U admin -d aura -h localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  ui:
    image: registry.backfielddigital.com/dev/aura/ui:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-ui
    build:
      context: ui
      dockerfile: Dockerfile
      args:
        - NPM_AUTH_TOKEN=${NPM_AUTH_TOKEN}
      tags:
        - registry.backfielddigital.com/dev/aura/ui:latest
    working_dir: /app
    env_file:
      - .env
    networks:
      - default
    command: ["npm", "start"]

  api:
    image: registry.backfielddigital.com/dev/aura/api:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-api
    build:
      context: api
      dockerfile: Dockerfile
      tags:
        - registry.backfielddigital.com/dev/aura/api:latest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_NAME}.tls=true"
      - "traefik.http.routers.${APP_NAME}.tls.certresolver=local-env"
      - "traefik.http.routers.${APP_NAME}.rule=Host(`api.${APP_NAME}.${BASE_DOMAIN:-backfielddigital.com}`)"
      - "traefik.http.routers.${APP_NAME}.entrypoints=websecure"
      - "traefik.http.services.${APP_NAME}.loadbalancer.server.port=${API_PORT:-3000}"
    working_dir: /app
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - .env
    networks:
      - default
      - ingress-backend
    command: ["./bin/${APP_NAME}"]

networks:
  default:
    name: ${COMPOSE_PROJECT_NAME}-network
    driver: bridge

  ingress-backend:
    external: true
