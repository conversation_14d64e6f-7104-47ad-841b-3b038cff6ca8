#!/usr/bin/env python3
"""
Test script for Server-Sent Events (SSE) real-time tool monitoring.

Tests the new SSE-based architecture for real-time tool updates
during question processing.
"""

import asyncio
import aiohttp
import json
import sys
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_KEY = "dev-api-key-12345"

class SSEMonitor:
    """Monitor SSE events for real-time tool updates."""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.events_received = []
        self.start_time = None
        
    async def connect_and_monitor(self):
        """Connect to SSE endpoint and monitor events."""
        sse_url = f"{BASE_URL}/api/v1/sse/sessions/{self.session_id}"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache"
        }
        
        print(f"🔗 Connecting to SSE endpoint: {sse_url}")
        self.start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(sse_url, headers=headers) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return
                        
                    print(f"✅ SSE connected! Monitoring events...")
                    print("=" * 60)
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]  # Remove 'data: ' prefix
                            try:
                                event_data = json.loads(data_str)
                                await self.handle_event(event_data)
                            except json.JSONDecodeError as e:
                                print(f"⚠️  Invalid JSON in SSE data: {e}")
                                
        except aiohttp.ClientError as e:
            print(f"❌ SSE connection error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    async def handle_event(self, event: dict):
        """Handle incoming SSE event."""
        current_time = datetime.now()
        elapsed = (current_time - self.start_time).total_seconds()
        
        event_type = event.get('type', 'unknown')
        timestamp = event.get('timestamp', '')
        data = event.get('data', {})
        
        self.events_received.append({
            'type': event_type,
            'elapsed': elapsed,
            'timestamp': timestamp,
            'data': data
        })
        
        # Format and display event
        if event_type == 'connection_established':
            print(f"🔗 [{elapsed:6.2f}s] Connection established")
            print(f"    Message: {data.get('message', '')}")
            
        elif event_type == 'tool_status':
            tool_name = data.get('tool_name', 'unknown')
            status = data.get('status', 'unknown')
            message = data.get('message', '')
            
            status_emoji = {
                'running': '🔄',
                'completed': '✅',
                'failed': '❌'
            }.get(status, '❓')
            
            print(f"{status_emoji} [{elapsed:6.2f}s] {tool_name} - {status}")
            print(f"    {message}")
            
        elif event_type == 'question_complete':
            print(f"🎉 [{elapsed:6.2f}s] Question processing complete!")
            final_answer = data.get('final_answer', '')
            print(f"    Answer: {final_answer[:100]}...")
            print("=" * 60)
            print("✅ Monitoring complete!")
            return True  # Signal completion
            
        elif event_type == 'error':
            print(f"💥 [{elapsed:6.2f}s] Error occurred!")
            print(f"    Error: {data.get('error_message', '')}")
            return True  # Signal completion
            
        elif event_type == 'ping':
            print(f"💓 [{elapsed:6.2f}s] Keepalive ping")
            
        else:
            print(f"❓ [{elapsed:6.2f}s] Unknown event: {event_type}")
            print(f"    Data: {data}")
        
        return False  # Continue monitoring

async def submit_question(session_id: str, question: str):
    """Submit question for processing."""
    url = f"{BASE_URL}/api/v1/questions/simple"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "question": question,
        "session_id": session_id
    }
    
    print(f"📤 Submitting question to: {url}")
    print(f"    Session ID: {session_id}")
    print(f"    Question: {question}")
    print()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Question submitted successfully!")
                    print(f"    SSE Endpoint: {result.get('sse_endpoint', '')}")
                    print()
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Question submission failed: {response.status}")
                    print(f"    Error: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

async def test_sse_monitoring():
    """Test SSE monitoring with a sample question."""
    print("🧪 Testing SSE Real-Time Tool Monitoring")
    print("=" * 60)
    
    # Generate unique session ID
    session_id = f"test-session-{uuid.uuid4().hex[:8]}"
    
    # Create monitor
    monitor = SSEMonitor(session_id)
    
    # Start monitoring in background
    monitor_task = asyncio.create_task(monitor.connect_and_monitor())
    
    # Wait a moment for connection to establish
    await asyncio.sleep(1)
    
    # Submit question
    test_questions = [
        "What are the latest developments in renewable energy in 2024?",
        "What is 2+2?",  # Simple question to test different behavior
        "How does artificial intelligence impact healthcare?"
    ]
    
    question = test_questions[0]  # Use first question
    success = await submit_question(session_id, question)
    
    if not success:
        monitor_task.cancel()
        return
    
    # Wait for monitoring to complete (or timeout)
    try:
        await asyncio.wait_for(monitor_task, timeout=180.0)  # 3 minute timeout
    except asyncio.TimeoutError:
        print("⏰ Monitoring timed out after 3 minutes")
        monitor_task.cancel()
    
    # Print summary
    print("\n📊 Event Summary:")
    print(f"    Total events received: {len(monitor.events_received)}")
    
    tool_events = [e for e in monitor.events_received if e['type'] == 'tool_status']
    if tool_events:
        print(f"    Tool status updates: {len(tool_events)}")
        tool_names = set(e['data'].get('tool_name', '') for e in tool_events)
        print(f"    Tools used: {', '.join(tool_names)}")
    
    completion_events = [e for e in monitor.events_received if e['type'] == 'question_complete']
    if completion_events:
        total_time = completion_events[0]['elapsed']
        print(f"    Total processing time: {total_time:.2f} seconds")
    
    # Check for real-time delivery
    if len(tool_events) > 1:
        time_gaps = []
        for i in range(1, len(tool_events)):
            gap = tool_events[i]['elapsed'] - tool_events[i-1]['elapsed']
            time_gaps.append(gap)
        
        avg_gap = sum(time_gaps) / len(time_gaps)
        print(f"    Average time between tool updates: {avg_gap:.2f} seconds")
        
        if avg_gap < 10:  # If updates come within 10 seconds of each other
            print("    ✅ Real-time delivery confirmed!")
        else:
            print("    ⚠️  Updates may be buffered")

async def main():
    """Main test function."""
    if len(sys.argv) > 1:
        question = sys.argv[1]
        session_id = f"custom-session-{uuid.uuid4().hex[:8]}"
        
        print(f"🧪 Testing with custom question: {question}")
        print("=" * 60)
        
        monitor = SSEMonitor(session_id)
        monitor_task = asyncio.create_task(monitor.connect_and_monitor())
        
        await asyncio.sleep(1)
        await submit_question(session_id, question)
        
        try:
            await asyncio.wait_for(monitor_task, timeout=180.0)
        except asyncio.TimeoutError:
            print("⏰ Monitoring timed out")
            monitor_task.cancel()
    else:
        await test_sse_monitoring()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1) 