#!/bin/bash
set -e

# DSPy Multi-Agent System API Docker Entrypoint
echo "🚀 Starting DSPy Multi-Agent System API Container"
echo "=================================================="

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for $service_name at $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "⚠️  $service_name not available after $max_attempts attempts"
    return 1
}

# Set default environment variables if not provided
export API_KEY="${API_KEY:-dev-api-key-12345}"
export REDIS_HOST="${REDIS_HOST:-redis}"
export REDIS_PORT="${REDIS_PORT:-6379}"
export REDIS_DB="${REDIS_DB:-0}"

# API Configuration
export API_HOST="${API_HOST:-0.0.0.0}"
export API_PORT="${API_PORT:-8000}"
export LOG_LEVEL="${LOG_LEVEL:-info}"
export WORKERS="${WORKERS:-1}"

# File upload configuration
export MAX_FILE_SIZE="${MAX_FILE_SIZE:-10485760}"
export UPLOAD_DIR="${UPLOAD_DIR:-./data/uploads}"

echo "🔧 Configuration:"
echo "   API Host: $API_HOST"
echo "   API Port: $API_PORT"
echo "   Log Level: $LOG_LEVEL"
echo "   Workers: $WORKERS"
echo "   Redis Host: $REDIS_HOST"
echo "   Redis Port: $REDIS_PORT"
echo "   API Key: ***${API_KEY: -4}"

# Create required directories
echo "📁 Creating required directories..."
mkdir -p /app/data /app/data/uploads /app/data/chroma_db /app/monitoring /app/logs /app/cache /app/checkpoints

# Wait for Redis if configured (optional)
if [ "$REDIS_HOST" != "localhost" ] && [ "$REDIS_HOST" != "127.0.0.1" ]; then
    if command -v nc >/dev/null 2>&1; then
        wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis" || echo "⚠️  Continuing without Redis (will use SQLite fallback)"
    else
        echo "⚠️  netcat not available, skipping Redis connectivity check"
    fi
fi

# Validate required environment variables for production
if [ "$API_KEY" = "dev-api-key-12345" ]; then
    echo "⚠️  WARNING: Using default development API key!"
    echo "   Set API_KEY environment variable for production"
fi

# Check if OpenAI API key is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  WARNING: OPENAI_API_KEY not set!"
    echo "   The application may not function properly without it"
fi

# Run database migrations or setup if needed
echo "🗄️  Initializing database..."
python -c "
import asyncio
import sys
sys.path.insert(0, '/app')
from src.api.utils.async_session_storage import get_async_session_storage

async def init_db():
    try:
        storage = await get_async_session_storage()
        print('✅ Database initialized successfully')
    except Exception as e:
        print(f'⚠️  Database initialization warning: {e}')

asyncio.run(init_db())
"

echo "🎯 Starting application..."
echo "=================================================="

# Execute the command
exec "$@"
