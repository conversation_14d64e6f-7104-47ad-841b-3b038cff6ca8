# =============================================================================
# API Configuration
# =============================================================================
API_KEY=
API_PORT=8889
LOG_LEVEL=info
WORKERS=1

# =============================================================================
# AI Service API Keys
# =============================================================================
# OpenAI API Key
OPENAI_API_KEY=********************************************************

# Serper API Key (Optional - for enhanced web search)
SERPER_API_KEY=95df768820e787e50423168ddc98b9f3784a7a52

# =============================================================================
# Redis Configuration (Optional - SQLite fallback available)
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# Vector Database Configuration
# =============================================================================
# Choose vector database: chroma (default) or qdrant (production)
VECTOR_DB_TYPE=chroma

# ChromaDB Configuration
VECTOR_DB_IMAGE=chromadb/chroma:latest
VECTOR_DB_PORT=8001
VECTOR_DB_INTERNAL_PORT=8000
CHROMA_HOST=vector-db
CHROMA_PORT=8000
CHROMA_TELEMETRY=true

# Qdrant Configuration
# To use Qdrant, set: VECTOR_DB_TYPE=qdrant and VECTOR_DB_IMAGE=qdrant/qdrant:latest
# VECTOR_DB_IMAGE=qdrant/qdrant:latest
# VECTOR_DB_PORT=6333
# VECTOR_DB_INTERNAL_PORT=6333
# QDRANT_HOST=vector-db
# QDRANT_PORT=6333
# QDRANT_API_KEY=your-qdrant-api-key
# QDRANT_LOG_LEVEL=INFO

# =============================================================================
# File Upload Configuration
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/data/uploads



# =============================================================================
# Development Configuration
# =============================================================================
DEBUG_MODE=false

# API_KEY=dev-api-key-12345
