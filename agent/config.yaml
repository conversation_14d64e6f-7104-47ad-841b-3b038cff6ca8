# Enhanced Flow Configuration
ENHANCED_FLOW: false  # Set to true to use enhanced coordination flow by default

# Enhanced Flow Specific Settings
enhanced_flow:
  enabled: false
  complexity_analysis: true
  parallel_processing: true
  advanced_routing: true
  real_time_monitoring: true
  dspy_integration: true
  
  # Alert thresholds for monitoring
  alert_thresholds:
    max_execution_time: 600  # seconds
    min_success_rate: 0.8
    max_error_rate: 0.2
  
  # DSPy Configuration
  dspy:
    model: "gpt-4.1-mini"
    temperature: 0.1
    max_tokens: 128000
    training_enabled: true
    optimization_enabled: true

# Automated Evaluation Configuration
evaluation:
  enabled: true
  
  # Decomposed metrics configuration
  metrics:
    relevance:
      enabled: true
      weight: 0.4
      minimum_threshold: 0.7
    
    coherence:
      enabled: true
      weight: 0.3
      minimum_threshold: 0.8
    
    instruction_following:
      enabled: true
      weight: 0.2
      minimum_threshold: 0.75
    
    tool_usage_efficiency:
      enabled: true
      weight: 0.1
      minimum_threshold: 0.6
  
  # Quality gates configuration
  quality_gates:
    enabled: true
    thresholds:
      excellent: 0.9
      good: 0.8
      acceptable: 0.7
      poor: 0.5
      minimum_composite: 0.7
    
    # Escalation actions
    escalation:
      automatic_reprocessing: false
      human_review_flagged: true
      marked_for_training: true
      notification_webhook: null
  
  # Evaluation frequency
  frequency:
    evaluate_all_responses: true
    batch_evaluation_interval: 24  # hours
    minimum_examples_for_batch: 50
  
  # DSPy evaluation model configuration
  dspy_evaluator:
    model: "gpt-4.1-mini"
    temperature: 0.1  # Alacsony legyen!
    max_tokens: 10000
    enable_caching: true

llm:
  provider: "openai"
  model_name: "gpt-4.1-mini"  # fallback model
  
  # Specialized models for different roles
  teacher_model: "gpt-4.1-mini"      # DSPy optimization teacher
  task_manager_model: "gpt-4.1-nano"  # Task coordination
  researcher_model: "gpt-4.1-mini"    # Web research
  librarian_model: "gpt-4.1-nano"     # Document retrieval
  data_processor_model: "gpt-4.1-mini" # Data analysis
  writer_model: "gpt-4.1-mini"        # Final synthesis
  
  api_key: "********************************************************"
  max_tokens: 128000
  temperature: 0.7
  timeout: 30
  
  # Cost optimization features
  enable_caching: true         
  use_batch_api: false         
  cache_system_prompts: true   
  cache_training_examples: true 

# API Keys for external services
api_keys:
  openai: "********************************************************"
  serper: "95df768820e787e50423168ddc98b9f3784a7a52"

tools:
  web_search_delay: 1.5
  max_search_results: 5
  document_max_length: 15000
  analysis_types: ["summary", "keywords", "sentiment_basic"]

optimization:
  enable_optimization: true
  max_bootstrapped_demos: 4
  max_labeled_demos: 16
  num_candidate_programs: 8
  num_threads: 4
  similarity_threshold: 0.7

system:
  debug_mode: false
  log_level: "INFO"
  max_concurrent_tasks: 5
  data_dir: "./data"
  cache_dir: "./cache"
  logs_dir: "./logs" 
