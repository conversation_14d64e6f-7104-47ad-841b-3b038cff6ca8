{"total_tests": 4, "passed_tests": 4, "success_rate": 100.0, "test_results": {"document_upload": {"success": true, "timestamp": "2025-07-09T22:29:50.010771", "details": {"file_id": "ef6dcd64-7123-4516-ae1b-3f3a50986409", "processing_enabled": true, "file_size": 1640}}, "document_retrieval": {"success": true, "timestamp": "2025-07-09T22:31:08.660277", "details": {"success_rate": 50.0, "successful_retrievals": 2, "total_questions": 4, "results": [{"question": "What is the magic number mentioned in the document?", "expected": "42", "found": true, "final_answer": "42", "sse_messages": 4, "sse_message_types": ["connection_established", "tool_status", "tool_status", "question_complete"]}, {"question": "What is the secret authentication code?", "expected": "ALPHA-BRAVO-CHARLIE-789", "found": null, "final_answer": null, "sse_messages": 5, "sse_message_types": ["connection_established", "tool_status", "tool_status", "tool_status", "tool_status"]}, {"question": "Where is the treasure located according to the document?", "expected": "coordinates 51.5074, -0.1278", "found": false, "final_answer": "The secret authentication code is ALPHA-BRAVO-CHARLIE-789.", "sse_messages": 2, "sse_message_types": ["connection_established", "question_complete"]}, {"question": "What is the master access credential password?", "expected": "SuperSecretPassword123!", "found": true, "final_answer": "The master access credential password is SuperSecretPassword123!.", "sse_messages": 4, "sse_message_types": ["connection_established", "tool_status", "tool_status", "question_complete"]}]}}, "cross_encoder_reranking": {"success": true, "timestamp": "2025-07-09T22:31:31.908865", "details": {"reranking_indicators": true, "cross_encoder_usage": false, "cpu_reranking": false, "enhanced_retriever": false, "question": "Find all the secret codes and passwords in the uploaded document"}}, "file_listing": {"success": true, "timestamp": "2025-07-09T22:31:31.913865", "details": {"files_count": 1, "files": [{"file_id": "ef6dcd64-7123-4516-ae1b-3f3a50986409", "file_name": "secret_document.txt", "upload_time": "2025-07-09T20:29:39.991280Z", "processed": false, "file_size": 1641}]}}}, "hidden_secrets": {"magic_number": "42", "secret_code": "ALPHA-BRAVO-CHARLIE-789", "hidden_formula": "E=mc²", "treasure_location": "The treasure is buried under the old oak tree at coordinates 51.5074, -0.1278", "password": "SuperSecretPassword123!", "api_endpoint": "https://api.secret-service.com/v2/classified", "phone_number": "******-0123-SECRET", "bitcoin_address": "**********************************"}, "session_id": "test-session-75e913cc-725c-411b-800f-45d51d98eb4b"}