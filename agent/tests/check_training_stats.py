#!/usr/bin/env python3
"""
Check training data collection status after main app execution.
"""

import asyncio
import sys
sys.path.append('.')
from src.main import get_training_stats

async def check_stats():
    print('📊 TRAINING DATA COLLECTION STATUS:')
    print('='*50)
    
    stats = await get_training_stats()
    print(f'   • Total examples: {stats.get("total_examples", 0)}')
    print(f'   • Average quality: {stats.get("avg_quality", 0.0):.2f}')
    print(f'   • Success rate: {stats.get("success_rate", 0.0):.1%}')
    print(f'   • Examples with feedback: {stats.get("examples_with_feedback", 0)}')
    print(f'   • Ready for optimization: {stats.get("ready_for_optimization", False)}')
    
    if stats.get('total_examples', 0) > 0:
        print('\n✅ SUCCESS: Training data collection is working!')
        print('   The system automatically collected data from the query.')
        remaining = 500 - stats.get('total_examples', 0)
        print(f'   Need {remaining} more examples to trigger auto-optimization.')
    else:
        print('\n❌ No training data found - check system integration.')

if __name__ == '__main__':
    asyncio.run(check_stats()) 