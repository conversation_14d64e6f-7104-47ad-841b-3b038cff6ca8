#!/usr/bin/env python3
"""
Demo: Production-Ready Continuous Learning with DSPy Optimization

This script demonstrates how the system collects training data during operation
and automatically triggers optimization when enough high-quality examples
are available - exactly what you asked about!

Features demonstrated:
- Query processing with automatic data collection
- Quality scoring and feedback integration
- Automatic optimization triggering at 500 examples
- Production-ready data flywheel pattern
"""

import asyncio
import time
import random
from typing import Dict, Any

from src.main import (
    MultiAgentSystem, 
    answer_question, 
    add_feedback, 
    get_training_stats
)
from src.optimization.dspy.training_data_collector import get_training_data_collector


async def simulate_production_usage():
    """
    Simulate production usage with automatic data collection.
    
    This shows exactly what you asked about:
    - Each query gets processed and data collected
    - User feedback is gathered (explicit/implicit)
    - When 500 examples are reached, optimization triggers automatically
    """
    
    print("🚀 PRODUCTION CONTINUOUS LEARNING DEMO")
    print("=" * 60)
    print("Demonstrating automatic training data collection and optimization...")
    print()
    
    # Sample questions that simulate real production usage
    production_questions = [
        "What is the golden ratio and how is it used in nature?",
        "Compare renewable energy sources vs fossil fuels", 
        "Explain how machine learning algorithms work",
        "What are the key differences between blockchain and traditional databases?",
        "How does photosynthesis work at the molecular level?",
        "Compare agile vs waterfall software development methodologies",
        "What are the main causes and effects of climate change?",
        "Explain the principles of quantum computing",
        "How does the human immune system fight viruses?",
        "What are the economic impacts of artificial intelligence?",
        "Compare democratic vs authoritarian political systems",
        "How do neural networks learn and make predictions?",
        "What are the benefits and risks of genetic engineering?",
        "Explain the theory of relativity in simple terms",
        "How does cryptocurrency mining work?",
        "What are the stages of human brain development?",
        "Compare different investment strategies for retirement",
        "How do vaccines work to prevent diseases?",
        "What are the principles of sustainable agriculture?",
        "Explain how search engines rank web pages"
    ]
    
    # Initialize system
    system = MultiAgentSystem()
    training_collector = get_training_data_collector()
    
    # Show initial training data status
    initial_stats = await get_training_stats()
    print(f"📊 INITIAL TRAINING DATA STATUS:")
    print(f"   • Total examples: {initial_stats.get('total_examples', 0)}")
    print(f"   • Average quality: {initial_stats.get('avg_quality', 0.0):.2f}")
    print(f"   • Success rate: {initial_stats.get('success_rate', 0.0):.1%}")
    print(f"   • Ready for optimization: {initial_stats.get('ready_for_optimization', False)}")
    print()
    
    # Simulate production queries with data collection
    queries_to_run = 10  # For demo - in production this would be ongoing
    
    print(f"🎯 SIMULATING {queries_to_run} PRODUCTION QUERIES...")
    print("   Each query will:")
    print("   • Process question with current optimization level")
    print("   • Collect training data automatically")
    print("   • Gather simulated user feedback")
    print("   • Check if optimization threshold is reached")
    print()
    
    for i in range(queries_to_run):
        question = random.choice(production_questions)
        
        print(f"🤖 Query {i+1}/{queries_to_run}: {question}")
        
        # Process query (this automatically collects training data)
        start_time = time.time()
        result = await answer_question(question)
        execution_time = time.time() - start_time
        
        if result.get('success'):
            print(f"   ✅ Success in {execution_time:.1f}s")
            print(f"   📚 Answer quality: {result.get('quality_metrics', {}).get('answer_quality_score', 0.0):.2f}")
            
            # Simulate user feedback (in production this would be real user input)
            if result.get('training_example_id'):
                # Simulate realistic feedback distribution
                feedback_scenarios = [
                    ('positive', 0.9, 'Excellent comprehensive answer'),
                    ('positive', 0.8, 'Good answer with helpful details'), 
                    ('positive', 0.7, 'Satisfactory response'),
                    ('quality_score', 0.6, 'Adequate but could be better'),
                    ('negative', 0.3, 'Missing important details'),
                ]
                
                # 80% positive feedback (realistic for good system)
                if random.random() < 0.8:
                    feedback = random.choice(feedback_scenarios[:3])  # Positive
                else:
                    feedback = random.choice(feedback_scenarios[3:])  # Neutral/negative
                
                await add_feedback(
                    example_id=result['training_example_id'],
                    feedback_type=feedback[0],
                    feedback_score=feedback[1],
                    comments=feedback[2]
                )
                print(f"   👍 Feedback: {feedback[0]} ({feedback[1]:.1f}) - {feedback[2]}")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
        
        # Show updated training data stats
        current_stats = await get_training_stats()
        print(f"   📊 Dataset: {current_stats.get('total_examples', 0)} examples, "
              f"{current_stats.get('avg_quality', 0.0):.2f} avg quality")
        
        # Check if optimization was triggered
        if current_stats.get('ready_for_optimization', False):
            print(f"   🎯 OPTIMIZATION READY! ({current_stats.get('total_examples', 0)} examples)")
        else:
            remaining = 500 - current_stats.get('total_examples', 0)
            print(f"   📈 Need {remaining} more examples for auto-optimization")
        
        print()
        
        # Small delay to simulate realistic usage pattern
        await asyncio.sleep(0.5)
    
    # Final status report
    print("📊 FINAL TRAINING DATA STATUS:")
    final_stats = await get_training_stats()
    print(f"   • Total examples collected: {final_stats.get('total_examples', 0)}")
    print(f"   • Average quality score: {final_stats.get('avg_quality', 0.0):.2f}")
    print(f"   • Success rate: {final_stats.get('success_rate', 0.0):.1%}")
    print(f"   • Examples with feedback: {final_stats.get('examples_with_feedback', 0)}")
    print(f"   • Feedback rate: {final_stats.get('feedback_rate', 0.0):.1%}")
    print(f"   • Ready for optimization: {final_stats.get('ready_for_optimization', False)}")
    print()
    
    if final_stats.get('ready_for_optimization', False):
        print("🚀 OPTIMIZATION THRESHOLD REACHED!")
        print("   The system will automatically:")
        print("   • Extract high-quality training examples")
        print("   • Run MIPROv2 optimization to improve prompts")
        print("   • Update agent instructions based on successful patterns")
        print("   • Improve query processing and answer quality")
    else:
        remaining = 500 - final_stats.get('total_examples', 0)
        print(f"📈 PROGRESS TRACKING:")
        print(f"   • {remaining} more examples needed for auto-optimization")
        print(f"   • Current collection rate: ~10 examples per demo run")
        print(f"   • Estimated optimization in ~{remaining//10} demo runs")
    
    print()
    print("✅ PRODUCTION CONTINUOUS LEARNING DEMO COMPLETED!")
    print()
    print("🎯 KEY TAKEAWAYS:")
    print("   1. Every query automatically collects training data")
    print("   2. User feedback improves data quality scoring")
    print("   3. System auto-optimizes when 500 examples are reached")
    print("   4. No manual intervention required - true production flywheel!")
    print()
    print("🔧 PRODUCTION DEPLOYMENT:")
    print("   • Deploy this system to production")
    print("   • Users naturally provide queries and feedback")
    print("   • System continuously learns and improves")
    print("   • Optimization happens automatically in background")
    print("   • Quality improves over time with zero manual work")


async def show_optimization_process():
    """Show what happens during the optimization process."""
    print("\n🎯 OPTIMIZATION PROCESS DETAILS:")
    print("=" * 50)
    print("When 500+ examples are collected, the system:")
    print()
    print("1. 📊 DATASET PREPARATION:")
    print("   • Filter examples with quality score ≥ 0.7")
    print("   • Prioritize examples with positive user feedback")
    print("   • Split 80% training / 20% validation")
    print()
    print("2. 🧠 MIPROv2 OPTIMIZATION:")
    print("   • Analyze successful query-response patterns")
    print("   • Generate optimized prompt instructions")
    print("   • Test multiple instruction candidates")
    print("   • Select best performing instructions")
    print()
    print("3. 🚀 SYSTEM IMPROVEMENT:")
    print("   • Update agent prompts with optimized instructions")
    print("   • Improve query processing patterns")
    print("   • Enhance answer quality and consistency")
    print("   • Measure performance improvement")
    print()
    print("4. 🔄 CONTINUOUS CYCLE:")
    print("   • Process continues with improved system")
    print("   • Collect new examples with better performance")
    print("   • Re-optimize periodically (every 24 hours)")
    print("   • System quality continuously improves")


if __name__ == "__main__":
    asyncio.run(simulate_production_usage())
    asyncio.run(show_optimization_process()) 