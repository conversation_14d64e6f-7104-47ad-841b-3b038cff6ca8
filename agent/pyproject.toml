[project]
name = "agent"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.11.18",
    "aioredis>=2.0.1",
    "aiosqlite>=0.20.0",
    "beautifulsoup4>=4.13.4",
    "chardet>=5.2.0",
    "chromadb>=0.5.23",
    "cohere>=5.15.0",
    "crewai>=0.120.1",
    "crewai-tools>=0.45.0",
    "dspy>=2.6.0",
    "duckduckgo-search>=8.0.2",
    "faiss-cpu>=1.11.0",
    "fastapi>=0.115.9",
    "httpx>=0.28.1",
    "langchain>=0.3.25",
    "langchain-cohere>=0.3.5",
    "langchain-community>=0.3.24",
    "langchain-core>=0.3.60",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.2.14",
    "langchain-text-splitters>=0.3.8",
    "langsmith>=0.3.42",
    "lxml>=5.4.0",
    "matplotlib>=3.10.3",
    "numpy>=2.2.6",
    "openai>=1.75.0",
    "pandas>=2.2.3",
    "pillow>=11.2.1",
    "psutil>=7.0.0",
    "pydantic>=2.11.4",
    "pydantic-core>=2.33.2",
    "pydantic-settings>=2.9.1",
    "pypdf>=5.5.0",
    "pypdfium2>=4.30.1",
    "pytest>=8.3.0",
    "pytest-asyncio>=0.26.0",
    "pytest-mock>=3.12.0",
    "python-dateutil>=2.9.0",
    "python-multipart>=0.0.20",
    "qdrant-client>=1.14.2",
    "redis>=6.1.0",
    "requests>=2.32.3",
    "seaborn>=0.13.2",
    "sentence-transformers>=4.1.0",
    "setuptools>=80.8.0",
    "tiktoken>=0.9.0",
    "uvicorn>=0.34.2",
    "websocket-client>=1.8.0",
    "websockets>=15.0.1",
    "wikipedia>=1.4.0",
]
