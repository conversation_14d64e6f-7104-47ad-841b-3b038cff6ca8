#!/usr/bin/env python3
"""
Startup script for the DSPy Multi-Agent System API.

This script starts the FastAPI server with proper configuration.
"""

import os
import sys
import uvicorn
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def setup_environment():
    """Setup environment variables and configuration."""
    # Set default API key if not provided
    if not os.getenv("API_KEY"):
        os.environ["API_KEY"] = "dev-api-key-12345"
        print("⚠️  Using default API key for development")
        print("   Set API_KEY environment variable for production")

    # Set default Redis configuration
    if not os.getenv("REDIS_HOST"):
        os.environ["REDIS_HOST"] = "localhost"
    if not os.getenv("REDIS_PORT"):
        os.environ["REDIS_PORT"] = "6379"
    if not os.getenv("REDIS_DB"):
        os.environ["REDIS_DB"] = "0"

    # Configure cache directories
    cache_dir = os.getenv("DSPY_CACHE_DIR", "./cache/dspy")
    os.environ["DSPY_CACHEDIR"] = cache_dir

    # Create data and cache directories
    data_dirs = [
        "data",
        "data/uploads",
        "data/chroma_db",
        "monitoring",
        "cache",
        "cache/dspy",
        "cache/transformers",
        "cache/huggingface",
        "cache/torch"
    ]

    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

    print(f"✅ Environment setup complete")
    print(f"   📁 DSPy cache directory: {cache_dir}")


def check_dependencies():
    """Check if required dependencies are available."""
    required_packages = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "aiofiles",
        "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies available")
    return True


def main():
    """Main startup function."""
    parser = argparse.ArgumentParser(description="DSPy Multi-Agent System API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--log-level", default="info", help="Log level")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies and exit")
    
    args = parser.parse_args()
    
    print("🚀 DSPy Multi-Agent System API")
    print("=" * 50)
    
    # Check dependencies if requested
    if args.check_deps:
        if check_dependencies():
            print("✅ All dependencies satisfied")
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start server due to missing dependencies")
        sys.exit(1)
    
    # Display configuration
    print(f"🌐 Server Configuration:")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Reload: {args.reload}")
    print(f"   Log Level: {args.log_level}")
    print(f"   Workers: {args.workers}")
    print(f"   API Key: {'***' + os.getenv('API_KEY', '')[-4:] if os.getenv('API_KEY') else 'Not set'}")
    
    print(f"\n📚 API Documentation:")
    print(f"   Swagger UI: http://{args.host}:{args.port}/docs")
    print(f"   ReDoc: http://{args.host}:{args.port}/redoc")
    print(f"   Health Check: http://{args.host}:{args.port}/api/v1/health")
    
    print(f"\n🔌 WebSocket:")
    print(f"   URL: ws://{args.host}:{args.port}/api/v1/ws/workflows/{{workflow_id}}")
    print(f"   Auth: ?api_key=your-api-key")
    
    print("\n" + "=" * 50)
    print("Starting server...")
    
    try:
        # Start the server
        uvicorn.run(
            "src.api.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level,
            workers=args.workers if not args.reload else 1,  # Reload doesn't work with multiple workers
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n\n⚠️  Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
