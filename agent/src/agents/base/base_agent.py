"""
Base agent implementation for the multi-agent system.

Integrates CrewAI 2025 and DSPy patterns with proper tool usage,
delegation, and optimization support.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Callable
from datetime import datetime

import dspy
from crewai import Agent
from crewai.tools import BaseTool

from ...core.interfaces.agent_interface import IAgent, AgentConfig, AgentType, AgentResult, AgentStatus
from ...core.interfaces.task_interface import <PERSON>ask, TaskSpec, TaskResult, TaskStatus, TaskType
from ...core.interfaces.tool_interface import ITool, ToolResult
from ...core.models.state_models import Workflow<PERSON>tate, AgentProgress, AgentStatus as ModelAgentStatus


class BaseMultiAgent(IAgent):
    """
    Base implementation for multi-agent system agents.
    
    Combines CrewAI Agent capabilities with DSPy intelligence
    and our custom interfaces.
    """
    
    def __init__(self, 
                 config: AgentConfig,
                 tools: Optional[List[ITool]] = None,
                 dspy_module: Optional[dspy.Module] = None):
        self._config = config
        self._tools = tools or []
        self._dspy_module = dspy_module
        self._agent_id = str(uuid.uuid4())
        self._status = ModelAgentStatus.IDLE
        self._current_task: Optional[ITask] = None
        
        # Set default agent type if not specified
        self._agent_type = getattr(config, 'agent_type', AgentType.SPECIALIST)
        
        self._progress = AgentProgress(
            agent_id=self._agent_id,
            agent_type=self._agent_type.value
        )
        
        # Initialize CrewAI agent (optional for some specialists)
        self._crewai_agent = None
        if hasattr(config, 'role') and hasattr(config, 'goal'):
            self._crewai_agent = self._create_crewai_agent()
        
        # Performance tracking
        self._execution_history: List[Dict[str, Any]] = []
        self._metrics: Dict[str, float] = {}
    
    @property
    def agent_id(self) -> str:
        return self._agent_id
    
    @property
    def config(self) -> AgentConfig:
        return self._config
    
    @property
    def capabilities(self) -> List[str]:
        """Return list of capabilities."""
        return getattr(self._config, 'capabilities', ['analysis', 'research'])
    
    @property
    def agent_type(self) -> AgentType:
        return self._agent_type
    
    @property
    def status(self) -> ModelAgentStatus:
        return self._status
    
    @property
    def current_task(self) -> Optional[ITask]:
        return self._current_task
    
    @property
    def progress(self) -> AgentProgress:
        return self._progress
    
    def _create_crewai_agent(self) -> Agent:
        """Create CrewAI agent with proper 2025 configuration."""
        # Convert our tools to CrewAI tools
        crewai_tools = [self._convert_tool_to_crewai(tool) for tool in self._tools]
        
        return Agent(
            role=getattr(self._config, 'role', 'Specialist Agent'),
            goal=getattr(self._config, 'goal', 'Process tasks effectively'),
            backstory=getattr(self._config, 'backstory', 'Advanced AI specialist'),
            tools=crewai_tools,
            allow_delegation=True,  # Enable hierarchical delegation
            verbose=True,
            memory=True,
            max_iter=self._config.max_iterations,
            max_execution_time=self._config.timeout_seconds,
            step_callback=self._step_callback,
            # CrewAI 2025 specific settings
            system_template=getattr(self._config, 'system_prompt', None),
            response_template=None,  # Let DSPy handle optimization
        )
    
    def _convert_tool_to_crewai(self, tool: ITool) -> BaseTool:
        """Convert our ITool to CrewAI BaseTool."""
        
        class CrewAIToolWrapper(BaseTool):
            name: str = tool.spec.name
            description: str = tool.spec.description
            
            def _run(self, **kwargs) -> str:
                # Execute our tool and return result
                result = asyncio.run(tool.execute(**kwargs))
                if result.is_success:
                    return str(result.output)
                else:
                    raise Exception(f"Tool execution failed: {result.error}")
        
        return CrewAIToolWrapper()
    
    def _step_callback(self, step_output: str) -> None:
        """Callback for CrewAI step execution."""
        self._progress.current_output = step_output
        self._progress.last_update = datetime.now()
        self._progress.iterations_completed += 1
    
    async def execute_task(self, task: ITask) -> TaskResult:
        """Execute a task using combined CrewAI + DSPy approach."""
        start_time = datetime.now()
        self._current_task = task
        self._status = ModelAgentStatus.WORKING
        self._progress.status = ModelAgentStatus.WORKING
        self._progress.start_time = start_time
        self._progress.current_task = task.spec.description
        
        try:
            # Use DSPy module if available for intelligent processing
            if self._dspy_module:
                result = await self._execute_with_dspy(task)
            else:
                result = await self._execute_with_crewai(task)
            
            # Update status and metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            self._status = ModelAgentStatus.COMPLETED
            self._progress.status = ModelAgentStatus.COMPLETED
            self._progress.progress_percentage = 100.0
            
            # Record execution history
            self._execution_history.append({
                "task_id": task.spec.task_id,
                "task_type": task.spec.task_type.value,
                "execution_time": execution_time,
                "success": result.is_success,
                "timestamp": start_time.isoformat()
            })
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self._status = ModelAgentStatus.FAILED
            self._progress.status = ModelAgentStatus.FAILED
            self._progress.errors.append(str(e))
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=str(e),
                execution_time=execution_time,
                agent_id=self._agent_id
            )
        finally:
            self._current_task = None
    
    async def _execute_with_dspy(self, task: ITask) -> TaskResult:
        """Execute task using DSPy module."""
        # Prepare inputs for DSPy module
        inputs = {
            "query": task.spec.description,
            "context": task.spec.context,
            **task.spec.inputs
        }
        
        # Execute DSPy module
        dspy_result = self._dspy_module(**inputs)
        
        return TaskResult(
            task_id=task.spec.task_id,
            task_type=task.spec.task_type,
            status=TaskStatus.COMPLETED,
            output=dspy_result,
            agent_id=self._agent_id
        )
    
    async def _execute_with_crewai(self, task: ITask) -> TaskResult:
        """Execute task using CrewAI agent."""
        # CrewAI execution is synchronous, but we can wrap it
        def _sync_execute():
            # Use the agent's execute method or direct tool usage
            return self._crewai_agent.execute_task(task.spec.description)
        
        # Run in executor to avoid blocking
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, _sync_execute)
        
        return TaskResult(
            task_id=task.spec.task_id,
            task_type=task.spec.task_type,
            status=TaskStatus.COMPLETED,
            output=result,
            agent_id=self._agent_id
        )
    
    async def can_handle_task(self, task: ITask) -> bool:
        """Check if agent can handle the given task."""
        # Check task type compatibility
        if task.spec.task_type not in self._get_supported_task_types():
            return False
        
        # Check if required tools are available
        required_tools = task.spec.context.get('required_tools', [])
        available_tools = [tool.spec.name for tool in self._tools]
        
        for required_tool in required_tools:
            if required_tool not in available_tools:
                return False
        
        return True
    
    def can_handle(self, task) -> bool:
        """Synchronous can_handle method required by IAgent interface."""
        # For compatibility with IAgent interface
        return asyncio.run(self.can_handle_task(task))
    
    async def execute(self, task) -> AgentResult:
        """Execute method required by IAgent interface."""
        # This is a compatibility wrapper
        result = await self.execute_task(task)
        
        # Map our status to interface status
        status_mapping = {
            TaskStatus.COMPLETED: AgentStatus.COMPLETED,
            TaskStatus.FAILED: AgentStatus.FAILED,
            TaskStatus.IN_PROGRESS: AgentStatus.EXECUTING
        }
        
        return AgentResult(
            agent_id=self._agent_id,
            task_id=task.spec.task_id,
            status=status_mapping.get(result.status, AgentStatus.FAILED),
            output=result.output,
            metadata=getattr(result, 'metadata', {}),
            execution_time=getattr(result, 'execution_time', 0.0),
            error=result.error
        )
    
    async def delegate_task(self, task: ITask, target_agent_id: str) -> TaskResult:
        """Delegate task to another agent."""
        # This would integrate with the flow orchestrator
        # For now, return a placeholder
        return TaskResult(
            task_id=task.spec.task_id,
            task_type=task.spec.task_type,
            status=TaskStatus.FAILED,
            error="Delegation not implemented in base class",
            agent_id=self._agent_id
        )
    
    def add_tool(self, tool: ITool) -> None:
        """Add a tool to the agent's toolkit."""
        self._tools.append(tool)
        # Update CrewAI agent tools
        self._crewai_agent.tools.append(self._convert_tool_to_crewai(tool))
    
    def remove_tool(self, tool_name: str) -> None:
        """Remove a tool from the agent's toolkit."""
        self._tools = [t for t in self._tools if t.spec.name != tool_name]
        # Update CrewAI agent tools
        self._crewai_agent.tools = [
            t for t in self._crewai_agent.tools 
            if t.name != tool_name
        ]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics."""
        if not self._execution_history:
            return {}
        
        total_tasks = len(self._execution_history)
        successful_tasks = sum(1 for h in self._execution_history if h['success'])
        avg_execution_time = sum(h['execution_time'] for h in self._execution_history) / total_tasks
        
        return {
            "total_tasks": total_tasks,
            "success_rate": successful_tasks / total_tasks,
            "average_execution_time": avg_execution_time,
            "current_status": self._status.value,
            "agent_type": self._agent_type.value,
            "capabilities": self.capabilities
        }
    
    @abstractmethod
    def _get_supported_task_types(self) -> List[TaskType]:
        """Get list of task types this agent can handle."""
        pass
    
    @abstractmethod
    async def _specialized_execution(self, task: ITask) -> TaskResult:
        """Specialized execution logic for each agent type."""
        pass


class BaseSpecialistAgent(BaseMultiAgent):
    """
    Base class for specialist agents (Researcher, Librarian, etc.).
    
    Provides common functionality for iterative task processing.
    """
    
    async def iterate_until_complete(self, 
                                   task: ITask, 
                                   max_iterations: Optional[int] = None) -> TaskResult:
        """Iterate on task until completion or max iterations reached."""
        max_iter = max_iterations or self._config.max_iterations
        
        for iteration in range(max_iter):
            self._progress.iterations_completed = iteration + 1
            
            result = await self.execute_task(task)
            
            if result.is_success and self._is_task_complete(result):
                return result
            
            # Prepare for next iteration
            task.spec.context['previous_result'] = result.output
            task.spec.context['iteration'] = iteration + 1
        
        # Return last result if max iterations reached
        return result
    
    def _is_task_complete(self, result: TaskResult) -> bool:
        """Check if task is complete based on result quality."""
        # This can be overridden by specific agent types
        # Basic implementation checks for non-empty output
        if not result.output:
            return False
        
        output_str = str(result.output)
        return len(output_str.strip()) > 50  # Minimum meaningful response
    
    async def _specialized_execution(self, task: ITask) -> TaskResult:
        """Default implementation calls iterate_until_complete."""
        return await self.iterate_until_complete(task) 