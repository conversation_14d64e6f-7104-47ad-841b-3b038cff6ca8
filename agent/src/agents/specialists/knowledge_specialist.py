"""
Enhanced Knowledge Specialist with mathematical computation capabilities.

Implements advanced knowledge processing with built-in Python execution
for mathematical calculations and computational analysis.
"""

import asyncio
import uuid
import copy
from typing import Any, Dict, List, Optional
from datetime import datetime

import dspy
from dspy import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChainOfThought, Prediction

from ..base.base_agent import BaseSpecialistAgent
from ...core.interfaces.task_interface import <PERSON>ask, TaskResult, TaskStatus, TaskType


class MathEnabledKnowledgeSpecialist(BaseSpecialistAgent):
    """
    Knowledge specialist with math and code execution capabilities.
    
    Features:
    - Built-in Python interpreter for calculations
    - Auto-detection of math/code requirements
    - Knowledge synthesis and reasoning
    - Fallback to regular reasoning for non-computational tasks
    """
    
    def __init__(self, config, tools=None, **kwargs):
        # Initialize BaseSpecialistAgent
        super().__init__(config, tools, **kwargs)

        # Create internal DSPy module for knowledge functionality
        self._dspy_module = self._create_dspy_module()

    def _create_dspy_module(self):
        """Create internal DSPy module for knowledge functionality."""
        class KnowledgeModule(dspy.Module):
            def __init__(self):
                super().__init__()
                # Initialize Python interpreter for code execution
                self.calculator = PythonInterpreter({})

                # Predictors for different types of queries
                self.math_detector = ChainOfThought(
                    "question -> requires_computation: bool, computation_type: str, reasoning: str"
                )

                self.knowledge_predictor = ChainOfThought(
                    "question, context -> answer: str, confidence: float, knowledge_areas: list[str]"
                )

                self.code_generator = ChainOfThought(
                    "problem_description -> python_code: str, explanation: str, expected_output: str"
                )

            def forward(self, question: str, context: str = "") -> dspy.Prediction:
                try:
                    # Step 1: Detect if computational work is needed
                    detection = self.math_detector(question=question)

                    if detection.requires_computation:
                        # Step 2: Generate and execute code
                        code_response = self.code_generator(problem_description=question)

                        try:
                            computation_result = self.calculator.execute(code_response.python_code)

                            # Step 3: Synthesize with knowledge
                            full_context = f"{context}\n\nComputation Result: {computation_result}\nCode: {code_response.python_code}" if context else f"Computation Result: {computation_result}\nCode: {code_response.python_code}"
                            knowledge_response = self.knowledge_predictor(question=question, context=full_context)

                            return dspy.Prediction(
                                answer=knowledge_response.answer,
                                confidence=knowledge_response.confidence,
                                knowledge_areas=knowledge_response.knowledge_areas,
                                computation_result=str(computation_result),
                                python_code=code_response.python_code,
                                requires_computation=True,
                                method="math_enabled_knowledge"
                            )

                        except Exception as e:
                            # Fallback to regular knowledge processing
                            knowledge_response = self.knowledge_predictor(question=question, context=context)

                            return dspy.Prediction(
                                answer=knowledge_response.answer,
                                confidence=max(0.0, knowledge_response.confidence - 0.2),  # Lower confidence due to computation failure
                                knowledge_areas=knowledge_response.knowledge_areas,
                                computation_error=str(e),
                                requires_computation=True,
                                method="math_enabled_knowledge_fallback"
                            )
                    else:
                        # Step 2: Regular knowledge processing
                        knowledge_response = self.knowledge_predictor(question=question, context=context)

                        return dspy.Prediction(
                            answer=knowledge_response.answer,
                            confidence=knowledge_response.confidence,
                            knowledge_areas=knowledge_response.knowledge_areas,
                            requires_computation=False,
                            method="knowledge_synthesis"
                        )

                except Exception as e:
                    return dspy.Prediction(
                        answer=f"Knowledge processing failed: {str(e)}",
                        confidence=0.0,
                        knowledge_areas=[],
                        error=str(e),
                        method="math_enabled_knowledge"
                    )

        return KnowledgeModule()

    def get_lm(self):
        """DSPy interface method - delegate to internal module."""
        return self._dspy_module.get_lm() if hasattr(self._dspy_module, 'get_lm') else None

    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        DSPy forward method for knowledge processing with math capabilities.

        Args:
            question: The knowledge question
            context: Additional context

        Returns:
            DSPy Prediction with knowledge synthesis results
        """
        return self._dspy_module.forward(question, context)

    async def _specialized_execution(self, task: ITask) -> TaskResult:
        """Execute knowledge task with math/code capabilities."""
        try:
            question = task.spec.description
            context = task.spec.context.get('content', '') if task.spec.context else ''
            
            # Step 1: Detect if computational work is needed
            detection = self.math_detector(question=question)
            
            result = {
                "original_question": question,
                "requires_computation": detection.requires_computation,
                "computation_type": detection.computation_type,
                "detection_reasoning": detection.reasoning,
                "timestamp": datetime.now().isoformat()
            }
            
            if detection.requires_computation:
                # Step 2: Generate and execute code
                code_response = self.code_generator(problem_description=question)
                
                try:
                    # Execute the generated code
                    execution_result = self.calculator.execute(code_response.python_code)
                    
                    result.update({
                        "answer": str(execution_result),
                        "python_code": code_response.python_code,
                        "code_explanation": code_response.explanation,
                        "expected_output": code_response.expected_output,
                        "execution_successful": True,
                        "method": "computational"
                    })
                    
                except Exception as exec_error:
                    # Fallback to reasoning if code execution fails
                    fallback_response = self.knowledge_predictor(
                        question=question,
                        context=f"Code execution failed: {exec_error}\nContext: {context}"
                    )
                    
                    result.update({
                        "answer": fallback_response.answer,
                        "confidence": fallback_response.confidence,
                        "knowledge_areas": fallback_response.knowledge_areas,
                        "execution_successful": False,
                        "execution_error": str(exec_error),
                        "method": "reasoning_fallback"
                    })
            else:
                # Step 3: Use knowledge reasoning for non-computational tasks
                knowledge_response = self.knowledge_predictor(
                    question=question,
                    context=context
                )
                
                result.update({
                    "answer": knowledge_response.answer,
                    "confidence": knowledge_response.confidence,
                    "knowledge_areas": knowledge_response.knowledge_areas,
                    "method": "knowledge_reasoning"
                })
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.COMPLETED,
                output=result,
                agent_id=self._agent_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=f"Knowledge processing failed: {str(e)}",
                agent_id=self._agent_id
            )
    
    def _get_supported_task_types(self) -> List[TaskType]:
        """Return supported task types."""
        return [TaskType.ANALYSIS, TaskType.RESEARCH]
    
    async def solve_math_problem(self, problem: str) -> Dict[str, Any]:
        """
        Direct method for solving mathematical problems.
        
        Args:
            problem: Mathematical problem description
            
        Returns:
            Solution with code and explanation
        """
        code_response = self.code_generator(problem_description=problem)
        
        try:
            result = self.calculator.execute(code_response.python_code)
            return {
                "problem": problem,
                "solution": str(result),
                "code": code_response.python_code,
                "explanation": code_response.explanation,
                "successful": True
            }
        except Exception as e:
            return {
                "problem": problem,
                "solution": None,
                "error": str(e),
                "code": code_response.python_code,
                "successful": False
            }
    
    async def analyze_knowledge_query(self, query: str, context: str = "") -> Dict[str, Any]:
        """
        Analyze a knowledge-based query.
        
        Args:
            query: Knowledge query
            context: Additional context
            
        Returns:
            Analysis with knowledge areas and confidence
        """
        response = self.knowledge_predictor(question=query, context=context)
        
        return {
            "query": query,
            "answer": response.answer,
            "confidence": response.confidence,
            "knowledge_areas": response.knowledge_areas,
            "reasoning": getattr(response, 'reasoning', '')
        } 