"""
ReAct Search Specialist with reasoning and action capabilities.

Implements step-by-step reasoning with tool usage for complex search
and information gathering tasks using DSPy's ReAct pattern.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
import logging

import dspy
from dspy import ReAct, ChainOfThought

from ..base.base_agent import BaseSpecialistAgent
from ...core.interfaces.task_interface import ITask, TaskResult, TaskStatus, TaskType
from ...tools.crewai_tools_integration import professional_tools

# Import document retrieval tools
from ...agents.tools.dspy_retrieval_tools import (
    get_enterprise_retriever, 
    get_contextual_retriever,
    EnterpriseRAGModule
)


class ReActSearchSpecialist(dspy.Module):
    """
    ReAct-enabled search specialist with tool integration.
    
    Features:
    - ReAct reasoning + action capabilities
    - Professional tool integration
    - Document retrieval and RAG capabilities
    - Step-by-step problem solving
    - Automatic tool selection and usage
    """
    
    def __init__(self, config=None, tools=None, websocket_manager=None, session_id=None, sse_manager=None, **kwargs):
        super().__init__()
        self.config = config or {}
        self.websocket_manager = websocket_manager  # Keep for backward compatibility
        self.sse_manager = sse_manager  # Use SSE for real-time updates
        self.session_id = session_id
        self.custom_tools = tools or {}
        self._agent_id = f"react_search_specialist_{hash(str(self))}"
        
        # Initialize document retrieval components
        self.enterprise_retriever = None
        self.contextual_retriever = None
        self.rag_module = None
        self._retrieval_initialized = False
        
        # Initialize agent with basic config including new document tools
        self.agent_config = {
            "max_iterations": 8,
            "model_name": "gpt-4o-mini",
            "temperature": 0.1,
            "reasoning_enabled": True,
            "use_cache": False,
            "timeout": 120,
            "tools": [
                "web_search", "website_analysis", "data_analysis", 
                "document_retrieval", "document_search", "contextual_search", "rag_answer"
            ]
        }
        
        # Update config with any provided values
        self.agent_config.update(self.config)
        
        # Set up DSPy tools
        self.dspy_tools = self._setup_dspy_compatible_tools()
        
        # Initialize standard React agent
        self.react_agent = ReAct(
            signature="question -> answer: str",
            tools=list(self.dspy_tools.values()),
            max_iters=self.agent_config["max_iterations"]
        )
        
        # Store the main event loop reference for WebSocket updates from thread pool
        try:
            # Try to get the running event loop first
            try:
                self.main_event_loop = asyncio.get_running_loop()
            except RuntimeError:
                # If no running loop, try to get the event loop for the current thread
                self.main_event_loop = asyncio.get_event_loop()
        except RuntimeError:
            # If that fails, set to None (will be handled in _send_tool_update)
            self.main_event_loop = None
        
        # Create async-compatible version
        self.async_react_agent = AsyncReActWrapper(
            signature="question -> answer: str",
            tools=self.dspy_tools,
            max_iters=self.agent_config["max_iterations"],
            websocket_manager=websocket_manager,
            sse_manager=sse_manager,
            session_id=session_id
        )
        
        # Initialize for specialist behavior with enhanced tool set
        self.available_tools = {
            "web_search": self.dspy_tools["search_web"],
            "website_analysis": self.dspy_tools["analyze_website"],
            "data_analysis": self.dspy_tools["analyze_data"],
            "document_retrieval": self.dspy_tools["search_website_content"],
            "file_reader": self.dspy_tools["read_file_content"],
            "directory_reader": self.dspy_tools["explore_directory"],
            "code_docs_search": self.dspy_tools["search_code_docs"],
            "synthesis": self.dspy_tools["synthesize_information"],
            # New document search tools
            "document_search": self.dspy_tools["search_documents"],
            "contextual_search": self.dspy_tools["contextual_document_search"],
            "rag_answer": self.dspy_tools["rag_question_answer"]
        }
        
        logging.info(f"ReAct Search Specialist initialized with {len(self.available_tools)} tools (including document retrieval)")
        
        # Fallback predictor for complex queries
        self.fallback_predictor = ChainOfThought(
            "question, context -> answer: str, confidence: float, approach: str"
        )
    
    def _send_tool_update(self, tool_name: str, status: str, message: str):
        """Send tool status update via SSE if available - IMMEDIATE EXECUTION."""
        # Prefer SSE over WebSocket for real-time updates
        if self.sse_manager and self.session_id:
            try:
                # Create tool data
                tool_data = {
                    "tool_name": tool_name,
                    "status": status,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Send immediately via SSE
                print(f"📡 SSE update sent: {tool_name} - {status}")
                
                # Use stored main event loop to schedule from thread pool
                import asyncio
                try:
                    if self.main_event_loop and self.main_event_loop.is_running():
                        # Schedule the coroutine to run in the main event loop
                        future = asyncio.run_coroutine_threadsafe(
                            self.sse_manager.send_tool_status_update(self.session_id, tool_data),
                            self.main_event_loop
                        )
                        print(f"📡 SSE task scheduled from thread to main loop for {tool_name} - {status}")
                    else:
                        print(f"⚠️  No stored main event loop for SSE update")
                except Exception as e:
                    print(f"⚠️  SSE scheduling error: {e}")
                    # Don't let SSE errors break tool execution
                    
            except Exception as e:
                print(f"⚠️  SSE update failed: {e}")
                # Don't let SSE errors break tool execution
                pass
        elif self.websocket_manager and self.session_id:
            # Fallback to WebSocket for backward compatibility
            try:
                # Create tool data
                tool_data = {
                    "tool_name": tool_name,
                    "status": status,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Send via WebSocket as fallback
                print(f"📡 WebSocket update sent (fallback): {tool_name} - {status}")
                
                # Use stored main event loop to schedule from thread pool
                import asyncio
                try:
                    if self.main_event_loop and self.main_event_loop.is_running():
                        # Schedule the coroutine to run in the main event loop
                        future = asyncio.run_coroutine_threadsafe(
                            self.websocket_manager.send_tool_status_update(self.session_id, tool_data),
                            self.main_event_loop
                        )
                        print(f"📡 WebSocket task scheduled from thread to main loop for {tool_name} - {status}")
                    else:
                        print(f"⚠️  No stored main event loop for WebSocket update")
                except Exception as e:
                    print(f"⚠️  WebSocket scheduling error: {e}")
                    # Don't let WebSocket errors break tool execution
                    
            except Exception as e:
                print(f"⚠️  WebSocket update failed: {e}")
                # Don't let WebSocket errors break tool execution
                pass
    
    async def _send_tool_update_async(self, tool_name: str, status: str, message: str):
        """Async version of send tool update with proper yielding."""
        # Send the update synchronously first
        self._send_tool_update(tool_name, status, message)
        
        # Yield control to allow WebSocket processing
        await asyncio.sleep(0.1)  # 100ms to ensure WebSocket delivery
    
    def _setup_react_tools(self) -> Dict[str, Callable]:
        """Setup tools for ReAct agent."""
        
        def web_search_tool(query: str) -> str:
            """Search the web for information using professional search."""
            try:
                web_tool = professional_tools.available_tools['web_search']
                # Use the tool's run method
                result = web_tool._run(query)
                return f"Web search results for '{query}': {result}"
            except Exception as e:
                return f"Web search failed: {str(e)}"
        
        def website_analysis_tool(url: str) -> str:
            """Analyze content from a specific website."""
            try:
                website_tool = professional_tools.available_tools['website_search']
                result = website_tool._run(url)
                return f"Website analysis for '{url}': {result}"
            except Exception as e:
                return f"Website analysis failed: {str(e)}"
        
        def knowledge_synthesis_tool(topic: str) -> str:
            """Synthesize knowledge about a specific topic."""
            # Use chain of thought for knowledge synthesis
            synthesizer = ChainOfThought("topic -> key_points: list[str], summary: str, reliability: float")
            result = synthesizer(topic=topic)
            
            return f"Knowledge synthesis for '{topic}': Summary: {result.summary}, Key points: {result.key_points}, Reliability: {result.reliability}"
        
        def query_expansion_tool(original_query: str) -> str:
            """Expand and improve search queries."""
            expander = ChainOfThought("original_query -> expanded_queries: list[str], search_strategy: str")
            result = expander(original_query=original_query)
            
            return f"Query expansion: Original: '{original_query}', Expanded: {result.expanded_queries}, Strategy: {result.search_strategy}"
        
        return {
            'web_search': web_search_tool,
            'website_analysis': website_analysis_tool,
            'knowledge_synthesis': knowledge_synthesis_tool,
            'query_expansion': query_expansion_tool
        }
    
    def _setup_dspy_compatible_tools(self) -> Dict[str, Callable]:
        """Set up DSPy-compatible tools for the ReAct agent."""
        
        def search_web(query: str) -> str:
            """Search the web for current information."""
            print(f"🔍 TOOL CALLED: search_web with query: {query}")
            
            # Send WebSocket update with longer delay for immediate delivery
            self._send_tool_update("search_web", "running", f"Searching the web for: {query}")
            
            try:
                # Use our own web search tool that works
                from ...tools.search.web_search_tool import WebSearchTool
                web_tool = WebSearchTool()
                result = web_tool._run(query, max_results=5)
                print(f"🔍 TOOL SUCCESS: search_web returned {len(str(result))} characters")
                
                # Send success update with longer delay for immediate delivery
                self._send_tool_update("search_web", "completed", f"Found {len(str(result))} characters of search results")
                
                return f"Web search results: {result}"
            except Exception as e:
                # Fallback to professional tools
                try:
                    web_tool = professional_tools.available_tools['web_search']
                    result = web_tool._run(query)
                    print(f"🔍 TOOL SUCCESS (fallback): search_web returned {len(str(result))} characters")
                    
                    # Send success update
                    self._send_tool_update("search_web", "completed", f"Found {len(str(result))} characters of search results (fallback)")
                    
                    return f"Web search results: {result}"
                except Exception as e2:
                    print(f"🔍 TOOL FAILED: search_web failed: {str(e)}, fallback: {str(e2)}")
                    
                    # Send failure update
                    self._send_tool_update("search_web", "failed", f"Search failed: {str(e)}")
                    
                    return f"Web search failed: {str(e)}. Fallback also failed: {str(e2)}"

        def analyze_website(url: str) -> str:
            """Analyze content from a specific website."""
            print(f"🌐 TOOL CALLED: analyze_website with url: {url}")
            
            # Send WebSocket update with longer delay for immediate delivery
            self._send_tool_update("analyze_website", "running", f"Analyzing website: {url}")
            
            try:
                from ...tools.processors.document_retrieval_tool import DocumentRetrievalTool
                doc_tool = DocumentRetrievalTool()
                result = doc_tool._run(url, extract_links=True, max_length=3000)
                print(f"🌐 TOOL SUCCESS: analyze_website returned {len(str(result))} characters")
                
                # Send success update with longer delay for immediate delivery
                self._send_tool_update("analyze_website", "completed", f"Analyzed website, extracted {len(str(result))} characters")
                
                return f"Website analysis: {result}"
            except Exception as e:
                print(f"🌐 TOOL FAILED: analyze_website failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("analyze_website", "failed", f"Website analysis failed: {str(e)}")
                
                return f"Website analysis failed: {str(e)}"

        def analyze_data(data: str, analysis_type: str = "summary") -> str:
            """Analyze text data to extract insights, statistics, patterns, or sentiment."""
            print(f"📊 TOOL CALLED: analyze_data with {len(data)} characters, type: {analysis_type}")
            
            # Send WebSocket update
            self._send_tool_update("analyze_data", "running", f"Analyzing {len(data)} characters of data ({analysis_type})")
            
            try:
                from ...tools.processors.data_analysis_tool import DataAnalysisTool
                analysis_tool = DataAnalysisTool()
                result = analysis_tool._run(data, analysis_type=analysis_type, output_format="text")
                print(f"📊 TOOL SUCCESS: analyze_data returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("analyze_data", "completed", f"Data analysis completed, generated {len(str(result))} characters of insights")
                
                return f"Data analysis ({analysis_type}): {result}"
            except Exception as e:
                print(f"📊 TOOL FAILED: analyze_data failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("analyze_data", "failed", f"Data analysis failed: {str(e)}")
                
                return f"Data analysis failed: {str(e)}"

        def search_website_content(url: str, search_query: str) -> str:
            """Search for specific content within a website."""
            print(f"🔍🌐 TOOL CALLED: search_website_content with url: {url}, query: {search_query}")
            
            # Send WebSocket update
            self._send_tool_update("search_website_content", "running", f"Searching within website {url} for: {search_query}")
            
            try:
                # Use professional tools website search
                website_tool = professional_tools.available_tools['website_search']
                result = website_tool._run(website_url=url, search_query=search_query)
                print(f"🔍🌐 TOOL SUCCESS: search_website_content returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("search_website_content", "completed", f"Found {len(str(result))} characters from website search")
                
                return f"Website search results: {result}"
            except Exception as e:
                print(f"🔍🌐 TOOL FAILED: search_website_content failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("search_website_content", "failed", f"Website search failed: {str(e)}")
                
                return f"Website search failed: {str(e)}"

        def read_file_content(file_path: str) -> str:
            """Read and analyze the contents of a file."""
            print(f"📄 TOOL CALLED: read_file_content with path: {file_path}")
            
            # Send WebSocket update
            self._send_tool_update("read_file_content", "running", f"Reading file: {file_path}")
            
            try:
                # Use professional tools file reader
                file_tool = professional_tools.available_tools['file_reader']
                result = file_tool._run(file_path=file_path)
                print(f"📄 TOOL SUCCESS: read_file_content returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("read_file_content", "completed", f"Read {len(str(result))} characters from file")
                
                return f"File content: {result}"
            except Exception as e:
                print(f"📄 TOOL FAILED: read_file_content failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("read_file_content", "failed", f"File reading failed: {str(e)}")
                
                return f"File reading failed: {str(e)}"

        def explore_directory(directory_path: str) -> str:
            """Explore and list the contents of a directory."""
            print(f"📁 TOOL CALLED: explore_directory with path: {directory_path}")
            
            # Send WebSocket update
            self._send_tool_update("explore_directory", "running", f"Exploring directory: {directory_path}")
            
            try:
                # Use professional tools directory reader
                dir_tool = professional_tools.available_tools['directory_reader']
                result = dir_tool._run(directory_path=directory_path)
                print(f"📁 TOOL SUCCESS: explore_directory returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("explore_directory", "completed", f"Found {len(str(result))} characters of directory contents")
                
                return f"Directory contents: {result}"
            except Exception as e:
                print(f"📁 TOOL FAILED: explore_directory failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("explore_directory", "failed", f"Directory exploration failed: {str(e)}")
                
                return f"Directory exploration failed: {str(e)}"

        def search_code_docs(query: str) -> str:
            """Search code documentation and repositories for technical information."""
            print(f"💻 TOOL CALLED: search_code_docs with query: {query}")
            
            # Send WebSocket update
            self._send_tool_update("search_code_docs", "running", f"Searching code documentation for: {query}")
            
            try:
                # Use professional tools code docs search
                code_tool = professional_tools.available_tools['code_docs_search']
                result = code_tool._run(search_query=query)
                print(f"💻 TOOL SUCCESS: search_code_docs returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("search_code_docs", "completed", f"Found {len(str(result))} characters of code documentation")
                
                return f"Code documentation search: {result}"
            except Exception as e:
                print(f"💻 TOOL FAILED: search_code_docs failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("search_code_docs", "failed", f"Code documentation search failed: {str(e)}")
                
                return f"Code documentation search failed: {str(e)}"

        def synthesize_information(topic: str, sources: str = "") -> str:
            """Synthesize information about a topic using reasoning and available sources."""
            print(f"🧠 TOOL CALLED: synthesize_information with topic: {topic}")
            
            # Send WebSocket update
            self._send_tool_update("synthesize_information", "running", f"Synthesizing information about: {topic}")
            
            try:
                # Use the existing agent's reasoning capabilities
                context = f"Topic: {topic}\n"
                if sources:
                    context += f"Available sources/information: {sources}\n"
                context += "Please provide a comprehensive synthesis of the available information."
                
                # Use the LLM directly for synthesis
                import dspy
                lm = dspy.settings.lm
                if lm:
                    response = lm(context)
                    result = response.choices[0].message.content if hasattr(response, 'choices') else str(response)
                else:
                    result = f"Synthesis of {topic}: {sources[:500]}..." if sources else f"Unable to synthesize - no information provided for {topic}"
                
                print(f"🧠 TOOL SUCCESS: synthesize_information returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("synthesize_information", "completed", f"Synthesized {len(str(result))} characters of information")
                
                return f"Information synthesis: {result}"
            except Exception as e:
                print(f"🧠 TOOL FAILED: synthesize_information failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("synthesize_information", "failed", f"Information synthesis failed: {str(e)}")
                
                return f"Information synthesis failed: {str(e)}"

        def search_documents(query: str) -> str:
            """
            Search uploaded documents using enterprise vector database with enhanced query processing.
            
            Performs semantic search across all uploaded documents with query expansion,
            similarity filtering, and result ranking to find the most relevant information.
            
            Args:
                query: Natural language search query (e.g., "magic number 42", "error handling")
                
            Returns:
                Formatted search results with relevance scores and metadata
            """
            print(f"📚 TOOL CALLED: search_documents with query: {query}")
            
            # Send update
            self._send_tool_update("search_documents", "running", f"Searching documents for: {query}")
            
            try:
                # Ensure retrieval tools are initialized
                if not self._retrieval_initialized:
                    # Try to initialize from thread pool executor
                    import asyncio
                    try:
                        # First try to get the current event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If we're in an async context, we need to handle this differently
                            import concurrent.futures
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(self._init_retrieval_sync)
                                future.result(timeout=30)
                        else:
                            loop.run_until_complete(self.initialize_retrieval_tools())
                    except RuntimeError:
                        # No event loop, create one
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(self.initialize_retrieval_tools())
                        finally:
                            loop.close()
                    except Exception as e:
                        print(f"❌ Failed to initialize retrieval tools: {e}")
                        return "Error: Could not initialize document search. Please try again."
                
                # Use the enhanced retriever directly (no need for signature creation)
                prediction = self.enterprise_retriever.forward(query)
                print(f"📚🔍 PREDICTION OBJECT TYPE: {type(prediction)}")
                print(f"📚🔍 PREDICTION OBJECT ATTRIBUTES: {dir(prediction)}")
                
                # Extract passages with better error handling for enhanced retriever
                passages = []
                if hasattr(prediction, 'passages'):
                    print(f"📚🔍 PREDICTION.PASSAGES TYPE: {type(prediction.passages)}")
                    print(f"📚🔍 PREDICTION.PASSAGES COUNT: {len(prediction.passages) if prediction.passages else None}")
                    
                    if prediction.passages:
                        for i, passage in enumerate(prediction.passages):
                            print(f"📚🔍 PROCESSING PASSAGE {i+1}: {type(passage)}")
                            
                            # Handle both old and new passage formats
                            content = None
                            if hasattr(passage, 'content'):
                                content = passage.content
                            elif hasattr(passage, 'long_text'):
                                content = passage.long_text
                            
                            if content:
                                passages.append({
                                    'content': content,
                                    'score': getattr(passage, 'score', 0.0),
                                    'metadata': getattr(passage, 'metadata', {}),
                                    'source': getattr(passage, 'source', 'unknown')
                                })
                                print(f"📚🔍 PASSAGE {i+1} CONTENT LENGTH: {len(content)}")
                                print(f"📚🔍 PASSAGE {i+1} SCORE: {getattr(passage, 'score', 'N/A')}")
                                print(f"📚🔍 PASSAGE {i+1} RERANKED: {getattr(passage, 'score', 0.0) > 0.3}")
                
                # Format results with enhanced presentation
                if passages:
                    result_parts = [f"Found {len(passages)} relevant documents:\n"]
                    
                    for i, passage in enumerate(passages, 1):
                        score = passage.get('score', 0.0)
                        metadata = passage.get('metadata', {})
                        content = passage['content']
                        
                        # Enhanced metadata display with cross-encoder score
                        source_info = []
                        if metadata.get('source_file'):
                            source_info.append(f"File: {metadata['source_file']}")
                        if metadata.get('session_id'):
                            source_info.append(f"Session: {metadata['session_id']}")
                        if metadata.get('chunk_index') is not None:
                            source_info.append(f"Chunk: {metadata['chunk_index']}")
                        
                        # Use source from passage if available, otherwise from metadata
                        source_str = " | ".join(source_info) if source_info else passage.get('source', 'Unknown source')
                        
                        # Indicate if this was reranked by cross-encoder
                        rerank_indicator = " 🔄" if score > 0.3 else ""
                        
                        result_parts.append(f"""
**Document {i}** (Relevance: {score:.3f}{rerank_indicator})
Source: {source_str}
Content: {content}
""")
                    
                    result = "\n".join(result_parts)
                    print(f"📚 TOOL SUCCESS: search_documents returned {len(result)} characters")
                    print(f"📚 RESULT PREVIEW: {result[:200]}...")
                    
                    # Send success update
                    self._send_tool_update("search_documents", "completed", f"Found {len(passages)} relevant documents")
                    
                    return result
                    
                else:
                    result = "No relevant documents found in the knowledge base. Try rephrasing your query or check if documents have been uploaded."
                    print(f"📚 TOOL SUCCESS: search_documents returned {len(result)} characters")
                    print(f"📚 RESULT PREVIEW: {result}")
                    
                    # Send completion update
                    self._send_tool_update("search_documents", "completed", "No relevant documents found")
                    
                    return result
                    
            except Exception as e:
                error_msg = f"Error searching documents: {str(e)}"
                print(f"📚 TOOL ERROR: {error_msg}")
                
                # Send error update
                self._send_tool_update("search_documents", "error", error_msg)
                
                return error_msg

        def contextual_document_search(query: str, file_types: str = "", time_range: str = "") -> str:
            """Search documents with contextual filters (file types, time range)."""
            print(f"📚🔍 TOOL CALLED: contextual_document_search with query: {query}, filters: {file_types}, {time_range}")
            
            # Send update
            self._send_tool_update("contextual_document_search", "running", f"Searching documents with context for: {query}")
            
            try:
                # Ensure retrieval tools are initialized
                if not self._retrieval_initialized:
                    # Try to initialize from thread pool executor
                    import asyncio
                    try:
                        # First try to get the current event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If we're in an async context, we need to handle this differently
                            return "Contextual document search unavailable - retrieval tools not initialized. Please initialize first."
                        else:
                            # If there's a loop but it's not running, use it
                            loop.run_until_complete(self.initialize_retrieval_tools())
                    except RuntimeError:
                        # No event loop in this thread - create a new one
                        asyncio.run(self.initialize_retrieval_tools())
                
                if not self.enterprise_retriever:
                    return "Contextual document search unavailable - no documents uploaded or vector database not configured."
                
                # For now, use basic retrieval (contextual filtering can be enhanced later)
                prediction = self.enterprise_retriever.forward(query)
                
                if prediction.passages:
                    # Filter by file types if specified
                    filtered_passages = prediction.passages
                    if file_types:
                        file_type_list = [t.strip() for t in file_types.split(",") if t.strip()]
                        filtered_passages = [p for p in prediction.passages 
                                           if any(ft in p.source.lower() for ft in file_type_list)]
                    
                    results = []
                    for i, passage in enumerate(filtered_passages):
                        results.append(f"Document {i+1}:\nSource: {passage.source}\nContent: {passage.long_text[:500]}...\nScore: {passage.score:.3f}")
                    
                    result = f"Found {len(filtered_passages)} relevant documents (filtered):\n\n" + "\n\n".join(results)
                else:
                    result = "No relevant documents found matching the contextual filters."
                
                print(f"📚🔍 TOOL SUCCESS: contextual_document_search returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("contextual_document_search", "completed", f"Found {len(filtered_passages)} filtered documents")
                
                return result
                
            except Exception as e:
                print(f"📚🔍 TOOL FAILED: contextual_document_search failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("contextual_document_search", "failed", f"Contextual document search failed: {str(e)}")
                
                return f"Contextual document search failed: {str(e)}"

        def rag_question_answer(question: str) -> str:
            """Answer questions using document retrieval and chain of thought reasoning."""
            print(f"🧠📚 TOOL CALLED: rag_question_answer with question: {question}")
            
            # Send update
            self._send_tool_update("rag_question_answer", "running", f"Generating RAG answer for: {question}")
            
            try:
                # Ensure retrieval tools are initialized
                if not self._retrieval_initialized:
                    # Try to initialize from thread pool executor
                    import asyncio
                    try:
                        # First try to get the current event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If we're in an async context, we need to handle this differently
                            return "RAG answering unavailable - retrieval tools not initialized. Please initialize first."
                        else:
                            # If there's a loop but it's not running, use it
                            loop.run_until_complete(self.initialize_retrieval_tools())
                    except RuntimeError:
                        # No event loop in this thread - create a new one
                        asyncio.run(self.initialize_retrieval_tools())
                
                if not self.rag_module:
                    return "RAG answering unavailable - no documents uploaded or vector database not configured."
                
                # Generate RAG answer using the RAG module
                # Note: DSPy modules work synchronously, so this should be safe
                prediction = self.rag_module.forward(question)
                
                # Format the response
                result_parts = [f"Answer: {prediction.answer}"]
                
                if hasattr(prediction, 'reasoning') and prediction.reasoning:
                    result_parts.append(f"Reasoning: {prediction.reasoning}")
                
                if hasattr(prediction, 'retrieved_passages'):
                    result_parts.append(f"Retrieved {prediction.retrieved_passages} relevant document passages")
                
                result = "\n\n".join(result_parts)
                
                print(f"🧠📚 TOOL SUCCESS: rag_question_answer returned {len(str(result))} characters")
                
                # Send success update
                self._send_tool_update("rag_question_answer", "completed", f"Generated RAG answer with {len(str(result))} characters")
                
                return result
                
            except Exception as e:
                print(f"🧠📚 TOOL FAILED: rag_question_answer failed: {str(e)}")
                
                # Send failure update
                self._send_tool_update("rag_question_answer", "failed", f"RAG question answering failed: {str(e)}")
                
                return f"RAG question answering failed: {str(e)}"

        return {
            'search_web': search_web,
            'analyze_website': analyze_website,
            'analyze_data': analyze_data,
            'search_website_content': search_website_content,
            'read_file_content': read_file_content,
            'explore_directory': explore_directory,
            'search_code_docs': search_code_docs,
            'synthesize_information': synthesize_information,
            # New document search tools
            'search_documents': search_documents,
            'contextual_document_search': contextual_document_search,
            'rag_question_answer': rag_question_answer
        }
    
    async def aforward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        Async forward method that processes a question using the ReAct agent.
        This method yields control to the event loop to allow WebSocket messages.
        """
        try:
            # Combine question and context
            full_question = f"{question}"
            if context:
                full_question += f"\n\nAdditional context: {context}"
            
            # Yield control to allow WebSocket messages to be processed
            await asyncio.sleep(0)
            
            # Execute ReAct agent asynchronously with proper event loop yielding
            react_result = await self.async_react_agent.acall(question=full_question)
            
            # Yield control again after execution
            await asyncio.sleep(0)
            
            return react_result
            
        except Exception as e:
            # Log the error and return a basic prediction
            logging.error(f"Error in ReAct agent: {e}")
            return dspy.Prediction(
                answer=f"I encountered an error while processing your question: {str(e)}"
            )

    # Keep the synchronous forward method for backward compatibility
    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        Synchronous forward method - now calls the async version.
        """
        return asyncio.run(self.aforward(question, context))

    async def execute_task(self, task: ITask) -> TaskResult:
        """Execute search task using ReAct reasoning and tools."""
        try:
            query = task.spec.description
            context = task.spec.context.get('content', '') if task.spec.context else ''
            
            # Use the forward method for processing
            result = self.forward(question=query, context=context)
            
            # Format result for TaskResult
            output = {
                "original_query": query,
                "answer": result.answer,
                "confidence": getattr(result, 'confidence', 0.8),
                "reasoning_steps": getattr(result, 'reasoning_steps', []),
                "method": getattr(result, 'method', 'react_reasoning_and_action'),
                "tools_used": getattr(result, 'tools_used', list(self.dspy_tools.keys())),
                "timestamp": getattr(result, 'timestamp', datetime.now().isoformat()),
                "metadata": {
                    "max_iterations": 8,
                    "agent_type": "ReAct",
                    "reasoning_enabled": True
                }
            }
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.COMPLETED,
                output=output,
                agent_id=self._agent_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=f"ReAct search failed: {str(e)}",
                agent_id=self._agent_id
            )
    
    def _get_supported_task_types(self) -> List[TaskType]:
        """Return supported task types."""
        return [TaskType.RESEARCH, TaskType.ANALYSIS]
    
    async def search_with_reasoning(self, query: str, max_steps: int = 6) -> Dict[str, Any]:
        """
        Direct method for search with step-by-step reasoning.
        
        Args:
            query: Search query
            max_steps: Maximum reasoning steps
            
        Returns:
            Search results with detailed reasoning
        """
        # Create a temporary ReAct agent with custom max_iters
        temp_agent = ReAct(
            signature="question -> answer: str, confidence: float, reasoning_steps: list[str]",
            tools=list(self.available_tools.values()),
            max_iters=max_steps
        )
        
        result = temp_agent(question=query)
        
        return {
            "query": query,
            "answer": result.answer,
            "confidence": getattr(result, 'confidence', 0.8),
            "reasoning_steps": getattr(result, 'reasoning_steps', []),
            "reasoning": getattr(result, 'reasoning', ''),
            "max_steps_used": max_steps
        }
    
    async def demonstrate_reasoning(self, query: str) -> Dict[str, Any]:
        """
        Demonstrate the reasoning process for educational purposes.
        
        Args:
            query: Query to demonstrate reasoning for
            
        Returns:
            Detailed breakdown of reasoning process
        """
        result = await self.search_with_reasoning(query, max_steps=5)
        
        # Add educational breakdown
        result["educational_breakdown"] = {
            "what_is_react": "ReAct combines Reasoning and Acting - the agent thinks step by step and uses tools",
            "reasoning_process": "Each step involves: Think -> Act (use tool) -> Observe (see result) -> Think again",
            "tools_available": list(self.available_tools.keys()),
            "benefits": [
                "Step-by-step problem solving",
                "Tool integration",
                "Transparent reasoning",
                "Better accuracy through iteration"
            ]
        }
        
        return result 

    def _init_retrieval_sync(self):
        """Synchronous wrapper for initializing retrieval tools."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.initialize_retrieval_tools())
        finally:
            loop.close()

    async def initialize_retrieval_tools(self):
        """Initialize document retrieval tools using enhanced retriever from component manager."""
        if self._retrieval_initialized:
            return
            
        try:
            # Get the component manager
            from src.api.services.component_manager import get_component_manager
            
            component_manager = await get_component_manager()
            
            # Get the enhanced retrieval tools from component manager
            retrieval_tools = component_manager.get_react_retrieval_tools()
            
            if retrieval_tools and retrieval_tools.get("initialized"):
                # Use the enhanced retriever with cross-encoder reranking
                self.enterprise_retriever = retrieval_tools["enhanced_retriever"]
                self.rag_module = retrieval_tools["rag_module"]
                
                # Set contextual retriever to the same enhanced retriever for now
                self.contextual_retriever = self.enterprise_retriever
                
                self._retrieval_initialized = True
                logging.info("✅ Enhanced document retrieval tools with cross-encoder reranking initialized successfully")
                
            else:
                # Fallback to individual initialization if component manager doesn't have it
                logging.warning("⚠️ Component manager doesn't have retrieval tools, falling back to individual initialization")
                
                from src.agents.tools.dspy_retrieval_tools import (
                    EnhancedEnterpriseDocumentRetriever,
                    EnterpriseRAGModule
                )
                
                # Create enhanced retriever with reranking
                self.enterprise_retriever = EnhancedEnterpriseDocumentRetriever(
                    k=5,
                    similarity_threshold=0.35,
                    enable_reranking=True
                )
                await self.enterprise_retriever.initialize()
                
                # Initialize RAG module
                self.rag_module = EnterpriseRAGModule(
                    retriever=self.enterprise_retriever,
                    num_passages=5,
                    use_cot=True
                )
                await self.rag_module.initialize()
                
                self.contextual_retriever = self.enterprise_retriever
                self._retrieval_initialized = True
                logging.info("✅ Enhanced document retrieval tools initialized via fallback")
            
        except Exception as e:
            logging.error(f"❌ Failed to initialize retrieval tools: {e}")
            # Set fallback values to prevent errors
            self.enterprise_retriever = None
            self.contextual_retriever = None
            self.rag_module = None

# Add custom AsyncReActWrapper class
class AsyncReActWrapper:
    """
    Async wrapper for DSPy ReAct agent that properly yields control to event loop.
    """
    
    def __init__(self, signature, tools, max_iters=8, websocket_manager=None, sse_manager=None, session_id=None):
        self.signature = signature
        self.tools = tools
        self.max_iters = max_iters
        self.websocket_manager = websocket_manager
        self.sse_manager = sse_manager
        self.session_id = session_id
        
        # Create the underlying ReAct agent
        self.react_agent = ReAct(
            signature=signature,
            tools=list(tools.values()),
            max_iters=max_iters
        )
    
    async def acall(self, question: str) -> dspy.Prediction:
        """
        Async version of ReAct call that yields control between tool executions.
        """
        import asyncio
        import concurrent.futures
        
        # Create a thread pool for running synchronous DSPy agent
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # Submit the synchronous operation to thread pool
            future = executor.submit(self.react_agent, question=question)
            
            # Wait for completion while yielding control
            while not future.done():
                await asyncio.sleep(0.1)  # Yield control to event loop
            
            # Get the result
            return future.result()
    
    def __call__(self, **kwargs):
        """Synchronous call - delegate to underlying agent"""
        return self.react_agent(**kwargs) 