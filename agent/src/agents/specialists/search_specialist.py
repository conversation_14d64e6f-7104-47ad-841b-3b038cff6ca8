"""
Enhanced Search Specialist with ColBERTv2 integration.

Implements professional-grade search capabilities using DSPy's built-in ColBERTv2
retrieval system for high-quality document search and information gathering.
"""

import asyncio
import uuid
import copy
from typing import Any, Dict, List, Optional
from datetime import datetime

import dspy
from dspy import ColBERTv2, ChainOfThought, Prediction

from ..base.base_agent import BaseSpecialistAgent
from ...core.interfaces.task_interface import ITask, TaskResult, TaskStatus, TaskType
from ...core.models.state_models import AgentProgress


class EnhancedSearchSpecialist(BaseSpecialistAgent):
    """
    Enhanced SearchSpecialist with ColBERTv2 professional-grade retrieval.
    
    Features:
    - ColBERTv2 retrieval for high-quality search results
    - Chain-of-thought reasoning for complex queries
    - Structured output with confidence scoring
    - Professional query analysis and expansion
    """
    
    def __init__(self, config, tools=None, **kwargs):
        # Initialize BaseSpecialistAgent
        super().__init__(config, tools, **kwargs)

        # Create internal DSPy module for search functionality
        self._dspy_module = self._create_dspy_module()

    def _create_dspy_module(self):
        """Create internal DSPy module for search functionality."""
        class SearchModule(dspy.Module):
            def __init__(self):
                super().__init__()
                # Initialize ColBERTv2 retriever with Wikipedia abstracts
                self.retriever = ColBERTv2(url='http://************:2017/wiki17_abstracts')

                # Enhanced search predictor with reasoning
                self.search_predictor = ChainOfThought(
                    "context, question -> answer: str, confidence: float, sources: list[str]"
                )

                # Query analysis for better search
                self.query_analyzer = ChainOfThought(
                    "original_query -> expanded_query: str, search_terms: list[str], query_type: str"
                )

            def forward(self, question: str, context: str = "") -> dspy.Prediction:
                try:
                    # Step 1: Analyze and expand the query
                    analysis = self.query_analyzer(original_query=question)

                    # Step 2: Retrieve relevant documents using ColBERTv2
                    expanded_query = analysis.expanded_query
                    search_results = self.retriever(expanded_query, k=10)

                    # Step 3: Process and format context
                    retrieved_context = []
                    sources = []

                    for result in search_results:
                        retrieved_context.append(result.get('text', ''))
                        sources.append(result.get('title', 'Unknown Source'))

                    # Combine retrieved context
                    full_context = " ".join(retrieved_context)
                    if context:
                        full_context = f"{context}\n\n{full_context}"

                    # Step 4: Generate reasoned answer
                    search_response = self.search_predictor(
                        context=full_context,
                        question=question
                    )

                    return dspy.Prediction(
                        answer=search_response.answer,
                        confidence=search_response.confidence,
                        sources=search_response.sources[:5],
                        expanded_query=expanded_query,
                        search_terms=analysis.search_terms,
                        query_type=analysis.query_type,
                        retrieved_documents=len(search_results),
                        reasoning=getattr(search_response, 'reasoning', ''),
                        method="colbert_enhanced_search"
                    )

                except Exception as e:
                    return dspy.Prediction(
                        answer=f"Search failed: {str(e)}",
                        confidence=0.0,
                        sources=[],
                        error=str(e),
                        method="colbert_enhanced_search"
                    )

        return SearchModule()

    def get_lm(self):
        """DSPy interface method - delegate to internal module."""
        return self._dspy_module.get_lm() if hasattr(self._dspy_module, 'get_lm') else None

    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        DSPy forward method for search processing.

        Args:
            question: The search question
            context: Additional context

        Returns:
            DSPy Prediction with search results
        """
        return self._dspy_module.forward(question, context)

    async def _specialized_execution(self, task: ITask) -> TaskResult:
        """Execute search task with ColBERTv2 and reasoning."""
        try:
            query = task.spec.description
            context = task.spec.context
            
            # Use the internal DSPy module for processing
            context_str = context.get('content', '') if context else ''
            dspy_result = self._dspy_module.forward(query, context_str)
            
            # Step 5: Format comprehensive result
            result = {
                "original_query": query,
                "expanded_query": getattr(dspy_result, 'expanded_query', query),
                "search_terms": getattr(dspy_result, 'search_terms', []),
                "query_type": getattr(dspy_result, 'query_type', 'general'),
                "answer": dspy_result.answer,
                "confidence": getattr(dspy_result, 'confidence', 0.8),
                "sources": getattr(dspy_result, 'sources', []),
                "retrieved_documents": getattr(dspy_result, 'retrieved_documents', 0),
                "reasoning": getattr(dspy_result, 'reasoning', ''),
                "metadata": {
                    "retriever": "ColBERTv2",
                    "search_quality": "professional_grade",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.COMPLETED,
                output=result,
                agent_id=self._agent_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=f"Enhanced search failed: {str(e)}",
                agent_id=self._agent_id
            )
    
    def _get_supported_task_types(self) -> List[TaskType]:
        """Return supported task types."""
        return [TaskType.RESEARCH, TaskType.ANALYSIS]
    
    async def search_with_context(self, query: str, additional_context: str = "") -> Dict[str, Any]:
        """
        Public method for direct search with context.
        
        Args:
            query: Search query
            additional_context: Additional context to include
            
        Returns:
            Search results with reasoning and sources
        """
        # Combine contexts
        combined_context = f"{additional_context}\n\nQuery: {query}" if additional_context else query
        
        # Analyze query
        analysis = self.query_analyzer(original_query=combined_context)
        
        # Retrieve with ColBERTv2
        search_results = self.retriever(analysis.expanded_query, k=8)
        
        # Process results
        context_text = " ".join([r.get('text', '') for r in search_results])
        
        # Generate answer with reasoning
        response = self.search_predictor(
            context=context_text,
            question=query
        )
        
        return {
            "answer": response.answer,
            "confidence": response.confidence,
            "sources": response.sources,
            "reasoning": getattr(response, 'reasoning', ''),
            "expanded_query": analysis.expanded_query,
            "documents_retrieved": len(search_results)
        } 