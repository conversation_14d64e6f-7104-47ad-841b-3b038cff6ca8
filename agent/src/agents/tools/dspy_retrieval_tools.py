"""
DSPy Retrieval Tools for Document-based Question Answering

This module implements DSPy-compatible retrieval tools that integrate with
the existing vector database infrastructure, following DSPy best practices.

DSPy 2.6+ Compliance:
- Inherits from dspy.Retrieve base class
- Implements forward() method returning dspy.Prediction
- Uses proper passage format with compatibility fields
- Follows DSPy 2.6+ patterns for custom retrievers
- Supports both sync and async operations
- Includes enhanced error handling and logging

Key Features:
- CPU-optimized cross-encoder reranking 
- Two-stage retrieval pipeline (vector search + reranking)
- Context-aware query enhancement
- Configurable similarity thresholds
- Enterprise-grade error handling
- Rich metadata and scoring
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
from sentence_transformers import CrossEncoder
import numpy as np

import dspy
from dspy import Retrieve, Prediction

# Define dotdict class for compatibility
class dotdict(dict):
    """Dictionary that supports dot notation for accessing keys."""
    __getattr__ = dict.get
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__

# Import our services
from src.api.services.component_manager import get_component_manager
from src.api.services.document_processing_service import get_document_processing_service

logger = logging.getLogger(__name__)


@dataclass
class RetrievalResult:
    """Structured retrieval result for DSPy integration."""
    content: str
    score: float
    metadata: Dict[str, Any]
    source: str
    

class CPUOptimizedCrossEncoder:
    """CPU-optimized cross-encoder for reranking without GPU."""
    
    def __init__(self, model_name: str = "mixedbread-ai/mxbai-rerank-xsmall-v1"):
        """Initialize with CPU-optimized cross-encoder model."""
        self.model_name = model_name
        self.model = None
        self._initialized = False
        
    def _initialize_model(self):
        """Initialize the cross-encoder model for CPU inference."""
        if self._initialized:
            return
        
        try:
            # Load CPU-optimized model
            self.model = CrossEncoder(self.model_name, device='cpu')
            self._initialized = True
            logger.info(f"✅ CPU-optimized cross-encoder loaded: {self.model_name}")
        except Exception as e:
            logger.error(f"❌ Failed to load cross-encoder: {e}")
            raise
    
    def rerank(self, query: str, documents: List[str], top_k: int = 5) -> List[Dict[str, Any]]:
        """Rerank documents using cross-encoder."""
        if not self._initialized:
            self._initialize_model()
        
        if not documents:
            return []
        
        try:
            # Create query-document pairs
            pairs = [(query, doc) for doc in documents]
            
            # Get relevance scores
            scores = self.model.predict(pairs)
            
            # Combine documents with scores
            results = []
            for i, (doc, score) in enumerate(zip(documents, scores)):
                results.append({
                    'document': doc,
                    'score': float(score),
                    'rank': i + 1
                })
            
            # Sort by score (descending) and return top_k
            results.sort(key=lambda x: x['score'], reverse=True)
            
            # Update ranks after sorting
            for i, result in enumerate(results[:top_k]):
                result['rank'] = i + 1
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"❌ Cross-encoder reranking failed: {e}")
            return []


class EnhancedEnterpriseDocumentRetriever(dspy.Retrieve):
    """
    Enhanced retriever with CPU-optimized cross-encoder reranking.
    
    This retriever implements a two-stage pipeline:
    1. Initial semantic search using vector similarity
    2. Cross-encoder reranking for improved precision
    
    Args:
        k (int): Number of documents to retrieve (default: 5)
        similarity_threshold (float): Minimum similarity score threshold (default: 0.35)
        context_filters (Optional[Dict[str, Any]]): Additional filters for retrieval
        enable_reranking (bool): Whether to enable cross-encoder reranking (default: True)
    
    Returns:
        dspy.Prediction: Prediction object with passages containing:
            - content: Document text content
            - score: Relevance score (similarity or cross-encoder score)
            - metadata: Additional document metadata
            - source: Document source information
    
    Example:
        >>> retriever = EnhancedEnterpriseDocumentRetriever(k=5, enable_reranking=True)
        >>> result = retriever("What is machine learning?")
        >>> for passage in result.passages:
        ...     print(f"Score: {passage.score}, Content: {passage.content[:100]}...")
    """
    
    def __init__(self, k: int = 5, similarity_threshold: float = 0.35, 
                 context_filters: Optional[Dict[str, Any]] = None,
                 enable_reranking: bool = True):
        super().__init__(k=k)
        self.document_service = None
        self.similarity_threshold = similarity_threshold
        self.context_filters = context_filters or {}
        self.enable_reranking = enable_reranking
        
        # Initialize CPU-optimized cross-encoder
        if enable_reranking:
            self.cross_encoder = CPUOptimizedCrossEncoder()
        
        logger.info(f"🔍 Enhanced retriever initialized with reranking={enable_reranking}")
    
    def forward(self, query: str, k: Optional[int] = None) -> dspy.Prediction:
        """
        Enhanced forward method with cross-encoder reranking.
        
        Implements DSPy 2.6+ retrieval pattern with two-stage pipeline:
        1. Vector similarity search (fast, broad recall)
        2. Cross-encoder reranking (accurate, precise)
        
        Args:
            query (str): Search query
            k (Optional[int]): Number of documents to retrieve (overrides self.k)
        
        Returns:
            dspy.Prediction: Prediction object with passages list
        """
        search_k = k or self.k
        
        try:
            # Step 1: Initial retrieval (get more candidates for reranking)
            initial_k = min(search_k * 3, 50) if self.enable_reranking else search_k
            
            results = self._retrieve_single(query, initial_k)
            
            if not results:
                logger.warning(f"⚠️ No results found for query: {query}")
                return dspy.Prediction(passages=[])
            
            # Step 2: Cross-encoder reranking (CPU-optimized)
            if self.enable_reranking and len(results) > 1:
                try:
                    # Extract content for reranking
                    documents = [result.content for result in results]
                    
                    # Rerank with cross-encoder
                    reranked = self.cross_encoder.rerank(query, documents, top_k=search_k)
                    
                    # Map back to original results
                    reranked_results = []
                    for ranked_item in reranked:
                        # Find original result by content matching
                        for result in results:
                            if result.content == ranked_item['document']:
                                # Update score with cross-encoder score
                                result.score = ranked_item['score']
                                reranked_results.append(result)
                                break
                    
                    results = reranked_results
                    logger.info(f"🔄 Cross-encoder reranked {len(results)} documents")
                    
                except Exception as e:
                    logger.warning(f"⚠️ Cross-encoder reranking failed, using original results: {e}")
            
            # Step 3: Convert to DSPy format with enhanced passage structure
            passages = []
            for result in results:
                # Create DSPy-compatible passage object
                passage = dotdict({
                    'content': result.content,
                    'score': result.score,
                    'metadata': result.metadata,
                    'source': result.source,
                    # Add compatibility fields for different DSPy patterns
                    'long_text': result.content,  # Some DSPy examples use this field
                    'text': result.content,       # Some DSPy examples use this field
                    'title': result.metadata.get('title', ''),
                    'pid': result.metadata.get('id', ''),
                    'reranked': self.enable_reranking,  # Indicator for cross-encoder scoring
                })
                passages.append(passage)
            
            logger.info(f"📚 Retrieved {len(passages)} passages for query: {query[:50]}...")
            return dspy.Prediction(passages=passages)
            
        except Exception as e:
            logger.error(f"❌ Enhanced retrieval failed: {e}")
            return dspy.Prediction(passages=[])
    
    def _expand_query(self, query: str) -> List[str]:
        """
        Expand query with synonyms and related terms for better recall.
        """
        expanded = [query]  # Always include original query
        
        # Simple keyword expansion (could be enhanced with LLM-based expansion)
        expansions = {
            "magic number": ["magic number", "constant", "literal value", "hardcoded value"],
            "error": ["error", "exception", "failure", "bug", "issue"],
            "config": ["config", "configuration", "settings", "parameters"],
            "database": ["database", "db", "data store", "storage"],
            "API": ["API", "interface", "endpoint", "service"],
        }
        
        for keyword, synonyms in expansions.items():
            if keyword.lower() in query.lower():
                for synonym in synonyms:
                    if synonym not in query:
                        expanded.append(query.replace(keyword, synonym))
        
        return expanded[:3]  # Limit to avoid too many queries
    
    def _deduplicate_results(self, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """
        Remove duplicate results based on content similarity.
        """
        seen_content = set()
        deduplicated = []
        
        for result in results:
            # Use first 100 characters as deduplication key
            content_key = result.content[:100].strip()
            if content_key not in seen_content:
                seen_content.add(content_key)
                deduplicated.append(result)
        
        return deduplicated
    
    def _rerank_results(self, original_query: str, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """
        Rerank results based on relevance to original query.
        Simple implementation - could be enhanced with cross-encoder.
        """
        # For now, just sort by score (descending)
        return sorted(results, key=lambda x: x.score, reverse=True)
    
    async def initialize(self):
        """Initialize the retriever with document service."""
        if self.document_service is None:
            self.document_service = await get_document_processing_service()
        
    def _retrieve_single(self, query: str, limit: int) -> List[RetrievalResult]:
        """Retrieve documents for a single query."""
        try:
            # Run async search with proper event loop handling
            try:
                # Try to get running event loop
                loop = asyncio.get_running_loop()
                # If we have a running loop, use thread pool executor
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._async_search_wrapper, query, limit)
                    search_results = future.result()
            except RuntimeError:
                # No running loop, create one
                search_results = self._async_search_wrapper(query, limit)
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error in single retrieval: {e}")
            return []
    
    def _async_search_wrapper(self, query: str, limit: int) -> List[RetrievalResult]:
        """Wrapper to run async search in a new event loop."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._async_search(query, limit))
        finally:
            loop.close()
    
    async def _async_search(self, query: str, limit: int) -> List[RetrievalResult]:
        """Perform async search using document processing service."""
        try:
            # Ensure document service is initialized
            if self.document_service is None:
                await self.initialize()
            
            search_results = await self.document_service.search_documents(
                query=query,
                limit=limit
            )
            
            # Convert to our format
            results = []
            for result in search_results:
                retrieval_result = RetrievalResult(
                    content=result.content,
                    score=result.score,
                    metadata=result.metadata,
                    source=result.source
                )
                results.append(retrieval_result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in async search: {e}")
            return []


class EnterpriseContextualRetriever(dspy.Retrieve):
    """
    Enhanced contextual retriever with better similarity threshold and query processing.
    
    This retriever enhances queries with contextual information and applies
    improved similarity thresholds for better retrieval performance.
    
    Args:
        k (int): Number of documents to retrieve (default: 5)
        similarity_threshold (float): Minimum similarity score threshold (default: 0.35)
        context_filters (Optional[Dict[str, Any]]): Additional filters for retrieval
    
    Returns:
        dspy.Prediction: Prediction object with passages containing enhanced context
    
    Example:
        >>> retriever = EnterpriseContextualRetriever(k=5, similarity_threshold=0.35)
        >>> result = retriever("machine learning", context="AI and deep learning")
        >>> for passage in result.passages:
        ...     print(f"Score: {passage.score}, Content: {passage.long_text[:100]}...")
    """
    
    def __init__(self, k: int = 5, similarity_threshold: float = 0.35, 
                 context_filters: Optional[Dict[str, Any]] = None):
        super().__init__(k=k)
        self.document_service = None
        self.similarity_threshold = similarity_threshold
        self.context_filters = context_filters or {}
        logger.info(f"🔍 EnterpriseContextualRetriever initialized with k={k}, threshold={similarity_threshold}")
    
    def forward(self, query: str, context: str = "", k: Optional[int] = None) -> dspy.Prediction:
        """
        Enhanced contextual retrieval with better query processing.
        
        Implements DSPy 2.6+ retrieval pattern with context-aware query enhancement:
        1. Enhance query with contextual information
        2. Perform vector similarity search
        3. Apply similarity threshold filtering
        
        Args:
            query (str): Search query
            context (str): Additional context to enhance the query
            k (Optional[int]): Number of documents to retrieve (overrides self.k)
        
        Returns:
            dspy.Prediction: Prediction object with filtered passages
        """
        if k is None:
            k = self.k
        
        # Combine query with context for better retrieval
        enhanced_query = self._enhance_query_with_context(query, context)
        logger.info(f"🔍📚 CONTEXTUAL RETRIEVAL: enhanced_query='{enhanced_query}', k={k}")
        
        try:
            results = self._retrieve_single(enhanced_query, k)
            
            # Apply similarity threshold filtering
            filtered_results = [
                result for result in results 
                if result.score >= self.similarity_threshold
            ]
            
            logger.info(f"🔍📚 CONTEXTUAL RESULTS: {len(filtered_results)} documents after filtering")
            
            # Convert to DSPy format with enhanced compatibility
            passages = []
            for result in filtered_results:
                passage = dotdict({
                    "content": result.content,
                    "score": result.score,
                    "metadata": result.metadata,
                    "source": result.source,
                    # Enhanced compatibility fields for different DSPy patterns
                    "long_text": result.content,
                    "text": result.content,
                    "title": result.metadata.get('title', ''),
                    "pid": result.metadata.get('id', ''),
                    "contextual": True,  # Indicator for contextual retrieval
                    "enhanced_query": enhanced_query,
                })
                passages.append(passage)
            
            logger.info(f"📚 Retrieved {len(passages)} contextual passages")
            return dspy.Prediction(passages=passages)
            
        except Exception as e:
            logger.error(f"❌ Error in contextual retrieval: {e}")
            return dspy.Prediction(passages=[])
    
    def _enhance_query_with_context(self, query: str, context: str) -> str:
        """
        Enhance query with relevant context information.
        """
        if not context:
            return query
        
        # Extract key entities from context (simple implementation)
        context_keywords = self._extract_keywords_from_context(context)
        
        if context_keywords:
            enhanced = f"{query} {' '.join(context_keywords)}"
            return enhanced
        
        return query
    
    def _extract_keywords_from_context(self, context: str) -> List[str]:
        """
        Extract relevant keywords from context.
        Simple implementation - could be enhanced with NLP.
        """
        keywords = []
        
        # Simple keyword extraction
        important_terms = ["error", "config", "database", "API", "service", "application", "system"]
        context_lower = context.lower()
        
        for term in important_terms:
            if term in context_lower:
                keywords.append(term)
        
        return keywords[:3]  # Limit to avoid query bloat
    
    async def initialize(self):
        """Initialize the retriever with document service."""
        if self.document_service is None:
            self.document_service = await get_document_processing_service()
    
    def _retrieve_single(self, query: str, limit: int) -> List[RetrievalResult]:
        """Retrieve documents for a single query."""
        try:
            # Run async search with proper event loop handling
            try:
                # Try to get running event loop
                loop = asyncio.get_running_loop()
                # If we have a running loop, use thread pool executor
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._async_search_wrapper, query, limit)
                    search_results = future.result()
            except RuntimeError:
                # No running loop, create one
                search_results = self._async_search_wrapper(query, limit)
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error in single retrieval: {e}")
            return []
    
    def _async_search_wrapper(self, query: str, limit: int) -> List[RetrievalResult]:
        """Wrapper to run async search in a new event loop."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._async_search(query, limit))
        finally:
            loop.close()
    
    async def _async_search(self, query: str, limit: int) -> List[RetrievalResult]:
        """Perform async search using document processing service."""
        try:
            # Ensure document service is initialized
            if self.document_service is None:
                await self.initialize()
            
            search_results = await self.document_service.search_documents(
                query=query,
                limit=limit
            )
            
            # Convert to our format
            results = []
            for result in search_results:
                retrieval_result = RetrievalResult(
                    content=result.content,
                    score=result.score,
                    metadata=result.metadata,
                    source=result.source
                )
                results.append(retrieval_result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in async search: {e}")
            return []


# DSPy Signatures for RAG operations
class DocumentRAGSignature(dspy.Signature):
    """Generate answer based on retrieved document context."""
    context = dspy.InputField(desc="relevant document passages retrieved from vector database")
    question = dspy.InputField(desc="user question to answer")
    answer = dspy.OutputField(desc="comprehensive answer based on the provided context")


class DocumentRAGWithCoT(dspy.Signature):
    """Generate answer with chain of thought reasoning."""
    context = dspy.InputField(desc="relevant document passages retrieved from vector database")
    question = dspy.InputField(desc="user question to answer")
    reasoning = dspy.OutputField(desc="step-by-step reasoning process")
    answer = dspy.OutputField(desc="final answer based on reasoning and context")


# RAG Module following DSPy best practices
class EnterpriseRAGModule(dspy.Module):
    """
    Enterprise RAG module using document retrieval and chain of thought.
    
    This module implements the RAG pattern with:
    - Document retrieval using enterprise vector database
    - Chain of thought reasoning for better answers
    - Contextual filtering and metadata usage
    """
    
    def __init__(self, retriever: Optional[EnhancedEnterpriseDocumentRetriever] = None, 
                 num_passages: int = 5, use_cot: bool = True):
        """
        Initialize the RAG module.
        
        Args:
            retriever: Document retriever (will create default if None)
            num_passages: Number of passages to retrieve
            use_cot: Whether to use chain of thought reasoning
        """
        super().__init__()
        
        self.retriever = retriever or EnhancedEnterpriseDocumentRetriever(k=num_passages)
        self.num_passages = num_passages
        self.use_cot = use_cot
        
        if use_cot:
            self.generate_answer = dspy.ChainOfThought(DocumentRAGWithCoT)
        else:
            self.generate_answer = dspy.Predict(DocumentRAGSignature)
    
    async def initialize(self):
        """Initialize the RAG module."""
        await self.retriever.initialize()
    
    def forward(self, question: str, filters: Optional[Dict[str, Any]] = None):
        """
        Forward pass for the RAG module.
        
        Args:
            question: User question
            filters: Optional metadata filters for retrieval
            
        Returns:
            DSPy Prediction with answer and context
        """
        # Retrieve relevant documents using DSPy 2.6+ pattern
        retrieval_result = self.retriever.forward(question)
        context_passages = retrieval_result.passages
        
        # Format context for the generator
        if context_passages:
            context = "\n\n".join([
                f"Source: {p.source}\nContent: {p.long_text}" 
                for p in context_passages
            ])
        else:
            context = "No relevant documents found."
        
        # Generate answer
        if self.use_cot:
            prediction = self.generate_answer(context=context, question=question)
            return dspy.Prediction(
                answer=prediction.answer,
                reasoning=prediction.reasoning,
                context=context_passages,
                retrieved_passages=len(context_passages)
            )
        else:
            prediction = self.generate_answer(context=context, question=question)
            return dspy.Prediction(
                answer=prediction.answer,
                context=context_passages,
                retrieved_passages=len(context_passages)
            )


# Global retriever instances
_enterprise_retriever = None
_contextual_retriever = None


async def get_enterprise_retriever() -> EnhancedEnterpriseDocumentRetriever:
    """Get or create the enterprise document retriever."""
    global _enterprise_retriever
    
    if _enterprise_retriever is None:
        _enterprise_retriever = EnhancedEnterpriseDocumentRetriever(k=5)
        await _enterprise_retriever.initialize()
    
    return _enterprise_retriever


async def get_contextual_retriever() -> EnterpriseContextualRetriever:
    """Get or create the contextual document retriever."""
    global _contextual_retriever
    
    if _contextual_retriever is None:
        _contextual_retriever = EnterpriseContextualRetriever(k=5)
        await _contextual_retriever.initialize()
    
    return _contextual_retriever 