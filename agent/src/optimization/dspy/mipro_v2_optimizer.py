"""
DSPy MIPROv2 (Multi-stage Instruction Proposal and Revision Optimizer v2) Implementation.

Advanced optimization pipeline that combines instruction optimization, 
in-context learning, and adaptive parameter tuning for large-scale DSPy programs.
"""

import dspy
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
import random
import concurrent.futures
import numpy as np
from collections import defaultdict
import json
from pathlib import Path
import logging

from .base_optimizer import BaseOptimizer, OptimizationResult, OptimizationConfig

logger = logging.getLogger(__name__)


@dataclass
class MIPROv2Config(OptimizationConfig):
    """Enhanced configuration for MIPROv2 optimization."""
    
    # Core MIPROv2 parameters
    num_instruction_candidates: int = 50
    num_instruction_iterations: int = 25
    instruction_batch_size: int = 5
    
    # Multi-stage optimization
    enable_multi_stage: bool = True
    stage_patience: int = 3
    improvement_threshold: float = 0.01
    
    # Adaptive learning
    enable_adaptive_learning: bool = True
    learning_rate_schedule: Dict[str, float] = field(default_factory=lambda: {
        "initial": 1.0,
        "decay": 0.95,
        "minimum": 0.1
    })
    
    # Advanced sampling
    temperature_schedule: List[float] = field(default_factory=lambda: [1.0, 0.8, 0.6, 0.4])
    top_k_candidates: int = 10
    diversity_penalty: float = 0.1
    
    # Parallel processing
    max_workers: int = 4
    enable_parallel_evaluation: bool = True
    
    # Checkpointing and resumption
    enable_checkpointing: bool = True
    checkpoint_frequency: int = 5
    checkpoint_dir: str = "checkpoints/miprov2"
    
    # Quality control
    min_validation_score: float = 0.1
    outlier_detection_threshold: float = 2.0
    
    def __post_init__(self):
        super().__post_init__()
        Path(self.checkpoint_dir).mkdir(parents=True, exist_ok=True)


class InstructionGenerator:
    """Generates and refines instructions for DSPy programs."""
    
    def __init__(self, config: MIPROv2Config):
        self.config = config
        self.instruction_history = []
        
    def generate_initial_instructions(self, 
                                    program: dspy.Module,
                                    examples: List[dspy.Example],
                                    num_candidates: int) -> List[str]:
        """Generate initial instruction candidates."""
        base_instruction = getattr(program, 'signature', {}).get('instructions', '')
        if not base_instruction:
            base_instruction = "Answer the question based on the provided context."
        
        instructions = [base_instruction]
        
        # Template-based generation
        templates = [
            "Carefully analyze the provided information and {base}",
            "Using step-by-step reasoning, {base}",
            "Based on the given context and your knowledge, {base}",
            "Think through this systematically and {base}",
            "Consider all relevant factors and {base}",
            "Apply logical reasoning to {base}",
            "Examine the evidence thoroughly and {base}",
            "Use critical thinking to {base}",
            "Analyze the relationship between concepts and {base}",
            "Break down the problem systematically and {base}"
        ]
        
        for template in templates[:num_candidates-1]:
            instructions.append(template.format(base=base_instruction.lower()))
        
        # Add variations based on examples
        if examples:
            example_based = self._generate_from_examples(examples, base_instruction, 
                                                       max(1, num_candidates - len(instructions)))
            instructions.extend(example_based)
        
        return instructions[:num_candidates]
    
    def _generate_from_examples(self, 
                               examples: List[dspy.Example], 
                               base_instruction: str,
                               num_needed: int) -> List[str]:
        """Generate instructions based on example patterns."""
        patterns = []
        
        # Analyze example characteristics
        for example in examples[:5]:  # Sample first 5 examples
            if hasattr(example, 'question') and hasattr(example, 'answer'):
                if len(example.question.split()) > 20:
                    patterns.append("For complex questions, break them down into components and ")
                if any(word in example.question.lower() for word in ['compare', 'contrast', 'difference']):
                    patterns.append("When comparing items, identify key similarities and differences and ")
                if any(word in example.question.lower() for word in ['why', 'because', 'reason']):
                    patterns.append("When explaining reasoning, provide clear causal relationships and ")
        
        instructions = []
        for pattern in patterns[:num_needed]:
            instructions.append(pattern + base_instruction.lower())
        
        return instructions
    
    def refine_instruction(self, 
                          instruction: str, 
                          performance_data: Dict[str, Any],
                          iteration: int) -> str:
        """Refine instruction based on performance feedback."""
        if performance_data.get('score', 0) > 0.8:
            # High performance - make minor refinements
            refinements = [
                "Ensure accuracy and ",
                "Double-check your reasoning and ",
                "Verify your conclusions and "
            ]
        elif performance_data.get('score', 0) > 0.5:
            # Medium performance - add structure
            refinements = [
                "Think step-by-step and ",
                "Consider multiple perspectives and ",
                "Analyze systematically and "
            ]
        else:
            # Low performance - major revision
            refinements = [
                "Carefully examine all provided information and ",
                "Use explicit reasoning steps and ",
                "Focus on key details and "
            ]
        
        if iteration < len(refinements):
            return refinements[iteration] + instruction.lower()
        
        return instruction


class AdaptiveSampler:
    """Manages adaptive sampling strategies for MIPROv2."""
    
    def __init__(self, config: MIPROv2Config):
        self.config = config
        self.performance_history = defaultdict(list)
        
    def sample_candidates(self, 
                         candidates: List[str], 
                         scores: List[float],
                         iteration: int) -> List[str]:
        """Sample candidates using adaptive strategy."""
        if len(candidates) != len(scores):
            raise ValueError("Candidates and scores must have same length")
        
        # Calculate selection probabilities
        temperature = self._get_temperature(iteration)
        probabilities = self._softmax(scores, temperature)
        
        # Apply diversity penalty
        if self.config.diversity_penalty > 0:
            probabilities = self._apply_diversity_penalty(candidates, probabilities)
        
        # Sample candidates
        num_samples = min(self.config.top_k_candidates, len(candidates))
        selected_indices = np.random.choice(
            len(candidates), 
            size=num_samples, 
            replace=False, 
            p=probabilities
        )
        
        return [candidates[i] for i in selected_indices]
    
    def _get_temperature(self, iteration: int) -> float:
        """Get temperature for current iteration."""
        schedule = self.config.temperature_schedule
        if iteration < len(schedule):
            return schedule[iteration]
        return schedule[-1]
    
    def _softmax(self, scores: List[float], temperature: float) -> np.ndarray:
        """Apply softmax with temperature to scores."""
        scores_array = np.array(scores)
        exp_scores = np.exp((scores_array - np.max(scores_array)) / temperature)
        return exp_scores / np.sum(exp_scores)
    
    def _apply_diversity_penalty(self, 
                                candidates: List[str], 
                                probabilities: np.ndarray) -> np.ndarray:
        """Apply diversity penalty to encourage exploration."""
        # Simple diversity metric based on string similarity
        diversity_scores = []
        
        for i, candidate in enumerate(candidates):
            similarity_sum = 0
            for j, other in enumerate(candidates):
                if i != j:
                    # Simple word overlap similarity
                    words_i = set(candidate.lower().split())
                    words_j = set(other.lower().split())
                    if words_i and words_j:
                        similarity = len(words_i & words_j) / len(words_i | words_j)
                        similarity_sum += similarity
            
            diversity_score = 1.0 - (similarity_sum / max(1, len(candidates) - 1))
            diversity_scores.append(diversity_score)
        
        # Apply diversity penalty
        diversity_array = np.array(diversity_scores)
        adjusted_probs = probabilities * (1 + self.config.diversity_penalty * diversity_array)
        
        return adjusted_probs / np.sum(adjusted_probs)


class MIPROv2Optimizer(BaseOptimizer):
    """
    MIPROv2 (Multi-stage Instruction Proposal and Revision Optimizer v2).
    
    Advanced optimization for large datasets (300+ examples) with:
    - Multi-stage instruction optimization
    - Adaptive learning and sampling
    - Parallel evaluation and processing
    - Checkpointing and resumption
    - Quality control and outlier detection
    """
    
    def __init__(self, 
                 config: MIPROv2Config = None,
                 metric: Callable = None,
                 teacher_model: Any = None):
        if config is None:
            config = MIPROv2Config()
        super().__init__(config, metric, teacher_model)
        
        self.instruction_generator = InstructionGenerator(config)
        self.adaptive_sampler = AdaptiveSampler(config)
        self.optimization_state = {}
        
    def optimize(self, 
                 program: dspy.Module, 
                 trainset: List[dspy.Example], 
                 valset: List[dspy.Example] = None) -> OptimizationResult:
        """Optimize using MIPROv2 pipeline."""
        print("🚀 Starting MIPROv2 optimization...")
        
        # Prepare data
        train_examples = self._prepare_examples(trainset)
        val_examples = valset or train_examples[len(train_examples)//2:]
        
        if len(train_examples) < 50:
            print("⚠️ Warning: MIPROv2 works best with 300+ examples")
        
        # Initialize optimization state
        self.optimization_state = {
            "best_program": None,
            "best_score": 0.0,
            "best_instruction": "",
            "current_stage": 0,
            "patience_counter": 0,
            "total_evaluations": 0
        }
        
        try:
            # Check for existing checkpoint
            checkpoint_path = Path(self.config.checkpoint_dir) / "miprov2_checkpoint.json"
            if checkpoint_path.exists() and self.config.enable_checkpointing:
                self._load_checkpoint(checkpoint_path)
            
            # Multi-stage optimization
            if self.config.enable_multi_stage:
                result = self._multi_stage_optimization(program, train_examples, val_examples)
            else:
                result = self._single_stage_optimization(program, train_examples, val_examples)
            
            self._log_optimization_step(
                "miprov2_complete", 
                result.optimization_score,
                {
                    "total_evaluations": self.optimization_state["total_evaluations"],
                    "final_stage": self.optimization_state["current_stage"],
                    "best_instruction": self.optimization_state["best_instruction"][:100]
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"MIPROv2 optimization failed: {e}")
            raise
    
    def _multi_stage_optimization(self, 
                                program: dspy.Module,
                                trainset: List[dspy.Example],
                                valset: List[dspy.Example]) -> OptimizationResult:
        """Run multi-stage optimization with early stopping."""
        
        for stage in range(3):  # Maximum 3 stages
            self.optimization_state["current_stage"] = stage
            print(f"🎯 Starting optimization stage {stage + 1}")
            
            # Adjust config for current stage
            stage_config = self._get_stage_config(stage)
            
            # Run stage optimization
            stage_result = self._optimize_stage(
                program, trainset, valset, stage_config
            )
            
            # Check improvement
            improvement = stage_result.optimization_score - self.optimization_state["best_score"]
            
            if improvement > self.config.improvement_threshold:
                # Significant improvement - continue
                self.optimization_state["best_score"] = stage_result.optimization_score
                self.optimization_state["best_program"] = stage_result.optimized_program
                self.optimization_state["patience_counter"] = 0
                
                print(f"✅ Stage {stage + 1} improved score by {improvement:.3f}")
                
            else:
                # No significant improvement
                self.optimization_state["patience_counter"] += 1
                print(f"⏸️ Stage {stage + 1} - no significant improvement")
                
                if self.optimization_state["patience_counter"] >= self.config.stage_patience:
                    print("🛑 Early stopping - no improvement across stages")
                    break
            
            # Save checkpoint
            if self.config.enable_checkpointing:
                self._save_checkpoint()
        
        return OptimizationResult(
            optimized_program=self.optimization_state["best_program"],
            optimization_score=self.optimization_state["best_score"],
            evaluation_results={"multi_stage_optimization": True},
            best_config=self.config.__dict__,
            optimization_history=self.optimization_history.copy()
        )
    
    def _single_stage_optimization(self, 
                                 program: dspy.Module,
                                 trainset: List[dspy.Example],
                                 valset: List[dspy.Example]) -> OptimizationResult:
        """Run single-stage optimization."""
        return self._optimize_stage(program, trainset, valset, self.config)
    
    def _optimize_stage(self, 
                       program: dspy.Module,
                       trainset: List[dspy.Example],
                       valset: List[dspy.Example],
                       config: MIPROv2Config) -> OptimizationResult:
        """Optimize a single stage."""
        
        # Generate initial instruction candidates
        initial_instructions = self.instruction_generator.generate_initial_instructions(
            program, trainset, config.num_instruction_candidates
        )
        
        current_instructions = initial_instructions
        best_program = program
        best_score = 0.0
        best_instruction = ""
        
        # Iterative instruction optimization
        for iteration in range(config.num_instruction_iterations):
            print(f"🔄 Iteration {iteration + 1}/{config.num_instruction_iterations}")
            
            # Evaluate instruction candidates
            if config.enable_parallel_evaluation and config.max_workers > 1:
                instruction_scores = self._evaluate_instructions_parallel(
                    current_instructions, program, trainset, valset
                )
            else:
                instruction_scores = self._evaluate_instructions_sequential(
                    current_instructions, program, trainset, valset
                )
            
            # Find best instruction in this iteration
            iteration_best_idx = np.argmax(instruction_scores)
            iteration_best_score = instruction_scores[iteration_best_idx]
            iteration_best_instruction = current_instructions[iteration_best_idx]
            
            # Update global best if improved
            if iteration_best_score > best_score:
                best_score = iteration_best_score
                best_instruction = iteration_best_instruction
                # Create program with best instruction
                best_program = self._create_program_with_instruction(
                    program, best_instruction
                )
                
                self._log_optimization_step(
                    f"iteration_{iteration}", 
                    best_score,
                    {"instruction_preview": best_instruction[:50]}
                )
            
            # Adaptive sampling for next iteration
            if iteration < config.num_instruction_iterations - 1:
                current_instructions = self.adaptive_sampler.sample_candidates(
                    current_instructions, instruction_scores, iteration
                )
                
                # Generate refined instructions
                refined_instructions = []
                for instruction in current_instructions:
                    idx = initial_instructions.index(instruction) if instruction in initial_instructions else 0
                    performance_data = {"score": instruction_scores[idx] if idx < len(instruction_scores) else 0.0}
                    refined = self.instruction_generator.refine_instruction(
                        instruction, performance_data, iteration
                    )
                    refined_instructions.append(refined)
                
                current_instructions = refined_instructions
        
        self.optimization_state["best_instruction"] = best_instruction
        
        return OptimizationResult(
            optimized_program=best_program,
            optimization_score=best_score,
            evaluation_results={"best_instruction": best_instruction},
            best_config=config.__dict__,
            optimization_history=self.optimization_history.copy()
        )
    
    def _evaluate_instructions_parallel(self, 
                                      instructions: List[str],
                                      program: dspy.Module,
                                      trainset: List[dspy.Example],
                                      valset: List[dspy.Example]) -> List[float]:
        """Evaluate instructions in parallel."""
        scores = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            futures = []
            
            for instruction in instructions:
                future = executor.submit(
                    self._evaluate_single_instruction,
                    instruction, program, trainset, valset
                )
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    score = future.result()
                    scores.append(score)
                    self.optimization_state["total_evaluations"] += 1
                except Exception as e:
                    logger.warning(f"Instruction evaluation failed: {e}")
                    scores.append(0.0)
        
        return scores
    
    def _evaluate_instructions_sequential(self, 
                                        instructions: List[str],
                                        program: dspy.Module,
                                        trainset: List[dspy.Example],
                                        valset: List[dspy.Example]) -> List[float]:
        """Evaluate instructions sequentially."""
        scores = []
        
        for instruction in instructions:
            try:
                score = self._evaluate_single_instruction(
                    instruction, program, trainset, valset
                )
                scores.append(score)
                self.optimization_state["total_evaluations"] += 1
            except Exception as e:
                logger.warning(f"Instruction evaluation failed: {e}")
                scores.append(0.0)
        
        return scores
    
    def _evaluate_single_instruction(self, 
                                   instruction: str,
                                   program: dspy.Module,
                                   trainset: List[dspy.Example],
                                   valset: List[dspy.Example]) -> float:
        """Evaluate a single instruction."""
        # Create program with instruction
        instructed_program = self._create_program_with_instruction(program, instruction)
        
        # Quick evaluation on subset
        eval_subset = random.sample(valset, min(10, len(valset)))
        results = self.evaluate_program(instructed_program, eval_subset, display_progress=False)
        
        score = results if isinstance(results, (int, float)) else results.get('score', 0.0)
        
        # Quality control - detect outliers
        if score > 1.0 or score < 0.0:
            logger.warning(f"Outlier score detected: {score}")
            return 0.0
        
        return score
    
    def _create_program_with_instruction(self, 
                                       program: dspy.Module, 
                                       instruction: str) -> dspy.Module:
        """Create a program copy with specified instruction."""
        # This is a simplified implementation
        # In practice, you'd need to properly set the instruction on the program
        program_copy = program.deepcopy() if hasattr(program, 'deepcopy') else program
        
        # Set instruction if the program supports it
        if hasattr(program_copy, 'set_instruction'):
            program_copy.set_instruction(instruction)
        elif hasattr(program_copy, 'signature'):
            if hasattr(program_copy.signature, 'instructions'):
                program_copy.signature.instructions = instruction
        
        return program_copy
    
    def _get_stage_config(self, stage: int) -> MIPROv2Config:
        """Get configuration for specific optimization stage."""
        config_copy = MIPROv2Config(**self.config.__dict__)
        
        if stage == 0:
            # Exploration stage
            config_copy.num_instruction_candidates = max(20, self.config.num_instruction_candidates // 2)
            config_copy.num_instruction_iterations = max(10, self.config.num_instruction_iterations // 2)
            config_copy.temperature_schedule = [1.2, 1.0, 0.8]
        elif stage == 1:
            # Refinement stage
            config_copy.num_instruction_candidates = self.config.num_instruction_candidates
            config_copy.num_instruction_iterations = self.config.num_instruction_iterations
            config_copy.temperature_schedule = [0.8, 0.6, 0.4]
        else:
            # Fine-tuning stage
            config_copy.num_instruction_candidates = min(10, self.config.num_instruction_candidates)
            config_copy.num_instruction_iterations = max(5, self.config.num_instruction_iterations // 3)
            config_copy.temperature_schedule = [0.4, 0.3, 0.2]
        
        return config_copy
    
    def _save_checkpoint(self):
        """Save optimization checkpoint."""
        if not self.config.enable_checkpointing:
            return
            
        checkpoint_data = {
            "optimization_state": self.optimization_state,
            "optimization_history": self.optimization_history,
            "config": self.config.__dict__
        }
        
        checkpoint_path = Path(self.config.checkpoint_dir) / "miprov2_checkpoint.json"
        with open(checkpoint_path, 'w') as f:
            json.dump(checkpoint_data, f, indent=2, default=str)
        
        print(f"💾 Checkpoint saved to {checkpoint_path}")
    
    def _load_checkpoint(self, checkpoint_path: Path):
        """Load optimization checkpoint."""
        try:
            with open(checkpoint_path, 'r') as f:
                checkpoint_data = json.load(f)
            
            self.optimization_state.update(checkpoint_data.get("optimization_state", {}))
            self.optimization_history = checkpoint_data.get("optimization_history", [])
            
            print(f"📂 Checkpoint loaded from {checkpoint_path}")
            
        except Exception as e:
            logger.warning(f"Failed to load checkpoint: {e}") 