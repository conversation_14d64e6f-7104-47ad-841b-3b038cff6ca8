"""
DSPy Reliability Wrapper using BestOfN and Refine for Enhanced Reliability.

Provides automatic validation and retry logic for DSPy modules
using the modern DSPy 2.6+ patterns to catch and handle bad outputs effectively.
"""

from typing import Dict, Any, Optional, Callable, Union
import dspy


class ReliableAgentWrapper(dspy.Module):
    """
    Wrapper that adds reliability to any DSPy agent using BestOfN and Refine.
    
    Features:
    - Automatic output validation using reward functions
    - BestOfN for trying multiple attempts with different temperatures
    - Refine for iterative improvement with feedback
    - Comprehensive error reporting
    - Agent interface compatibility for execute_task method
    """
    
    def __init__(self, 
                 base_agent: dspy.Module,
                 min_answer_length: int = 10,
                 confidence_threshold: float = 0.3,
                 max_attempts: int = 3,
                 use_refine: bool = True):
        super().__init__()
        self.base_agent = base_agent
        self.min_answer_length = min_answer_length
        self.confidence_threshold = confidence_threshold
        self.max_attempts = max_attempts
        self.use_refine = use_refine
        
        # Create reward function for quality validation
        self.reward_fn = self._create_reward_function()
        
        # Wrap agent with BestOfN or Refine
        if use_refine:
            self.reliable_agent = dspy.Refine(
                module=base_agent,
                N=max_attempts,
                reward_fn=self.reward_fn,
                threshold=0.8  # High threshold for quality
            )
        else:
            self.reliable_agent = dspy.BestOfN(
                module=base_agent,
                N=max_attempts,
                reward_fn=self.reward_fn,
                threshold=0.8
            )
        
        self.validation_stats = {
            "total_calls": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "avg_attempts": 0
        }
    
    def _create_reward_function(self):
        """Create reward function for output validation."""
        def reward_function(args, prediction):
            """Reward function that validates prediction quality."""
            try:
                # Get the main content field
                content = None
                if hasattr(prediction, 'answer'):
                    content = prediction.answer
                elif hasattr(prediction, 'output'):
                    content = prediction.output
                else:
                    # Try to get any string field
                    for attr in dir(prediction):
                        if not attr.startswith('_'):
                            val = getattr(prediction, attr)
                            if isinstance(val, str) and len(val) > 5:
                                content = val
                                break
                
                if content is None:
                    return 0.0
                
                content_str = str(content).strip()
                
                # Length validation
                if len(content_str) < self.min_answer_length:
                    return 0.1
                
                # Content quality checks
                low_quality_phrases = [
                    'i don\'t know', 'not sure', 'unclear', 'i cannot', 
                    'insufficient information', 'no data available',
                    'unable to determine', 'cannot provide'
                ]
                
                content_lower = content_str.lower()
                quality_penalty = 0.0
                for phrase in low_quality_phrases:
                    if phrase in content_lower:
                        quality_penalty += 0.2
                
                # Check for placeholder content
                placeholder_patterns = ['[specific topic]', '[insert', '{{', '}}', '<placeholder>']
                has_placeholders = any(pattern in content_str for pattern in placeholder_patterns)
                if has_placeholders:
                    return 0.1
                
                # Check confidence if available
                confidence_score = 1.0
                if hasattr(prediction, 'confidence'):
                    try:
                        conf = float(prediction.confidence)
                        if conf < self.confidence_threshold:
                            confidence_score = conf / self.confidence_threshold
                    except:
                        pass
                
                # Calculate final score
                base_score = min(1.0, len(content_str) / 100.0)  # Longer is generally better
                final_score = base_score * confidence_score * (1.0 - quality_penalty)
                
                return max(0.0, min(1.0, final_score))
                
            except Exception:
                return 0.0
        
        return reward_function
    
    def forward(self, **kwargs):
        """Execute agent with reliability enhancements."""
        self.validation_stats["total_calls"] += 1
        
        try:
            # Use the reliable agent (BestOfN or Refine)
            result = self.reliable_agent(**kwargs)
            
            # Check if result meets our standards
            score = self.reward_fn(kwargs, result)
            
            if score >= 0.8:
                self.validation_stats["successful_validations"] += 1
            else:
                self.validation_stats["failed_validations"] += 1
            
            return result
            
        except Exception as e:
            self.validation_stats["failed_validations"] += 1
            # Return a basic prediction with error info
            return dspy.Prediction(
                answer=f"Error in reliable execution: {str(e)}",
                error=str(e),
                confidence=0.0
            )
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics."""
        total = self.validation_stats["total_calls"]
        if total == 0:
            return {"status": "No calls made yet"}
        
        success_rate = self.validation_stats["successful_validations"] / total
        failure_rate = self.validation_stats["failed_validations"] / total
        
        return {
            "total_calls": total,
            "successful_validations": self.validation_stats["successful_validations"],
            "failed_validations": self.validation_stats["failed_validations"],
            "success_rate": success_rate,
            "failure_rate": failure_rate,
            "reliability_score": success_rate,
            "method": "Refine" if self.use_refine else "BestOfN"
        }

    async def execute_task(self, task):
        """
        Execute task using the reliable agent - compatibility with agent interface.
        
        This method provides compatibility with the agent interface expected by
        the enhanced specialist flows.
        """
        from ...core.interfaces.task_interface import TaskResult, TaskStatus
        
        self.validation_stats["total_calls"] += 1
        
        try:
            # Prepare inputs based on task structure
            if hasattr(task, 'spec'):
                # TaskSpec structure
                inputs = {
                    "query": task.spec.description,
                    "context": getattr(task.spec, 'context', {}),
                }
                task_id = task.spec.task_id
                task_type = task.spec.task_type
            else:
                # Simple task structure
                inputs = {
                    "query": str(task),
                    "context": {}
                }
                task_id = "reliable_task"
                task_type = "analysis"
            
            # Execute using the reliable agent
            result = self.reliable_agent(**inputs)
            
            # Check result quality
            score = self.reward_fn(inputs, result)
            
            if score >= 0.8:
                self.validation_stats["successful_validations"] += 1
            else:
                self.validation_stats["failed_validations"] += 1
            
            # Create proper output structure
            if hasattr(result, 'answer'):
                output = {
                    'answer': result.answer,
                    'confidence': score,
                    'method': 'reliable_dspy',
                    'validation_score': score
                }
            else:
                output = {
                    'answer': str(result),
                    'confidence': score,
                    'method': 'reliable_dspy',
                    'validation_score': score
                }
            
            # Add additional fields based on result structure
            for attr in ['sources', 'reasoning', 'knowledge_areas', 'methodology']:
                if hasattr(result, attr):
                    output[attr] = getattr(result, attr)
            
            return TaskResult(
                task_id=task_id,
                task_type=task_type,
                status=TaskStatus.COMPLETED,
                output=output,
                agent_id="reliable_wrapper"
            )
            
        except Exception as e:
            self.validation_stats["failed_validations"] += 1
            
            # Return error result
            return TaskResult(
                task_id=getattr(task, 'spec', {}).get('task_id', 'reliable_task'),
                task_type=getattr(task, 'spec', {}).get('task_type', 'analysis'),
                status=TaskStatus.FAILED,
                error=str(e),
                agent_id="reliable_wrapper"
            )


def make_agent_reliable(agent: dspy.Module, 
                       min_answer_length: int = 10,
                       confidence_threshold: float = 0.3,
                       max_attempts: int = 3,
                       use_refine: bool = True) -> 'ReliableAgentWrapper':
    """
    Convenience function to wrap any DSPy agent with reliability using BestOfN/Refine.
    
    Args:
        agent: DSPy module to wrap
        min_answer_length: Minimum required answer length
        confidence_threshold: Minimum confidence score
        max_attempts: Maximum retry attempts
        use_refine: Use Refine (True) or BestOfN (False)
        
    Returns:
        Wrapped agent with reliability enhancements
    """
    return ReliableAgentWrapper(
        base_agent=agent,
        min_answer_length=min_answer_length,
        confidence_threshold=confidence_threshold,
        max_attempts=max_attempts,
        use_refine=use_refine
    )


def make_agents_reliable(agents_dict: Dict[str, dspy.Module], 
                        **wrapper_kwargs) -> Dict[str, 'ReliableAgentWrapper']:
    """
    Wrap multiple agents with reliability using modern DSPy patterns.
    
    Args:
        agents_dict: Dictionary of agent name -> agent module
        **wrapper_kwargs: Arguments to pass to ReliableAgentWrapper
        
    Returns:
        Dictionary of wrapped agents
    """
    reliable_agents = {}
    for name, agent in agents_dict.items():
        reliable_agents[name] = make_agent_reliable(agent, **wrapper_kwargs)
        print(f"✅ Added reliability wrapper to {name} using modern DSPy patterns")
    
    return reliable_agents


# Reward function examples for different use cases
def create_factual_consistency_reward():
    """Create reward function for factual consistency."""
    def factual_reward(args, prediction):
        content = getattr(prediction, 'answer', getattr(prediction, 'output', ''))
        content_str = str(content).lower()
        
        # Check for contradictory statements
        contradictory_pairs = [
            ('yes', 'no'),
            ('true', 'false'),
            ('always', 'never'),
            ('increase', 'decrease')
        ]
        
        penalty = 0.0
        for word1, word2 in contradictory_pairs:
            if word1 in content_str and word2 in content_str:
                penalty += 0.3
        
        return max(0.0, 1.0 - penalty)
    
    return factual_reward


def create_source_citation_reward():
    """Create reward function for source citations."""
    def citation_reward(args, prediction):
        if hasattr(prediction, 'sources'):
            sources = prediction.sources
            if sources and len(sources) > 0:
                return 1.0
            else:
                return 0.3  # Partial score for no sources
        return 0.8  # Default score when sources not expected
    
    return citation_reward 