"""
Base DSPy optimization module implementing modern optimization patterns.

Supports BootstrapFewShot, BootstrapFewShotWithRandomSearch, and MIPROv2
following 2025 DSPy best practices for different data sizes and requirements.
"""

from typing import List, Dict, Any, Optional, Callable, Union
from abc import ABC, abstractmethod
import random
from dataclasses import dataclass
import json
import datetime

import dspy
from dspy.teleprompt import BootstrapFewShot, BootstrapFewShotWithRandomSearch
from dspy.evaluate import Evaluate

# Import debug logging
try:
    from ...infrastructure.monitoring.debug_logger import get_debug_logger
    DEBUG_LOGGING_AVAILABLE = True
except ImportError:
    DEBUG_LOGGING_AVAILABLE = False


@dataclass
class OptimizationConfig:
    """Configuration for DSPy optimization."""
    max_bootstrapped_demos: int = 4
    max_labeled_demos: int = 16
    max_rounds: int = 1
    num_candidate_programs: int = 16
    num_threads: int = 6
    max_errors: int = 10
    teacher_settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.teacher_settings is None:
            self.teacher_settings = {}


@dataclass
class OptimizationResult:
    """Result of DSPy optimization."""
    optimized_program: dspy.Module
    optimization_score: float
    evaluation_results: Dict[str, Any]
    best_config: Dict[str, Any]
    optimization_history: List[Dict[str, Any]]


class BaseOptimizer(ABC):
    """
    Base class for DSPy optimizers.
    
    Provides common optimization patterns and evaluation metrics
    following 2025 DSPy best practices.
    """
    
    def __init__(self, 
                 config: OptimizationConfig = None,
                 metric: Callable = None,
                 teacher_model: Any = None):
        """
        Initialize the optimizer.
        
        Args:
            config: Optimization configuration
            metric: Evaluation metric function
            teacher_model: Teacher model for optimization (optional)
        """
        self.config = config or OptimizationConfig()
        self.metric = metric or self._default_metric
        self.teacher_model = teacher_model
        self.optimization_history = []
        
    def _default_metric(self, example, pred, trace=None) -> bool:
        """Default evaluation metric - simple answer matching."""
        if hasattr(example, 'answer') and hasattr(pred, 'answer'):
            return example.answer.lower().strip() == pred.answer.lower().strip()
        elif hasattr(example, 'output') and hasattr(pred, 'output'):
            return example.output.lower().strip() == pred.output.lower().strip()
        else:
            # Fallback - string representation matching
            return str(example).lower().strip() == str(pred).lower().strip()
    
    @abstractmethod
    def optimize(self, 
                 program: dspy.Module, 
                 trainset: List[dspy.Example], 
                 valset: List[dspy.Example] = None) -> OptimizationResult:
        """Optimize the DSPy program."""
        pass
    
    def evaluate_program(self, 
                        program: dspy.Module, 
                        dataset: List[dspy.Example],
                        display_progress: bool = True) -> Dict[str, Any]:
        """Evaluate a program on a dataset."""
        evaluate = Evaluate(
            devset=dataset,
            metric=self.metric,
            num_threads=self.config.num_threads,
            display_progress=display_progress,
            display_table=False
        )
        
        return evaluate(program)
    
    def _log_optimization_step(self, 
                              step: str, 
                              score: float, 
                              details: Dict[str, Any] = None):
        """Log an optimization step."""
        log_entry = {
            "step": step,
            "score": score,
            "timestamp": str(datetime.datetime.now()),
            "details": details or {}
        }
        self.optimization_history.append(log_entry)
        print(f"📊 Optimization Step: {step} - Score: {score:.3f}")
    
    def _prepare_examples(self, examples: List[dspy.Example]) -> List[dspy.Example]:
        """Prepare and validate examples for optimization."""
        if not examples:
            raise ValueError("No examples provided for optimization")
        
        # Shuffle examples for better optimization
        prepared = examples.copy()
        random.shuffle(prepared)
        
        # Validate example format
        for i, example in enumerate(prepared[:5]):  # Check first 5 examples
            if not isinstance(example, dspy.Example):
                raise ValueError(f"Example {i} is not a dspy.Example instance")
        
        print(f"✅ Prepared {len(prepared)} examples for optimization")
        return prepared


class BootstrapOptimizer(BaseOptimizer):
    """
    BootstrapFewShot optimizer for small datasets (<10 examples).
    
    Uses basic few-shot learning with bootstrapped demonstrations.
    Ideal for rapid prototyping and small-scale optimization.
    """
    
    def optimize(self, 
                 program: dspy.Module, 
                 trainset: List[dspy.Example], 
                 valset: List[dspy.Example] = None) -> OptimizationResult:
        """Optimize using BootstrapFewShot."""
        print("🚀 Starting BootstrapFewShot optimization...")
        
        # Prepare data
        train_examples = self._prepare_examples(trainset)
        val_examples = valset or train_examples[:len(train_examples)//3] if len(train_examples) > 3 else train_examples
        
        # Set up teacher model if provided
        original_lm = None
        if self.teacher_model:
            original_lm = dspy.settings.lm
            dspy.settings.configure(lm=self.teacher_model)
        
        try:
            # Create optimizer
            optimizer = BootstrapFewShot(
                metric=self.metric,
                max_bootstrapped_demos=min(self.config.max_bootstrapped_demos, len(train_examples)),
                max_labeled_demos=min(self.config.max_labeled_demos, len(train_examples)),
                max_rounds=self.config.max_rounds,
                max_errors=self.config.max_errors
            )
            
            self._log_optimization_step(
                "bootstrap_init", 
                0.0, 
                {"trainset_size": len(train_examples), "valset_size": len(val_examples)}
            )
            
            # Compile the program
            optimized_program = optimizer.compile(program, trainset=train_examples)
            
            # Evaluate on validation set
            val_score = self.evaluate_program(optimized_program, val_examples)
            final_score = val_score if isinstance(val_score, (int, float)) else val_score.get('score', 0.0)
            
            self._log_optimization_step("bootstrap_complete", final_score)
            
            return OptimizationResult(
                optimized_program=optimized_program,
                optimization_score=final_score,
                evaluation_results={"validation_score": val_score},
                best_config=self.config.__dict__,
                optimization_history=self.optimization_history.copy()
            )
            
        finally:
            # Restore original language model
            if original_lm:
                dspy.settings.configure(lm=original_lm)


class RandomSearchOptimizer(BaseOptimizer):
    """
    BootstrapFewShotWithRandomSearch optimizer for medium datasets (50+ examples).
    
    Uses random search over hyperparameters with bootstrapped demonstrations.
    Better for datasets with sufficient examples for hyperparameter tuning.
    """
    
    def optimize(self, 
                 program: dspy.Module, 
                 trainset: List[dspy.Example], 
                 valset: List[dspy.Example] = None) -> OptimizationResult:
        """Optimize using BootstrapFewShotWithRandomSearch."""
        print("🎲 Starting BootstrapFewShotWithRandomSearch optimization...")
        
        # Prepare data
        train_examples = self._prepare_examples(trainset)
        val_examples = valset or train_examples[len(train_examples)//2:]
        
        if len(train_examples) < 10:
            print("⚠️ Warning: Random search works best with 50+ examples")
        
        # Set up teacher model if provided
        original_lm = None
        if self.teacher_model:
            original_lm = dspy.settings.lm
            dspy.settings.configure(lm=self.teacher_model)
        
        try:
            # Create optimizer
            optimizer = BootstrapFewShotWithRandomSearch(
                metric=self.metric,
                max_bootstrapped_demos=min(self.config.max_bootstrapped_demos, len(train_examples)//4),
                max_labeled_demos=min(self.config.max_labeled_demos, len(train_examples)//2),
                num_candidate_programs=min(self.config.num_candidate_programs, 8),
                num_threads=self.config.num_threads,
                max_errors=self.config.max_errors
            )
            
            self._log_optimization_step(
                "random_search_init", 
                0.0, 
                {
                    "trainset_size": len(train_examples), 
                    "valset_size": len(val_examples),
                    "num_candidates": self.config.num_candidate_programs
                }
            )
            
            # Compile the program
            optimized_program = optimizer.compile(program, trainset=train_examples, valset=val_examples)
            
            # Evaluate on validation set
            val_score = self.evaluate_program(optimized_program, val_examples)
            final_score = val_score if isinstance(val_score, (int, float)) else val_score.get('score', 0.0)
            
            self._log_optimization_step("random_search_complete", final_score)
            
            return OptimizationResult(
                optimized_program=optimized_program,
                optimization_score=final_score,
                evaluation_results={"validation_score": val_score},
                best_config=self.config.__dict__,
                optimization_history=self.optimization_history.copy()
            )
            
        finally:
            # Restore original language model
            if original_lm:
                dspy.settings.configure(lm=original_lm)


def get_optimizer(dataset_size: int, 
                  config: OptimizationConfig = None,
                  metric: Callable = None,
                  teacher_model: Any = None) -> BaseOptimizer:
    """
    Get the appropriate optimizer based on dataset size (2025 Best Practices).
    
    Updated strategy:
    - Start with BootstrapFewShot immediately (don't wait for large datasets)
    - Progress through optimizers as data grows
    - Focus on quality over quantity initially
    
    Args:
        dataset_size: Number of training examples
        config: Optimization configuration
        metric: Evaluation metric
        teacher_model: Teacher model for optimization
        
    Returns:
        Appropriate optimizer instance
    """
    # Initialize debug logging if available
    debug_logger = None
    if DEBUG_LOGGING_AVAILABLE:
        try:
            debug_logger = get_debug_logger()
        except Exception:
            pass  # Ignore debug logging errors
    
    if dataset_size < 50:  # Start optimization immediately, even with 0 examples
        optimizer_type = "BootstrapOptimizer"
        print(f"🔧 [DSPy-Optimizer] Dataset size: {dataset_size} - Using {optimizer_type} (immediate start)")
        
        if debug_logger:
            debug_logger.log_dspy_optimization(dataset_size, optimizer_type, True, True)
            
        return BootstrapOptimizer(config, metric, teacher_model)
    elif dataset_size < 200:  # Changed from 300 to 200 - faster progression  
        optimizer_type = "RandomSearchOptimizer"
        print(f"🔧 [DSPy-Optimizer] Dataset size: {dataset_size} - Using {optimizer_type}")
        
        if debug_logger:
            debug_logger.log_dspy_optimization(dataset_size, optimizer_type, True, True)
            
        return RandomSearchOptimizer(config, metric, teacher_model)
    else:
        print(f"🔧 [DSPy-Optimizer] Dataset size: {dataset_size} - Using MIPROv2Optimizer")
        try:
            from .mipro_v2_optimizer import MIPROv2Optimizer, MIPROv2Config
            # Convert base config to MIPROv2 config if needed
            if config and not isinstance(config, MIPROv2Config):
                mipro_config = MIPROv2Config(**config.__dict__)
            else:
                mipro_config = config or MIPROv2Config()
            
            optimizer_type = "MIPROv2Optimizer"
            if debug_logger:
                debug_logger.log_dspy_optimization(dataset_size, optimizer_type, True, True)
                
            return MIPROv2Optimizer(mipro_config, metric, teacher_model)
        except ImportError as e:
            error_msg = str(e)
            print(f"⚠️ [DSPy-Optimizer] MIPROv2 not available ({error_msg}), falling back to RandomSearchOptimizer")
            
            optimizer_type = "RandomSearchOptimizer_Fallback"
            if debug_logger:
                debug_logger.log_dspy_optimization(dataset_size, optimizer_type, True, False, f"MIPROv2 unavailable: {error_msg}")
                
            return RandomSearchOptimizer(config, metric, teacher_model) 