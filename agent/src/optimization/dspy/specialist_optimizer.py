"""
DSPy Specialist Optimizer

Handles compilation and optimization of DSPy ReAct specialists in the enhanced flow.
Provides safe compilation with fallback and compact debug logging.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path

import dspy
from .base_optimizer import get_optimizer, OptimizationConfig
from .training_data_collector import get_training_data_collector


class SpecialistOptimizer:
    """
    Manages DSPy compilation and optimization for ReAct specialists.
    
    Features:
    - Safe compilation with fallback to original modules
    - Compact debug logging
    - Integration with existing training data collection
    - Enhanced flow specific optimizations
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.compiled_specialists = {}
        self.optimization_enabled = self.config.get('dspy_optimization_enabled', True)
        self.training_collector = get_training_data_collector()
        
        print(f"🔧 [DSPy-Specialist] Optimizer initialized, enabled: {self.optimization_enabled}")
    
    async def compile_specialist(self, 
                               specialist_module: dspy.Module,
                               specialist_name: str,
                               context: Dict[str, Any] = None) -> dspy.Module:
        """
        Compile a DSPy specialist with available training data.
        
        Args:
            specialist_module: The DSPy module to compile
            specialist_name: Name for logging and caching
            context: Additional context for optimization
            
        Returns:
            Compiled module or original if compilation fails
        """
        if not self.optimization_enabled:
            print(f"🔧 [DSPy-Specialist] Optimization disabled for {specialist_name}")
            return specialist_module
        
        # Check if already compiled and cached
        cache_key = f"{specialist_name}_{hash(str(context))}"
        if cache_key in self.compiled_specialists:
            print(f"🔧 [DSPy-Specialist] Using cached compiled {specialist_name}")
            return self.compiled_specialists[cache_key]
        
        try:
            print(f"🔧 [DSPy-Specialist] Compiling {specialist_name}...")
            
            # Get training data
            stats = await self.training_collector.get_dataset_stats()
            print(f"🔧 [DSPy-Specialist] Training data: {stats['total_examples']} examples, quality: {stats['avg_quality']:.2f}")
            
            # Get optimizer based on available data
            optimizer = get_optimizer(
                dataset_size=stats['total_examples'],
                config=OptimizationConfig(
                    max_bootstrapped_demos=min(4, stats['total_examples']),
                    max_labeled_demos=min(8, stats['total_examples']),
                    num_threads=2  # Conservative for specialists
                ),
                metric=self._specialist_metric
            )
            
            if optimizer is None:
                print(f"🔧 [DSPy-Specialist] No optimizer available for {specialist_name} (need training data)")
                return specialist_module
            
            # Get training examples
            training_examples = await self.training_collector.get_training_examples(
                min_quality=0.5,  # Lower threshold for specialists
                limit=50
            )
            
            if len(training_examples) < 3:
                print(f"🔧 [DSPy-Specialist] Insufficient examples for {specialist_name}: {len(training_examples)}")
                return specialist_module
            
            # Convert to DSPy format
            dspy_examples = [ex.to_dspy_example() for ex in training_examples[:20]]  # Limit for specialists
            
            print(f"🔧 [DSPy-Specialist] Optimizing {specialist_name} with {len(dspy_examples)} examples")
            
            # Compile the specialist
            result = optimizer.optimize(
                program=specialist_module,
                trainset=dspy_examples,
                valset=dspy_examples[-5:] if len(dspy_examples) > 5 else dspy_examples
            )
            
            if result and result.optimized_program:
                compiled_module = result.optimized_program
                self.compiled_specialists[cache_key] = compiled_module
                
                print(f"✅ [DSPy-Specialist] Compiled {specialist_name} successfully, score: {result.optimization_score:.2f}")
                return compiled_module
            else:
                print(f"⚠️ [DSPy-Specialist] Compilation failed for {specialist_name}, using original")
                return specialist_module
                
        except Exception as e:
            print(f"❌ [DSPy-Specialist] Compilation error for {specialist_name}: {str(e)[:100]}...")
            return specialist_module
    
    def _specialist_metric(self, example, pred, trace=None) -> bool:
        """Simple metric for specialist evaluation."""
        try:
            # Simple relevance check - does prediction contain key terms from question?
            if hasattr(pred, 'answer') and hasattr(example, 'question'):
                answer = str(pred.answer).lower()
                question = str(example.question).lower()
                
                # Basic keyword overlap
                question_words = set(question.split())
                answer_words = set(answer.split())
                overlap = len(question_words.intersection(answer_words))
                
                return overlap > 0 and len(answer) > 10
            
            return True  # Fallback: assume success
        except Exception:
            return False
    
    async def collect_specialist_example(self,
                                       specialist_name: str,
                                       query: str,
                                       result: Dict[str, Any],
                                       session_id: str = None) -> str:
        """Collect training example from specialist execution."""
        try:
            example_id = await self.training_collector.collect_training_example(
                session_id=session_id or str(uuid.uuid4()),
                original_query=query,
                optimized_query=query,  # Specialists don't modify queries
                workflow_result=result,
                context={
                    'specialist_name': specialist_name,
                    'specialist_type': 'ReAct',
                    'enhanced_flow': True
                },
                quality_metrics={
                    'answer_quality_score': result.get('confidence', 0.8),
                    'specialist_performance': 1.0 if result.get('success', True) else 0.0
                }
            )
            
            print(f"🔧 [DSPy-Specialist] Collected example for {specialist_name}: {example_id[:8]}...")
            return example_id
            
        except Exception as e:
            print(f"⚠️ [DSPy-Specialist] Failed to collect example for {specialist_name}: {str(e)[:100]}...")
            return ""
    
    def get_compilation_stats(self) -> Dict[str, Any]:
        """Get statistics about compiled specialists."""
        return {
            'compiled_specialists': len(self.compiled_specialists),
            'specialist_names': list(self.compiled_specialists.keys()),
            'optimization_enabled': self.optimization_enabled
        }


# Global instance for enhanced flow
_specialist_optimizer = None

def get_specialist_optimizer(config: Dict[str, Any] = None) -> SpecialistOptimizer:
    """Get or create the global specialist optimizer."""
    global _specialist_optimizer
    if _specialist_optimizer is None:
        _specialist_optimizer = SpecialistOptimizer(config)
    return _specialist_optimizer

def initialize_specialist_optimizer(config: Dict[str, Any] = None) -> SpecialistOptimizer:
    """Initialize a new specialist optimizer."""
    global _specialist_optimizer
    _specialist_optimizer = SpecialistOptimizer(config)
    return _specialist_optimizer 