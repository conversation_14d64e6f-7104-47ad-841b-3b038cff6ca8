"""
Composite Evaluation Pipeline

This module provides the main AutomatedEvaluationPipeline class that orchestrates
all individual evaluators to provide comprehensive quality assessment of DSPy workflow results.

Features:
- Multi-dimensional evaluation (relevance, coherence, instruction following, tool usage)
- Configurable evaluation weights and thresholds
- Async evaluation support for performance
- Caching and optimization for repeated evaluations
- Integration with training data collection
"""

import asyncio
import json
import time
import hashlib
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime

from .decomposed_evaluators import EvaluatorFactory


@dataclass
class EvaluationConfig:
    """Configuration for evaluation pipeline."""
    enabled: bool = True
    
    # Metric configurations
    relevance_enabled: bool = True
    relevance_weight: float = 0.4
    relevance_threshold: float = 0.7
    
    coherence_enabled: bool = True
    coherence_weight: float = 0.3
    coherence_threshold: float = 0.8
    
    instruction_following_enabled: bool = True
    instruction_following_weight: float = 0.2
    instruction_following_threshold: float = 0.75
    
    tool_usage_enabled: bool = True
    tool_usage_weight: float = 0.1
    tool_usage_threshold: float = 0.6
    
    # Evaluation behavior
    timeout_seconds: float = 30.0
    enable_caching: bool = True
    cache_ttl_hours: float = 24.0
    
    # DSPy evaluator settings
    dspy_model: str = "gpt-4.1-mini"
    dspy_temperature: float = 0.1
    dspy_max_tokens: int = 10000

    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'EvaluationConfig':
        """Create EvaluationConfig from configuration dict."""
        # Handle both dict and SystemConfig objects
        if hasattr(config, 'get'):
            # It's a dictionary
            eval_config = config.get('evaluation', {})
            metrics = eval_config.get('metrics', {})
            dspy_eval = eval_config.get('dspy_evaluator', {})
        else:
            # It's likely a SystemConfig dataclass or similar
            eval_config = getattr(config, 'evaluation', {}) if hasattr(config, 'evaluation') else {}
            metrics = getattr(eval_config, 'metrics', {}) if hasattr(eval_config, 'metrics') else {}
            dspy_eval = getattr(eval_config, 'dspy_evaluator', {}) if hasattr(eval_config, 'dspy_evaluator') else {}

        # Helper function to safely get values from dict or object
        def safe_get(obj, key, default=None):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)

        return cls(
            enabled=safe_get(eval_config, 'enabled', True),

            # Relevance
            relevance_enabled=safe_get(safe_get(metrics, 'relevance', {}), 'enabled', True),
            relevance_weight=safe_get(safe_get(metrics, 'relevance', {}), 'weight', 0.4),
            relevance_threshold=safe_get(safe_get(metrics, 'relevance', {}), 'minimum_threshold', 0.7),

            # Coherence
            coherence_enabled=safe_get(safe_get(metrics, 'coherence', {}), 'enabled', True),
            coherence_weight=safe_get(safe_get(metrics, 'coherence', {}), 'weight', 0.3),
            coherence_threshold=safe_get(safe_get(metrics, 'coherence', {}), 'minimum_threshold', 0.8),

            # Instruction following
            instruction_following_enabled=safe_get(safe_get(metrics, 'instruction_following', {}), 'enabled', True),
            instruction_following_weight=safe_get(safe_get(metrics, 'instruction_following', {}), 'weight', 0.2),
            instruction_following_threshold=safe_get(safe_get(metrics, 'instruction_following', {}), 'minimum_threshold', 0.75),

            # Tool usage
            tool_usage_enabled=safe_get(safe_get(metrics, 'tool_usage_efficiency', {}), 'enabled', True),
            tool_usage_weight=safe_get(safe_get(metrics, 'tool_usage_efficiency', {}), 'weight', 0.1),
            tool_usage_threshold=safe_get(safe_get(metrics, 'tool_usage_efficiency', {}), 'minimum_threshold', 0.6),

            # DSPy settings
            dspy_model=safe_get(dspy_eval, 'model', 'gpt-4'),
            dspy_temperature=safe_get(dspy_eval, 'temperature', 0.1),
            dspy_max_tokens=safe_get(dspy_eval, 'max_tokens', 1000),

            # Behavior
            enable_caching=safe_get(dspy_eval, 'enable_caching', True)
        )


@dataclass
class EvaluationResult:
    """Comprehensive evaluation result."""
    # Core scores
    composite_score: float
    relevance_score: float = 0.0
    coherence_score: float = 0.0
    instruction_following_score: float = 0.0
    tool_efficiency_score: float = 0.0
    
    # Detailed feedback
    relevance_reasoning: str = ""
    coherence_issues: str = ""
    missed_requirements: str = ""
    tool_suggestions: str = ""
    
    # Metadata
    evaluation_time: float = 0.0
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    cached: bool = False
    
    # Raw results for debugging
    raw_results: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization."""
        return {
            'composite_score': self.composite_score,
            'relevance_score': self.relevance_score,
            'coherence_score': self.coherence_score,
            'instruction_following_score': self.instruction_following_score,
            'tool_efficiency_score': self.tool_efficiency_score,
            'relevance_reasoning': self.relevance_reasoning,
            'coherence_issues': self.coherence_issues,
            'missed_requirements': self.missed_requirements,
            'tool_suggestions': self.tool_suggestions,
            'evaluation_time': self.evaluation_time,
            'timestamp': self.timestamp,
            'cached': self.cached
        }


class AutomatedEvaluationPipeline:
    """Comprehensive evaluation pipeline with decomposed metrics."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize evaluation pipeline.
        
        Args:
            config: Configuration dictionary with evaluation settings
        """
        self.config = EvaluationConfig.from_config(config)
        self.evaluator_factory = EvaluatorFactory(config)
        
        # Cache for evaluation results
        self._cache: Dict[str, EvaluationResult] = {}
        
        print(f"✅ AutomatedEvaluationPipeline initialized (enabled: {self.config.enabled})")
    
    async def evaluate_comprehensive(self, 
                                   question: str,
                                   answer: str,
                                   context: Dict[str, Any],
                                   tools_used: List[str] = None) -> EvaluationResult:
        """
        Run comprehensive evaluation across all dimensions.
        
        Args:
            question: Original question/query
            answer: Generated answer
            context: Context dictionary with instructions, available tools, etc.
            tools_used: List of tools actually used
            
        Returns:
            EvaluationResult with comprehensive metrics
        """
        if not self.config.enabled:
            return EvaluationResult(composite_score=0.5)
        
        start_time = time.time()
        
        # Check cache first
        cache_key = self._generate_cache_key(question, answer, context, tools_used)
        if self.config.enable_caching and cache_key in self._cache:
            cached_result = self._cache[cache_key]
            cached_result.cached = True
            return cached_result
        
        try:
            # Run evaluations in parallel for performance
            evaluation_tasks = []
            
            # Relevance evaluation
            if self.config.relevance_enabled:
                task = self._evaluate_relevance_async(question, answer, context)
                evaluation_tasks.append(('relevance', task))
            
            # Coherence evaluation
            if self.config.coherence_enabled:
                task = self._evaluate_coherence_async(answer)
                evaluation_tasks.append(('coherence', task))
            
            # Instruction following evaluation
            if self.config.instruction_following_enabled and context.get('instructions'):
                task = self._evaluate_instruction_following_async(
                    context.get('instructions', ''), answer
                )
                evaluation_tasks.append(('instruction_following', task))
            
            # Tool usage evaluation
            if self.config.tool_usage_enabled and tools_used:
                task = self._evaluate_tool_usage_async(
                    context.get('available_tools', []), tools_used, str(0.5)
                )
                evaluation_tasks.append(('tool_usage', task))
            
            # Execute all evaluations with timeout
            evaluation_results = {}
            if evaluation_tasks:
                timeout = self.config.timeout_seconds
                completed_tasks = await asyncio.wait_for(
                    asyncio.gather(
                        *[task for _, task in evaluation_tasks],
                        return_exceptions=True
                    ),
                    timeout=timeout
                )
                
                # Process results
                for i, (metric_name, _) in enumerate(evaluation_tasks):
                    result = completed_tasks[i]
                    if isinstance(result, Exception):
                        print(f"⚠️ {metric_name} evaluation failed: {result}")
                        evaluation_results[metric_name] = {'score': 0.5, 'details': str(result)}
                    else:
                        evaluation_results[metric_name] = result
            
            # Calculate composite score
            composite_score = self._calculate_composite_score(evaluation_results)
            
            # Create result object
            result = EvaluationResult(
                composite_score=composite_score,
                relevance_score=evaluation_results.get('relevance', {}).get('score', 0.0),
                coherence_score=evaluation_results.get('coherence', {}).get('score', 0.0),
                instruction_following_score=evaluation_results.get('instruction_following', {}).get('score', 0.0),
                tool_efficiency_score=evaluation_results.get('tool_usage', {}).get('score', 0.0),
                relevance_reasoning=evaluation_results.get('relevance', {}).get('reasoning', ''),
                coherence_issues=evaluation_results.get('coherence', {}).get('issues', ''),
                missed_requirements=evaluation_results.get('instruction_following', {}).get('missed_requirements', ''),
                tool_suggestions=evaluation_results.get('tool_usage', {}).get('suggestions', ''),
                evaluation_time=time.time() - start_time,
                raw_results=evaluation_results
            )
            
            # Cache result
            if self.config.enable_caching:
                self._cache[cache_key] = result
            
            return result
            
        except asyncio.TimeoutError:
            print(f"⚠️ Evaluation timeout after {self.config.timeout_seconds}s")
            return EvaluationResult(
                composite_score=0.5,
                evaluation_time=time.time() - start_time
            )
        except Exception as e:
            print(f"⚠️ Evaluation pipeline failed: {e}")
            return EvaluationResult(
                composite_score=0.5,
                evaluation_time=time.time() - start_time
            )
    
    async def _evaluate_relevance_async(self, question: str, answer: str, 
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """Async relevance evaluation."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.evaluator_factory.evaluate_relevance,
            question, answer, json.dumps(context)
        )
    
    async def _evaluate_coherence_async(self, answer: str) -> Dict[str, Any]:
        """Async coherence evaluation."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.evaluator_factory.evaluate_coherence,
            answer
        )
    
    async def _evaluate_instruction_following_async(self, instructions: str, 
                                                  answer: str) -> Dict[str, Any]:
        """Async instruction following evaluation."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.evaluator_factory.evaluate_instruction_following,
            instructions, answer
        )
    
    async def _evaluate_tool_usage_async(self, available_tools: List[str],
                                       tools_used: List[str],
                                       answer_quality: str) -> Dict[str, Any]:
        """Async tool usage evaluation."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.evaluator_factory.evaluate_tool_usage,
            available_tools, tools_used, answer_quality
        )
    
    def _calculate_composite_score(self, evaluation_results: Dict[str, Any]) -> float:
        """Calculate weighted composite score from individual metrics."""
        weights = {
            'relevance': self.config.relevance_weight,
            'coherence': self.config.coherence_weight,
            'instruction_following': self.config.instruction_following_weight,
            'tool_usage': self.config.tool_usage_weight
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in evaluation_results:
                score = evaluation_results[metric].get('score', 0.0)
                total_score += score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_cache_key(self, question: str, answer: str, 
                          context: Dict[str, Any], tools_used: List[str] = None) -> str:
        """Generate cache key for evaluation result."""
        # Create deterministic hash of inputs
        content = {
            'question': question,
            'answer': answer,
            'context': context,
            'tools_used': tools_used or [],
            'config': {
                'relevance_enabled': self.config.relevance_enabled,
                'coherence_enabled': self.config.coherence_enabled,
                'instruction_following_enabled': self.config.instruction_following_enabled,
                'tool_usage_enabled': self.config.tool_usage_enabled
            }
        }
        
        content_str = json.dumps(content, sort_keys=True)
        return hashlib.md5(content_str.encode()).hexdigest()
    
    def get_evaluation_summary(self) -> Dict[str, Any]:
        """Get summary of evaluation pipeline status."""
        return {
            'enabled': self.config.enabled,
            'cache_size': len(self._cache),
            'metrics_enabled': {
                'relevance': self.config.relevance_enabled,
                'coherence': self.config.coherence_enabled,
                'instruction_following': self.config.instruction_following_enabled,
                'tool_usage': self.config.tool_usage_enabled
            },
            'weights': {
                'relevance': self.config.relevance_weight,
                'coherence': self.config.coherence_weight,
                'instruction_following': self.config.instruction_following_weight,
                'tool_usage': self.config.tool_usage_weight
            },
            'thresholds': {
                'relevance': self.config.relevance_threshold,
                'coherence': self.config.coherence_threshold,
                'instruction_following': self.config.instruction_following_threshold,
                'tool_usage': self.config.tool_usage_threshold
            }
        }
    
    def clear_cache(self):
        """Clear evaluation cache."""
        self._cache.clear()
        print("🧹 Evaluation cache cleared")


# Utility functions for easy integration
async def quick_evaluate(question: str, answer: str, context: Dict[str, Any] = None,
                       tools_used: List[str] = None, config: Dict[str, Any] = None) -> Dict[str, float]:
    """Quick evaluation with minimal setup."""
    pipeline = AutomatedEvaluationPipeline(config or {})
    result = await pipeline.evaluate_comprehensive(
        question, answer, context or {}, tools_used
    )
    
    return {
        'composite_score': result.composite_score,
        'relevance': result.relevance_score,
        'coherence': result.coherence_score,
        'instruction_following': result.instruction_following_score,
        'tool_efficiency': result.tool_efficiency_score
    }


def sync_evaluate(question: str, answer: str, context: Dict[str, Any] = None,
                 tools_used: List[str] = None, config: Dict[str, Any] = None) -> Dict[str, float]:
    """Synchronous evaluation wrapper."""
    return asyncio.run(quick_evaluate(question, answer, context, tools_used, config)) 