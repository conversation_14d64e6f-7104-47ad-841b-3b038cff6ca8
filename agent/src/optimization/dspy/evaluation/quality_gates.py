"""
Quality Gates and Thresholds

This module provides automated quality gates with escalation mechanisms for DSPy workflows.
Quality gates evaluate training data and workflow results against configurable thresholds
and trigger appropriate actions when quality standards are not met.

Features:
- Configurable quality thresholds per metric
- Quality level classification (excellent, good, acceptable, poor, unacceptable)
- Automated escalation actions (reprocessing, human review, training data marking)
- Integration with evaluation pipeline results
- Comprehensive logging and monitoring
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Callable
import asyncio
import json
from datetime import datetime

from .evaluation_pipeline import EvaluationResult


def _get_score_from_evaluation(evaluation_results, key: str, default: float = 0.0) -> float:
    """Helper to get scores from either EvaluationResult object or dict."""
    if hasattr(evaluation_results, key):
        return getattr(evaluation_results, key)
    elif isinstance(evaluation_results, dict):
        return evaluation_results.get(key, default)
    else:
        return default


class QualityLevel(Enum):
    """Quality level classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNACCEPTABLE = "unacceptable"


@dataclass
class QualityThresholds:
    """Quality thresholds for different metrics."""
    # Individual metric minimums
    relevance_min: float = 0.7
    coherence_min: float = 0.8
    instruction_following_min: float = 0.75
    tool_efficiency_min: float = 0.6
    composite_min: float = 0.7
    
    # Quality level thresholds
    excellent_threshold: float = 0.9
    good_threshold: float = 0.8
    acceptable_threshold: float = 0.7
    poor_threshold: float = 0.5

    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'QualityThresholds':
        """Create thresholds from configuration."""
        # Helper function to safely get values from dict or object
        def safe_get(obj, key, default=None):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)

        evaluation_config = safe_get(config, 'evaluation', {})
        gates_config = safe_get(evaluation_config, 'quality_gates', {})
        thresholds_config = safe_get(gates_config, 'thresholds', {})
        metrics_config = safe_get(evaluation_config, 'metrics', {})

        return cls(
            # Individual metrics
            relevance_min=safe_get(safe_get(metrics_config, 'relevance', {}), 'minimum_threshold', 0.7),
            coherence_min=safe_get(safe_get(metrics_config, 'coherence', {}), 'minimum_threshold', 0.8),
            instruction_following_min=safe_get(safe_get(metrics_config, 'instruction_following', {}), 'minimum_threshold', 0.75),
            tool_efficiency_min=safe_get(safe_get(metrics_config, 'tool_usage_efficiency', {}), 'minimum_threshold', 0.6),
            composite_min=safe_get(thresholds_config, 'minimum_composite', 0.7),

            # Quality levels
            excellent_threshold=safe_get(thresholds_config, 'excellent', 0.9),
            good_threshold=safe_get(thresholds_config, 'good', 0.8),
            acceptable_threshold=safe_get(thresholds_config, 'acceptable', 0.7),
            poor_threshold=safe_get(thresholds_config, 'poor', 0.5)
        )


@dataclass
class QualityGateResult:
    """Result of quality gate evaluation."""
    passed_gates: List[str]
    failed_gates: List[str]
    overall_quality: QualityLevel
    actions_triggered: List[str]
    recommendations: List[str]
    gate_details: Dict[str, Any]
    evaluation_timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            'passed_gates': self.passed_gates,
            'failed_gates': self.failed_gates,
            'overall_quality': self.overall_quality.value,
            'actions_triggered': self.actions_triggered,
            'recommendations': self.recommendations,
            'gate_details': self.gate_details,
            'evaluation_timestamp': self.evaluation_timestamp
        }


class EscalationHandler:
    """Handles escalation actions for failed quality gates."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize escalation handler with configuration."""
        self.config = config

        # Helper function to safely get values from dict or object
        def safe_get(obj, key, default=None):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)

        evaluation_config = safe_get(config, 'evaluation', {})
        gates_config = safe_get(evaluation_config, 'quality_gates', {})
        self.escalation_config = safe_get(gates_config, 'escalation', {})
        
        # Action handlers
        self.action_handlers = {
            'automatic_reprocessing': self._handle_automatic_reprocessing,
            'human_review_flagged': self._handle_human_review_flagging,
            'marked_for_training': self._handle_training_data_marking,
            'notification_webhook': self._handle_webhook_notification
        }
    
    async def handle_escalation(self, evaluation_results: Dict[str, Any],
                              context: Dict[str, Any], 
                              gate_results: QualityGateResult) -> List[str]:
        """Handle escalation actions for quality failures."""
        actions_taken = []
        
        # Check each configured escalation action
        for action, enabled in self.escalation_config.items():
            if enabled and action in self.action_handlers:
                try:
                    await self.action_handlers[action](evaluation_results, context, gate_results)
                    actions_taken.append(action)
                    print(f"✅ Escalation action completed: {action}")
                except Exception as e:
                    print(f"⚠️ Escalation action failed: {action} - {e}")
        
        return actions_taken
    
    async def _handle_automatic_reprocessing(self, evaluation_results: Dict[str, Any],
                                           context: Dict[str, Any], 
                                           gate_results: QualityGateResult):
        """Handle automatic reprocessing of failed results."""
        print("🔄 Automatic reprocessing triggered")
        
        # Log reprocessing request
        reprocessing_context = {
            'original_query': context.get('query', ''),
            'session_id': context.get('session_id', ''),
            'failed_gates': gate_results.failed_gates,
            'composite_score': _get_score_from_evaluation(evaluation_results, 'composite_score'),
            'timestamp': datetime.now().isoformat()
        }
        
        # Store reprocessing request (for future implementation)
        await self._log_escalation_action('automatic_reprocessing', reprocessing_context)
    
    async def _handle_human_review_flagging(self, evaluation_results: Dict[str, Any],
                                          context: Dict[str, Any], 
                                          gate_results: QualityGateResult):
        """Flag result for human review."""
        print("👤 Human review flagged")
        
        review_context = {
            'query': context.get('query', ''),
            'session_id': context.get('session_id', ''),
            'workflow_type': context.get('workflow_type', 'unknown'),
            'evaluation_summary': {
                'composite_score': _get_score_from_evaluation(evaluation_results, 'composite_score'),
                'failed_gates': gate_results.failed_gates,
                'overall_quality': gate_results.overall_quality.value
            },
            'flagged_timestamp': datetime.now().isoformat(),
            'priority': self._calculate_review_priority(gate_results)
        }
        
        await self._log_escalation_action('human_review_flagged', review_context)
    
    async def _handle_training_data_marking(self, evaluation_results: Dict[str, Any],
                                          context: Dict[str, Any], 
                                          gate_results: QualityGateResult):
        """Mark data for additional training."""
        print("📚 Marked for additional training")
        
        training_context = {
            'session_id': context.get('session_id', ''),
            'query': context.get('query', ''),
            'quality_issues': gate_results.failed_gates,
            'improvement_areas': gate_results.recommendations,
            'composite_score': _get_score_from_evaluation(evaluation_results, 'composite_score'),
            'marked_timestamp': datetime.now().isoformat(),
            'training_priority': self._calculate_training_priority(gate_results)
        }
        
        await self._log_escalation_action('marked_for_training', training_context)
    
    async def _handle_webhook_notification(self, evaluation_results: Dict[str, Any],
                                         context: Dict[str, Any], 
                                         gate_results: QualityGateResult):
        """Send webhook notification for quality issues."""
        webhook_url = self.escalation_config.get('notification_webhook')
        if not webhook_url:
            return
        
        print(f"📡 Sending webhook notification to {webhook_url}")
        
        # Prepare notification payload
        payload = {
            'event': 'quality_gate_failure',
            'timestamp': datetime.now().isoformat(),
            'session_id': context.get('session_id', ''),
            'workflow_type': context.get('workflow_type', 'unknown'),
            'quality_summary': {
                'overall_quality': gate_results.overall_quality.value,
                'composite_score': _get_score_from_evaluation(evaluation_results, 'composite_score'),
                'failed_gates': gate_results.failed_gates,
                'passed_gates': gate_results.passed_gates
            },
            'context': {
                'query': context.get('query', ''),
                'recommendations': gate_results.recommendations
            }
        }
        
        # Note: Webhook sending would be implemented here
        # For now, just log the payload
        await self._log_escalation_action('webhook_notification', payload)
    
    def _calculate_review_priority(self, gate_results: QualityGateResult) -> str:
        """Calculate priority level for human review."""
        if gate_results.overall_quality == QualityLevel.UNACCEPTABLE:
            return "high"
        elif gate_results.overall_quality == QualityLevel.POOR:
            return "medium"
        else:
            return "low"
    
    def _calculate_training_priority(self, gate_results: QualityGateResult) -> str:
        """Calculate priority level for training data collection."""
        failed_count = len(gate_results.failed_gates)
        if failed_count >= 3:
            return "high"
        elif failed_count >= 2:
            return "medium"
        else:
            return "low"
    
    async def _log_escalation_action(self, action: str, context: Dict[str, Any]):
        """Log escalation action for monitoring and analysis."""
        log_entry = {
            'action': action,
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        
        # For now, just print the log entry
        # In production, this would write to a proper logging system
        print(f"📋 Escalation log: {json.dumps(log_entry, indent=2)}")


class QualityGateSystem:
    """Quality gate system with automated escalation."""
    
    def __init__(self, thresholds: QualityThresholds, config: Dict[str, Any]):
        """
        Initialize quality gate system.

        Args:
            thresholds: Quality thresholds configuration
            config: Full system configuration
        """
        self.thresholds = thresholds
        self.config = config
        self.escalation_handler = EscalationHandler(config)

        # Helper function to safely get values from dict or object
        def safe_get(obj, key, default=None):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)

        # Quality gate configuration
        evaluation_config = safe_get(config, 'evaluation', {})
        self.gates_config = safe_get(evaluation_config, 'quality_gates', {})
        self.enabled = safe_get(self.gates_config, 'enabled', True)
        
        print(f"✅ QualityGateSystem initialized (enabled: {self.enabled})")
    
    async def evaluate_quality_gates(self, 
                                   evaluation_results: Dict[str, Any],
                                   context: Dict[str, Any]) -> QualityGateResult:
        """
        Evaluate quality gates and trigger actions.
        
        Args:
            evaluation_results: Results from evaluation pipeline
            context: Context dictionary with session info, query, etc.
            
        Returns:
            QualityGateResult with gate status and actions taken
        """
        if not self.enabled:
            return QualityGateResult(
                passed_gates=[],
                failed_gates=[],
                overall_quality=QualityLevel.ACCEPTABLE,
                actions_triggered=[],
                recommendations=[],
                gate_details={},
                evaluation_timestamp=datetime.now().isoformat()
            )
        
        passed_gates = []
        failed_gates = []
        gate_details = {}
        recommendations = []
        
        # Evaluate individual metric gates
        metrics_to_check = [
            ('relevance', self.thresholds.relevance_min),
            ('coherence', self.thresholds.coherence_min),
            ('instruction_following', self.thresholds.instruction_following_min),
            ('tool_efficiency', self.thresholds.tool_efficiency_min)
        ]
        
        for metric, threshold in metrics_to_check:
            if metric in evaluation_results:
                score = evaluation_results[metric]
                gate_details[metric] = {
                    'score': score,
                    'threshold': threshold,
                    'passed': score >= threshold
                }
                
                if score >= threshold:
                    passed_gates.append(metric)
                else:
                    failed_gates.append(metric)
                    recommendations.append(f"Improve {metric}: scored {score:.3f}, needs {threshold:.3f}")
        
        # Evaluate composite score gate
        composite_score = _get_score_from_evaluation(evaluation_results, 'composite_score')
        gate_details['composite'] = {
            'score': composite_score,
            'threshold': self.thresholds.composite_min,
            'passed': composite_score >= self.thresholds.composite_min
        }
        
        if composite_score >= self.thresholds.composite_min:
            passed_gates.append('composite')
        else:
            failed_gates.append('composite')
            recommendations.append(f"Improve overall quality: scored {composite_score:.3f}, needs {self.thresholds.composite_min:.3f}")
        
        # Determine overall quality level
        overall_quality = self._classify_quality_level(composite_score)
        
        # Create initial result
        gate_result = QualityGateResult(
            passed_gates=passed_gates,
            failed_gates=failed_gates,
            overall_quality=overall_quality,
            actions_triggered=[],
            recommendations=recommendations,
            gate_details=gate_details,
            evaluation_timestamp=datetime.now().isoformat()
        )
        
        # Trigger escalation if needed
        if failed_gates or composite_score < self.thresholds.composite_min:
            escalation_actions = await self.escalation_handler.handle_escalation(
                evaluation_results, context, gate_result
            )
            gate_result.actions_triggered = escalation_actions
        
        # Log quality gate results
        await self._log_quality_gate_evaluation(gate_result, context)
        
        return gate_result
    
    def _classify_quality_level(self, score: float) -> QualityLevel:
        """Classify quality level based on composite score."""
        if score >= self.thresholds.excellent_threshold:
            return QualityLevel.EXCELLENT
        elif score >= self.thresholds.good_threshold:
            return QualityLevel.GOOD
        elif score >= self.thresholds.acceptable_threshold:
            return QualityLevel.ACCEPTABLE
        elif score >= self.thresholds.poor_threshold:
            return QualityLevel.POOR
        else:
            return QualityLevel.UNACCEPTABLE
    
    async def _log_quality_gate_evaluation(self, gate_result: QualityGateResult, 
                                         context: Dict[str, Any]):
        """Log quality gate evaluation for monitoring."""
        log_entry = {
            'session_id': context.get('session_id', ''),
            'workflow_type': context.get('workflow_type', 'unknown'),
            'quality_level': gate_result.overall_quality.value,
            'passed_gates': len(gate_result.passed_gates),
            'failed_gates': len(gate_result.failed_gates),
            'actions_triggered': gate_result.actions_triggered,
            'timestamp': gate_result.evaluation_timestamp
        }
        
        print(f"📊 Quality Gate Evaluation: {json.dumps(log_entry, indent=2)}")
    
    def get_gate_summary(self) -> Dict[str, Any]:
        """Get summary of quality gate configuration."""
        return {
            'enabled': self.enabled,
            'thresholds': {
                'relevance_min': self.thresholds.relevance_min,
                'coherence_min': self.thresholds.coherence_min,
                'instruction_following_min': self.thresholds.instruction_following_min,
                'tool_efficiency_min': self.thresholds.tool_efficiency_min,
                'composite_min': self.thresholds.composite_min
            },
            'quality_levels': {
                'excellent': self.thresholds.excellent_threshold,
                'good': self.thresholds.good_threshold,
                'acceptable': self.thresholds.acceptable_threshold,
                'poor': self.thresholds.poor_threshold
            },
            'escalation_actions': {
                action: enabled for action, enabled in self.escalation_handler.escalation_config.items()
            }
        }


# Utility functions for easy integration
def create_quality_gates(config: Dict[str, Any]) -> Optional[QualityGateSystem]:
    """Create quality gate system from configuration."""
    try:
        thresholds = QualityThresholds.from_config(config)
        return QualityGateSystem(thresholds, config)
    except Exception as e:
        print(f"⚠️ Failed to create quality gates: {e}")
        return None


async def quick_quality_check(evaluation_results: Dict[str, Any],
                            context: Dict[str, Any] = None,
                            config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Quick quality gate check with minimal setup."""
    gates = create_quality_gates(config or {})
    if not gates:
        return {'overall_quality': 'acceptable', 'passed_gates': [], 'failed_gates': []}
    
    result = await gates.evaluate_quality_gates(evaluation_results, context or {})
    return result.to_dict() 