"""
Core interfaces for the multi-agent system.

This module defines the abstract contracts that all components must implement,
ensuring consistency and enabling dependency injection throughout the system.
"""

from .agent_interface import IAgent
from .task_interface import ITask, ITaskExecutor
from .tool_interface import ITool, IToolRegistry
from .flow_interface import IFlow, ITaskManagerFlow, ISpecialistFlow, IWriterFlow

__all__ = [
    "IAgent",
    "ITask",
    "ITaskExecutor",
    "ITool",
    "IToolRegistry",
    "IFlow",
    "ITaskManagerFlow",
    "ISpecialistFlow",
    "IWriterFlow",
]

# System interfaces and contracts 