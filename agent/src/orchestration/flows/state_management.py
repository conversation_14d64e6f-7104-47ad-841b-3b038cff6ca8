from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

class TaskResult(BaseModel):
    """Structured task result with metadata."""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str
    result_data: Dict[str, Any] = Field(default_factory=dict)
    success: bool = True
    execution_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.now)
    error_message: Optional[str] = None

class AgentMetrics(BaseModel):
    """Performance metrics for agent execution."""
    agent_id: str
    total_tasks: int = 0
    successful_tasks: int = 0
    avg_execution_time: float = 0.0
    last_activity: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_tasks == 0:
            return 0.0
        return self.successful_tasks / self.total_tasks

class WorkflowState(BaseModel):
    """
    Comprehensive workflow state with persistence capabilities.
    Compatible with CrewAI Flows 2025 state management.
    """
    # Core workflow data
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    query: str = ""
    complexity_score: float = 0.0
    required_capabilities: List[str] = Field(default_factory=list)
    estimated_time: float = 0.0
    parallel_eligible: bool = False
    
    # Execution tracking
    workflow_started: datetime = Field(default_factory=datetime.now)
    workflow_completed: bool = False
    current_stage: str = "initialization"
    
    # Results and metrics
    final_result: Optional[str] = None
    task_results: List[TaskResult] = Field(default_factory=list)
    performance_metrics: Dict[str, Any] = Field(default_factory=dict)
    agent_metrics: Dict[str, AgentMetrics] = Field(default_factory=dict)
    
    # Error handling
    errors_encountered: List[str] = Field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    
    def add_task_result(self, result: TaskResult):
        """Add a task result to the workflow state."""
        self.task_results.append(result)
    
    def add_error(self, error_message: str, agent_type: str = "Unknown"):
        """Add an error to the workflow state."""
        error_entry = f"[{datetime.now().isoformat()}] {agent_type}: {error_message}"
        self.errors_encountered.append(error_entry)
    
    def update_agent_metrics(self, agent_id: str, metrics: AgentMetrics):
        """Update metrics for a specific agent."""
        self.agent_metrics[agent_id] = metrics
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """Get a summary of the workflow execution."""
        return {
            "session_id": self.session_id,
            "query": self.query,
            "complexity_score": self.complexity_score,
            "workflow_completed": self.workflow_completed,
            "total_tasks": len(self.task_results),
            "successful_tasks": sum(1 for r in self.task_results if r.success),
            "total_errors": len(self.errors_encountered),
            "execution_time": (datetime.now() - self.workflow_started).total_seconds() if not self.workflow_completed else None,
            "performance_metrics": self.performance_metrics
        }


class StateManager:
    """Manages workflow states and session history."""
    
    def __init__(self):
        self.active_sessions: Dict[str, WorkflowState] = {}
        self.session_history: List[WorkflowState] = []
        self.performance_cache: Dict[str, Any] = {}
    
    def create_session(self, workflow_type: str = "standard") -> str:
        """Create a new workflow session."""
        session_id = str(uuid.uuid4())
        state = WorkflowState(
            session_id=session_id,
            current_stage="initialized",
            complexity_score=1.0 if workflow_type == "standard" else 5.0
        )
        self.active_sessions[session_id] = state
        return session_id
    
    def get_session_state(self, session_id: str) -> Optional[WorkflowState]:
        """Get the state of a specific session."""
        return self.active_sessions.get(session_id)
    
    def add_task_result(self, session_id: str, result: TaskResult):
        """Add a task result to a specific session."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].add_task_result(result)
    
    def update_stage(self, session_id: str, stage: str):
        """Update the current stage of a workflow."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].current_stage = stage
    
    def complete_session(self, session_id: str):
        """Mark a session as complete and move to history."""
        if session_id in self.active_sessions:
            state = self.active_sessions[session_id]
            state.workflow_completed = True
            self.session_history.append(state)
            del self.active_sessions[session_id] 