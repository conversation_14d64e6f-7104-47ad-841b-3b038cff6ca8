"""
Enhanced Main workflow implementation using CrewAI 2025 Flows + Quick Wins.

Orchestrates the entire multi-agent system with hierarchical task management,
parallel specialist execution with enhanced capabilities, and final synthesis.

Features integrated Quick Wins:
- ColBERTv2 professional-grade search
- Mathematical computation and code execution
- ReAct reasoning and action capabilities  
- Reliability wrappers with automatic validation
- Professional CrewAI tools integration
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime

from crewai.flow.flow import Flow, listen, start, and_, or_
from pydantic import BaseModel, Field

from src.core.models.state_models import WorkflowState, TaskPhase, ResearchResult, LibraryResult, AnalysisResult
from src.core.interfaces.flow_interface import IFlow, FlowStatus
from src.orchestration.flows.task_manager_flow import TaskManagerFlow
from src.orchestration.flows.specialist_flows import EnhancedResearcherFlow, EnhancedLibrarianFlow, EnhancedDataProcessorFlow
from src.orchestration.flows.writer_flow import WriterFlow


class MainWorkflowFlow(Flow[WorkflowState]):
    """
    Enhanced Main workflow orchestrating the multi-agent system with Quick Wins.
    
    Implements hierarchical task management with parallel enhanced specialist execution
    following CrewAI 2025 Flows patterns + Phase 0 Quick Wins integration.
    
    Enhanced Features:
    - ColBERTv2 professional-grade search capabilities
    - Mathematical computation and code execution  
    - ReAct reasoning for step-by-step problem solving
    - Reliability wrappers for automatic retry and validation
    - Professional CrewAI tools for advanced functionality
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        
        # Initialize specialized flows with ENHANCED CAPABILITIES
        self._task_manager_flow = TaskManagerFlow()
        self._researcher_flow = EnhancedResearcherFlow()  # Quick Wins: ColBERTv2 + ReAct
        self._librarian_flow = EnhancedLibrarianFlow()    # Quick Wins: Math + ReAct  
        self._data_processor_flow = EnhancedDataProcessorFlow()  # Quick Wins: Math + ReAct
        self._writer_flow = WriterFlow()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    @property
    def flow_id(self) -> str:
        return self._flow_id
    
    @property
    def status(self) -> FlowStatus:
        return self._status
    
    @start()
    def initialize_workflow(self):
        """Initialize the enhanced workflow with user query and setup."""
        print("🚀 Starting Enhanced Multi-Agent Workflow System with Quick Wins")
        print("=" * 70)
        print("🎯 Enhanced Features Active:")
        print("   ✅ ColBERTv2 Professional-grade Search")
        print("   ✅ Mathematical Computation & Code Execution")
        print("   ✅ ReAct Reasoning & Action Capabilities")
        print("   ✅ Reliability Wrappers & Auto-validation")
        print("   ✅ Professional CrewAI Tools Integration")
        print("=" * 70)
        
        # Get query from state (set by main.py) or use default
        query = self.state.original_query
        if not query:
            query = "What are the latest developments in renewable energy technology?"
        
        # Initialize workflow state
        self.state.original_query = query
        self.state.processed_query = query
        self.state.workflow_id = self._flow_id
        self.state.current_phase = TaskPhase.INITIALIZATION
        
        print(f"\n🎯 Query: {query}")
        print(f"🆔 Workflow ID: {self._flow_id}")
        print(f"🔧 Enhancement Level: Phase 0 Quick Wins Integrated")
        
        self._status = FlowStatus.RUNNING
        return {"query": query, "workflow_id": self._flow_id, "enhancements": "quick_wins_active"}
    
    @listen(initialize_workflow)
    async def task_planning_phase(self):
        """Create execution plan using TaskManager flow."""
        print(f"\n📋 PHASE: Enhanced Task Planning")
        print("-" * 50)
        
        self.state.advance_phase(TaskPhase.PLANNING)
        
        try:
            # Run TaskManager flow to create execution plan
            planning_result = await self._task_manager_flow.kickoff_async(inputs={
                "query": self.state.original_query,
                "context": self.state.context
            })
            
            # Update state with plan
            if hasattr(planning_result, 'execution_plan'):
                self.state.execution_plan = planning_result.execution_plan
            
            print(f"✅ Enhanced execution plan created with {len(self.state.execution_plan)} steps")
            return {"planning_complete": True, "plan": self.state.execution_plan}
            
        except Exception as e:
            error_msg = f"Enhanced task planning failed: {str(e)}"
            self.state.add_error(error_msg, "TaskManager")
            print(f"❌ {error_msg}")
            return {"planning_complete": False, "error": error_msg}
    
    @listen(task_planning_phase)
    async def parallel_enhanced_specialists_phase(self):
        """Execute enhanced specialist flows in parallel with Quick Wins capabilities."""
        print(f"\n🤖 PHASE: Parallel Enhanced Specialist Execution")
        print("-" * 60)
        print("🔧 Activating Enhanced Capabilities:")
        print("   🔍 Enhanced Researcher: ColBERTv2 + ReAct Search")
        print("   📚 Enhanced Librarian: Math Computation + ReAct Knowledge")
        print("   📊 Enhanced Data Processor: Statistical Analysis + ReAct Processing")
        print("-" * 60)
        
        self.state.advance_phase(TaskPhase.RESEARCH)
        
        # Prepare context for all specialists
        specialist_context = {
            "query": self.state.original_query,
            "execution_plan": self.state.execution_plan,
            "workflow_id": self.state.workflow_id,
            "enhanced_mode": True,  # Signal enhanced capabilities
            "quick_wins_active": True
        }
        
        try:
            print("🔄 Starting parallel execution of enhanced specialists...")
            
            # Execute all enhanced specialists in parallel
            specialist_tasks = [
                self._researcher_flow.kickoff_async(inputs=specialist_context),
                self._librarian_flow.kickoff_async(inputs=specialist_context),
                self._data_processor_flow.kickoff_async(inputs=specialist_context)
            ]
            
            # Wait for all specialists to complete
            results = await asyncio.gather(*specialist_tasks, return_exceptions=True)
            
            # Process results - kickoff_async returns the final method output
            researcher_result, librarian_result, processor_result = results
            
            # Handle Enhanced Researcher result (kickoff returns structured dict)
            if isinstance(researcher_result, Exception):
                self.state.add_error(f"Enhanced Researcher failed: {str(researcher_result)}", "EnhancedResearcher")
                print(f"❌ Enhanced Researcher failed: {str(researcher_result)}")
            elif researcher_result and isinstance(researcher_result, dict):
                # Process the returned structured data
                if researcher_result.get('success'):
                    flow_results = researcher_result.get('results', [])
                    for result_dict in flow_results:
                        # Convert dict to ResearchResult object
                        research_result = ResearchResult(**result_dict)
                        self.state.add_research_result(research_result)
                    
                    # Show enhancement statistics
                    enhanced_agents = researcher_result.get('enhanced_agents_used', [])
                    reliability_stats = researcher_result.get('reliability_stats', {})
                    tools_performance = researcher_result.get('tools_performance', {})
                    
                    print(f"✅ Enhanced Research completed: {len(flow_results)} results")
                    print(f"   🔧 Enhanced agents used: {len(enhanced_agents)} ({', '.join(enhanced_agents)})")
                    print(f"   📈 Tools performance: {tools_performance}")
                    if reliability_stats:
                        print(f"   🛡️ Reliability stats: {reliability_stats.get('average_confidence', 'N/A')}")
                else:
                    print("⚠️ Enhanced Researcher completed but returned no results")
            else:
                print(f"⚠️ Enhanced Researcher returned unexpected format: {type(researcher_result)}")
            
            # Handle Enhanced Librarian result (kickoff returns structured dict)
            if isinstance(librarian_result, Exception):
                self.state.add_error(f"Enhanced Librarian failed: {str(librarian_result)}", "EnhancedLibrarian")
                print(f"❌ Enhanced Librarian failed: {str(librarian_result)}")
            elif librarian_result and isinstance(librarian_result, dict):
                # Process the returned structured data
                if librarian_result.get('success'):
                    flow_results = librarian_result.get('results', [])
                    for result_dict in flow_results:
                        # Convert dict to LibraryResult object
                        library_result = LibraryResult(**result_dict)
                        self.state.add_library_result(library_result)
                    
                    # Show enhancement statistics
                    enhanced_agents = librarian_result.get('enhanced_agents_used', [])
                    reliability_stats = librarian_result.get('reliability_stats', {})
                    tools_performance = librarian_result.get('tools_performance', {})
                    
                    print(f"✅ Enhanced Library search completed: {len(flow_results)} results")
                    print(f"   🔧 Enhanced agents used: {len(enhanced_agents)} ({', '.join(enhanced_agents)})")
                    print(f"   📈 Tools performance: {tools_performance}")
                    if reliability_stats:
                        print(f"   🛡️ Reliability stats: {reliability_stats.get('average_relevance', 'N/A')}")
                else:
                    print("⚠️ Enhanced Librarian completed but returned no results")
            else:
                print(f"⚠️ Enhanced Librarian returned unexpected format: {type(librarian_result)}")
            
            # Handle Enhanced Data Processor result (kickoff returns structured dict)
            if isinstance(processor_result, Exception):
                self.state.add_error(f"Enhanced Data Processor failed: {str(processor_result)}", "EnhancedDataProcessor")
                print(f"❌ Enhanced Data Processor failed: {str(processor_result)}")
            elif processor_result and isinstance(processor_result, dict):
                # Process the returned structured data
                if processor_result.get('success'):
                    flow_results = processor_result.get('results', [])
                    for result_dict in flow_results:
                        # Convert dict to AnalysisResult object
                        analysis_result = AnalysisResult(**result_dict)
                        self.state.add_analysis_result(analysis_result)
                    
                    # Show enhancement statistics
                    enhanced_agents = processor_result.get('enhanced_agents_used', [])
                    reliability_stats = processor_result.get('reliability_stats', {})
                    tools_performance = processor_result.get('tools_performance', {})
                    
                    print(f"✅ Enhanced Data analysis completed: {len(flow_results)} results")
                    print(f"   🔧 Enhanced agents used: {len(enhanced_agents)} ({', '.join(enhanced_agents)})")
                    print(f"   📈 Tools performance: {tools_performance}")
                    if reliability_stats:
                        print(f"   🛡️ Reliability stats: {reliability_stats.get('average_confidence', 'N/A')}")
                else:
                    print("⚠️ Enhanced Data Processor completed but returned no results")
            else:
                print(f"⚠️ Enhanced Data Processor returned unexpected format: {type(processor_result)}")
            
            specialists_success = (
                (isinstance(researcher_result, dict) and researcher_result.get('success', False)) or
                (isinstance(librarian_result, dict) and librarian_result.get('success', False)) or
                (isinstance(processor_result, dict) and processor_result.get('success', False))
            )
            
            print(f"\n🎯 Enhanced Specialists Summary:")
            print(f"   📊 Research results: {len(self.state.research_results)}")
            print(f"   📚 Library results: {len(self.state.library_results)}")
            print(f"   📈 Analysis results: {len(self.state.analysis_results)}")
            print(f"   ✅ Overall success: {specialists_success}")
            
            return {
                "specialists_complete": True,
                "research_count": len(self.state.research_results),
                "library_count": len(self.state.library_results),
                "analysis_count": len(self.state.analysis_results),
                "success": specialists_success,
                "enhanced_features_used": True
            }
            
        except Exception as e:
            error_msg = f"Parallel enhanced specialist execution failed: {str(e)}"
            self.state.add_error(error_msg, "EnhancedOrchestrator")
            print(f"❌ {error_msg}")
            return {"specialists_complete": False, "error": error_msg}
    
    @listen(parallel_enhanced_specialists_phase)
    async def enhanced_synthesis_phase(self):
        """Synthesize results from enhanced specialists using WriterFlow."""
        print(f"\n📝 PHASE: Enhanced Result Synthesis")
        print("-" * 50)
        
        self.state.advance_phase(TaskPhase.SYNTHESIS)
        
        # Collect all enhanced results for synthesis
        synthesis_context = {
            "query": self.state.original_query,
            "research_results": [r.dict() for r in self.state.research_results],
            "library_results": [l.dict() for l in self.state.library_results],
            "analysis_results": [a.dict() for a in self.state.analysis_results],
            "workflow_id": self.state.workflow_id,
            "enhanced_mode": True,
            "enhancement_summary": {
                "total_enhanced_agents": self._count_enhanced_agents(),
                "reliability_features_used": True,
                "professional_tools_used": True,
                "colbert_search_used": True,
                "math_computation_used": True,
                "react_reasoning_used": True
            }
        }
        
        try:
            print("🔄 Starting enhanced result synthesis...")
            
            # Run WriterFlow to synthesize all results
            synthesis_result = await self._writer_flow.kickoff_async(inputs=synthesis_context)
            
            # Update final answer - FIXED: correct attribute access
            if hasattr(synthesis_result, 'final_answer'):
                self.state.final_answer = synthesis_result.final_answer
            elif hasattr(self._writer_flow.state, 'final_answer'):
                self.state.final_answer = self._writer_flow.state.final_answer
            else:
                # Fallback: check if result is a dict with final_answer
                if isinstance(synthesis_result, dict) and 'final_answer' in synthesis_result:
                    self.state.final_answer = synthesis_result['final_answer']
                else:
                    # Last resort: use the raw result
                    self.state.final_answer = str(synthesis_result)
            
            # FIXED: Calculate proper completion metrics
            self._calculate_final_metrics()
            
            print("✅ Enhanced synthesis completed")
            return {"synthesis_complete": True, "enhanced_features_summarized": True}
            
        except Exception as e:
            error_msg = f"Enhanced synthesis failed: {str(e)}"
            self.state.add_error(error_msg, "EnhancedSynthesizer")
            print(f"❌ {error_msg}")
            return {"synthesis_complete": False, "error": error_msg}
    
    def _calculate_final_metrics(self):
        """Calculate and update final workflow metrics."""
        import time
        
        # Calculate execution time
        if hasattr(self.state, 'start_time'):
            total_time = (datetime.now() - self.state.start_time).total_seconds()
            self.state.metrics.total_execution_time = total_time
        
        # Calculate success rate based on results and errors
        total_tasks = len(self.state.research_results) + len(self.state.library_results) + len(self.state.analysis_results)
        successful_tasks = 0
        if self.state.research_results: successful_tasks += 1
        if self.state.library_results: successful_tasks += 1  
        if self.state.analysis_results: successful_tasks += 1
        
        if total_tasks > 0:
            self.state.metrics.success_rate = successful_tasks / 3.0  # 3 main specialist types
        else:
            self.state.metrics.success_rate = 0.0
            
        # Calculate answer quality score based on content length and results
        if self.state.final_answer:
            # Basic quality score based on answer length and number of results
            answer_length = len(self.state.final_answer)
            results_count = total_tasks
            
            # Quality score: 0.4 for having an answer + 0.3 for good length + 0.3 for multiple results
            quality_score = 0.4  # Base score for having an answer
            
            if answer_length > 500:  # Good detailed answer
                quality_score += 0.3
            elif answer_length > 200:  # Decent answer  
                quality_score += 0.2
            elif answer_length > 50:  # Basic answer
                quality_score += 0.1
                
            if results_count >= 3:  # All specialists contributed
                quality_score += 0.3
            elif results_count >= 2:  # Most specialists contributed
                quality_score += 0.2
            elif results_count >= 1:  # Some specialists contributed
                quality_score += 0.1
                
            self.state.answer_quality_score = min(quality_score, 1.0)
        else:
            self.state.answer_quality_score = 0.0
            
        # Update metrics error count
        self.state.metrics.error_count = len(self.state.errors)
        
        # Mark execution plan steps as completed if we have a final answer
        if self.state.final_answer and self.state.execution_plan:
            for step in self.state.execution_plan:
                if step.step_id not in self.state.completed_steps:
                    self.state.completed_steps.append(step.step_id)
                    
        print(f"📊 Final Metrics Calculated:")
        print(f"   ⏱️ Total time: {self.state.metrics.total_execution_time:.1f}s")
        print(f"   ✅ Success rate: {self.state.metrics.success_rate:.1%}")
        print(f"   🌟 Quality score: {self.state.answer_quality_score:.1%}")
        print(f"   📈 Completion: {self.state.get_completion_percentage():.1f}%")
    
    def _count_enhanced_agents(self) -> int:
        """Count total enhanced agents used across all flows."""
        total_enhanced = 0
        
        # Count from researcher flow
        if hasattr(self._researcher_flow.state, 'enhanced_agents_used'):
            total_enhanced += len(self._researcher_flow.state.enhanced_agents_used)
        
        # Count from librarian flow
        if hasattr(self._librarian_flow.state, 'enhanced_agents_used'):
            total_enhanced += len(self._librarian_flow.state.enhanced_agents_used)
        
        # Count from data processor flow
        if hasattr(self._data_processor_flow.state, 'enhanced_agents_used'):
            total_enhanced += len(self._data_processor_flow.state.enhanced_agents_used)
        
        return total_enhanced


# Convenience function for running the main workflow
async def run_main_workflow():
    """Run the main workflow with proper error handling."""
    try:
        workflow = MainWorkflowFlow()
        result = await workflow.kickoff()
        return result
    except Exception as e:
        print(f"❌ Workflow execution failed: {str(e)}")
        return None


# Development helper for plotting the workflow
def plot_main_workflow():
    """Generate a visualization of the main workflow."""
    workflow = MainWorkflowFlow()
    workflow.plot("main_workflow_visualization")
    print("📊 Workflow visualization saved as 'main_workflow_visualization.html'")


if __name__ == "__main__":
    # For testing
    asyncio.run(run_main_workflow()) 