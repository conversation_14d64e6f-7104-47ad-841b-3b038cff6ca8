"""
Quality Control Flow with hierarchical review and revision.

Implements explicit review → revision loop with supervisor oversight
following CrewAI 2025 Flows patterns.
"""

import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime

from crewai.flow.flow import Flow, listen, start
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field

from src.core.models.state_models import WorkflowState
from src.infrastructure.config.settings import get_llm_for_agent, enhance_backstory_with_current_date


class QualityControlState(BaseModel):
    """State for quality control flow."""
    original_content: str = ""
    review_result: str = ""
    revision_content: str = ""
    quality_score: float = 0.0
    approved: bool = False
    revision_count: int = 0
    max_revisions: int = 2


class QualityControlFlow(Flow[QualityControlState]):
    """
    Quality control flow with hierarchical review and revision.
    
    Implements explicit review → revision loop with supervisor oversight.
    """
    
    def __init__(self):
        self._flow_id = str(uuid.uuid4())
        self._supervisor_agent = self._create_supervisor_agent()
        self._revision_agent = self._create_revision_agent()
        super().__init__()
    
    def _create_supervisor_agent(self) -> Agent:
        """Create supervisor agent for quality review."""
        return Agent(
            role="Quality Supervisor",
            goal="Review content for quality, accuracy, and completeness, providing specific feedback for improvement",
            backstory=enhance_backstory_with_current_date("""
            You are an experienced quality supervisor responsible for ensuring high-quality outputs.
            Your role is to review content and either approve it or provide specific, actionable feedback.
            
            REVIEW PROCESS:
            - If content meets quality standards, respond with exactly 'APPROVE'
            - If content needs improvement, provide specific feedback with clear improvement suggestions
            - Evaluate for accuracy, completeness, clarity, and structure
            - Ensure all claims are well-supported and logical
            """),
            verbose=True,
            allow_delegation=True,
            memory=True,
            llm=get_llm_for_agent("supervisor"),
            tools=[]
        )
    
    def _create_revision_agent(self) -> Agent:
        """Create revision agent for content improvement."""
        return Agent(
            role="Content Revision Specialist",
            goal="Revise and improve content based on supervisor feedback while maintaining quality and accuracy",
            backstory=enhance_backstory_with_current_date("""
            You are a skilled content revision specialist who excels at improving written content.
            You take supervisor feedback and implement specific improvements while maintaining
            the original intent and accuracy of the content.
            """),
            verbose=True,
            allow_delegation=False,
            memory=True,
            llm=get_llm_for_agent("writer"),
            tools=[]
        )
    
    @start()
    async def review_content(self):
        """Review content for quality and provide feedback."""
        print("🔍 Quality Control: Reviewing content...")
        
        review_task = Task(
            description=f"""
            Review the following content for quality, accuracy, and completeness:
            
            CONTENT TO REVIEW:
            {self.state.original_content}
            
            Evaluate the content based on:
            1. ACCURACY: Are facts and claims correct and well-supported?
            2. COMPLETENESS: Does it fully address the intended purpose?
            3. CLARITY: Is the content clear and easy to understand?
            4. STRUCTURE: Is information well-organized and logical?
            5. COHERENCE: Do all parts work together effectively?
            
            IMPORTANT INSTRUCTIONS:
            - If the content meets all quality standards, respond with exactly 'APPROVE'
            - If the content needs improvement, provide specific, actionable feedback
            - Be constructive and specific in your feedback
            - Focus on the most important improvements needed
            """,
            expected_output="Either 'APPROVE' or specific feedback for improvement",
            agent=self._supervisor_agent
        )
        
        crew = Crew(
            agents=[self._supervisor_agent],
            tasks=[review_task],
            process=Process.sequential,
            memory=True,
            verbose=True
        )
        
        result = await crew.kickoff_async()
        self.state.review_result = result.raw
        self.state.approved = result.raw.strip().upper() == "APPROVE"
        
        print(f"✅ Review completed: {'APPROVED' if self.state.approved else 'NEEDS REVISION'}")
        return {"review_complete": True, "approved": self.state.approved}
    
    @listen("review_content")
    async def revise_content(self):
        """Revise content based on supervisor feedback."""
        if self.state.approved:
            print("✅ Content approved, no revision needed")
            return {"revision_complete": True, "final_content": self.state.original_content}
        
        if self.state.revision_count >= self.state.max_revisions:
            print("⚠️ Maximum revisions reached, using last version")
            final_content = self.state.revision_content or self.state.original_content
            return {"revision_complete": True, "final_content": final_content}
        
        print("🔄 Quality Control: Revising content based on feedback...")
        
        content_to_revise = self.state.revision_content or self.state.original_content
        
        revision_task = Task(
            description=f"""
            Revise the following content based on the supervisor's feedback:
            
            ORIGINAL CONTENT:
            {content_to_revise}
            
            SUPERVISOR FEEDBACK:
            {self.state.review_result}
            
            Your revision should:
            1. Address all specific points mentioned in the feedback
            2. Maintain the original intent and accuracy
            3. Improve clarity, structure, and completeness
            4. Ensure all claims remain well-supported
            5. Keep the same general length and scope
            
            Provide the complete revised content, not just the changes.
            """,
            expected_output="Complete revised content addressing all feedback points",
            agent=self._revision_agent
        )
        
        crew = Crew(
            agents=[self._revision_agent],
            tasks=[revision_task],
            process=Process.sequential,
            memory=True,
            verbose=True
        )
        
        result = await crew.kickoff_async()
        self.state.revision_content = result.raw
        self.state.revision_count += 1
        
        print(f"✅ Revision completed (attempt {self.state.revision_count})")
        
        # If we've revised, we need to review again
        if self.state.revision_count < self.state.max_revisions:
            # Update original content for next review cycle
            self.state.original_content = self.state.revision_content
            # Trigger another review
            return await self.review_content()
        else:
            return {"revision_complete": True, "final_content": self.state.revision_content} 