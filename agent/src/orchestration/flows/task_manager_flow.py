"""
TaskManager flow implementation using CrewAI 2025 Flows.

Handles task planning, execution strategy creation, and specialist delegation
in the hierarchical multi-agent system.
"""

import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime
import re

from crewai.flow.flow import Flow, listen, start
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field

from src.core.models.state_models import WorkflowState, PlanStep, AgentStatus, TaskPhase
from src.core.interfaces.flow_interface import ITaskManagerFlow, FlowStatus
from src.infrastructure.config.settings import get_llm_for_agent, enhance_backstory_with_current_date


class TaskManagerState(BaseModel):
    """State for TaskManager flow."""
    original_query: str = ""
    query_analysis: str = ""
    execution_plan: List[PlanStep] = Field(default_factory=list)
    delegation_strategy: Dict[str, Any] = Field(default_factory=dict)
    plan_quality_score: float = 0.0


class TaskManagerFlow(Flow[TaskManagerState]):
    """
    TaskManager flow orchestrating task planning and delegation.
    
    Creates comprehensive execution plans and manages specialist delegation
    using CrewAI 2025 Flows patterns.
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        
        # Create specialized agents
        self._planning_agent = self._create_planning_agent()
        self._strategy_agent = self._create_strategy_agent()
        self._delegation_agent = self._create_delegation_agent()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    def _create_planning_agent(self) -> Agent:
        """Create the main task planning agent."""
        original_backstory = """You are an expert task planner with deep experience in breaking down complex questions 
        into actionable steps. You understand how to leverage different types of research, analysis, and 
        synthesis to answer questions thoroughly. You excel at identifying the optimal sequence of tasks 
        and understanding dependencies between different work streams."""
        
        return Agent(
            role="Senior Task Planning Specialist",
            goal="Analyze complex queries and create comprehensive execution plans that maximize efficiency and accuracy",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=True,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("task_manager"),
            tools=[]  # TaskManager uses reasoning, not external tools
        )
    
    def _create_strategy_agent(self) -> Agent:
        """Create the execution strategy agent."""
        
        original_backstory = """You are a strategic thinker who specializes in optimizing workflow execution. 
        You understand the capabilities and limitations of different specialist agents and can design 
        execution strategies that maximize parallel processing while maintaining quality. You excel at 
        resource allocation and dependency management."""
        
        return Agent(
            role="Execution Strategy Architect",
            goal="Design optimal execution strategies that leverage parallel processing and specialist capabilities",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=True,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("task_manager"),
            tools=[]
        )
    
    def _create_delegation_agent(self) -> Agent:
        """Create the specialist delegation agent."""
        
        original_backstory = """You are an expert in multi-agent coordination with deep knowledge of specialist 
        capabilities. You know exactly which types of tasks should be assigned to researchers, librarians, 
        data processors, and writers. You excel at load balancing and ensuring that each specialist 
        receives tasks that match their expertise."""
        
        return Agent(
            role="Specialist Coordination Manager",
            goal="Coordinate specialist assignments and ensure optimal task distribution across the multi-agent system",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=True,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("task_manager"),
            tools=[]
        )
    
    def kickoff_async(self, inputs=None):
        """Custom kickoff_async that properly handles inputs from both workflows."""
        if inputs:
            # Handle different input formats from main workflow vs enhanced workflow
            query = None
            context = ""
            
            # Main workflow passes: {"query": "...", "context": {...}}
            # Enhanced workflow passes: {"query": "...", "context": "...", "workflow_id": "..."}
            if isinstance(inputs, dict):
                # Try different key variations to be flexible
                query = inputs.get('query') or inputs.get('original_query')
                context_data = inputs.get('context', {})
                
                # Handle context that could be dict or string
                if isinstance(context_data, dict):
                    # Extract meaningful context from dict
                    context_parts = []
                    if 'session_id' in context_data:
                        context_parts.append(f"Session: {context_data['session_id']}")
                    if 'workflow_id' in inputs:
                        context_parts.append(f"Workflow: {inputs['workflow_id']}")
                    context = ", ".join(context_parts) if context_parts else ""
                else:
                    context = str(context_data)
            
            # Set the state before starting the flow
            if query:
                self.state.original_query = query
                print(f"🔍 TaskManager received query: {query[:100]}{'...' if len(query) > 100 else ''}")
            
            if context:
                print(f"📋 TaskManager context: {context}")
        
        # Call the parent kickoff_async method
        return super().kickoff_async()
    
    @start()
    def analyze_query(self):
        """Analyze the input query to understand complexity and requirements."""
        print("🔍 TaskManager: Analyzing query complexity and requirements...")
        
        # Get query from state (now properly set by kickoff_async)
        query = self.state.original_query
        if not query:
            # Fallback - use a generic question instead of renewable energy
            query = "What is artificial intelligence?"
            self.state.original_query = query
            print("⚠️ TaskManager: No query provided, using default question")
        
        # Create query analysis task
        analysis_task = Task(
            description=f"""
            Analyze the following query to understand its complexity, scope, and requirements:
            
            Query: "{query}"
            
            Your analysis should cover:
            1. Query complexity level (low/medium/high)
            2. Required research types (web search, academic papers, news, technical docs)
            3. Analysis requirements (data processing, trend analysis, comparison)
            4. Expected response format and depth
            5. Key topics and domains involved
            6. Timeline sensitivity and recency requirements
            
            Provide a comprehensive analysis that will guide task planning.
            """,
            expected_output="Detailed query analysis covering complexity, requirements, and guidance for task planning",
            agent=self._planning_agent
        )
        
        # Execute analysis
        crew = Crew(
            agents=[self._planning_agent],
            tasks=[analysis_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        self.state.query_analysis = result.raw
        
        print("✅ Query analysis completed")
        return {"analysis_complete": True, "analysis": self.state.query_analysis}
    
    @listen(analyze_query)
    def create_execution_plan(self):
        """Create detailed execution plan based on query analysis."""
        print("📋 TaskManager: Creating comprehensive execution plan...")
        
        planning_task = Task(
            description=f"""
            Based on the query analysis, create a comprehensive execution plan:
            
            Original Query: "{self.state.original_query}"
            Query Analysis: {self.state.query_analysis}
            
            Create a detailed execution plan with 4-8 specific steps that include:
            
            1. RESEARCH TASKS:
               - Web search for recent developments and news
               - Academic/technical literature review if needed
               - Industry reports and market analysis if relevant
            
            2. RETRIEVAL TASKS:
               - Document collection from internal knowledge base
               - Specific data or statistics gathering
               - Historical context and background information
            
            3. ANALYSIS TASKS:
               - Data processing and trend analysis
               - Comparative analysis if multiple options exist
               - Impact assessment and implications
            
            4. SYNTHESIS REQUIREMENTS:
               - Key points that must be covered
               - Preferred structure and format
               - Target audience considerations
            
            For each step, specify:
            - Clear description of what needs to be done
            - Estimated time requirement (in minutes)
            - Dependencies on other steps
            - Required specialist type (Researcher/Librarian/DataProcessor)
            - Expected output format
            
            Format your response as a structured plan with numbered steps.
            """,
            expected_output="Structured execution plan with 4-8 detailed steps including descriptions, timing, dependencies, and specialist assignments",
            agent=self._strategy_agent
        )
        
        crew = Crew(
            agents=[self._strategy_agent],
            tasks=[planning_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Parse the plan into structured format
        plan_steps = self._parse_execution_plan(result.raw)
        self.state.execution_plan = plan_steps
        
        print(f"✅ Execution plan created with {len(plan_steps)} steps")
        return {"plan_complete": True, "steps": len(plan_steps)}
    
    @listen(create_execution_plan)
    def optimize_delegation_strategy(self):
        """Optimize task delegation strategy for parallel execution."""
        print("⚡ TaskManager: Optimizing delegation strategy for parallel execution...")
        
        delegation_task = Task(
            description=f"""
            Optimize the delegation strategy for the following execution plan:
            
            Original Query: "{self.state.original_query}"
            
            Execution Plan:
            {self._format_plan_for_agent()}
            
            Create an optimal delegation strategy that:
            
            1. PARALLEL EXECUTION OPTIMIZATION:
               - Identify which tasks can run in parallel
               - Group independent tasks for simultaneous execution
               - Minimize bottlenecks and waiting times
            
            2. SPECIALIST ASSIGNMENT:
               - Assign tasks to the most appropriate specialists:
                 * Researcher: Web search, news, recent developments, market research
                 * Librarian: Document retrieval, academic papers, knowledge base search
                 * DataProcessor: Data analysis, trend analysis, comparative studies
               - Balance workload across specialists
               - Consider specialist capabilities and limitations
            
            3. EXECUTION COORDINATION:
               - Define clear handoff points between specialists
               - Specify information sharing requirements
               - Plan for result integration and synthesis
            
            4. RISK MITIGATION:
               - Identify potential failure points
               - Plan for task reassignment if specialists fail
               - Ensure redundancy for critical information
            
            Provide a comprehensive delegation strategy with specific assignments and coordination details.
            """,
            expected_output="Comprehensive delegation strategy with parallel execution optimization, specialist assignments, and coordination details",
            agent=self._delegation_agent
        )
        
        crew = Crew(
            agents=[self._delegation_agent],
            tasks=[delegation_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Store delegation strategy
        self.state.delegation_strategy = {
            "strategy": result.raw,
            "parallel_groups": self._identify_parallel_groups(),
            "specialist_assignments": self._create_specialist_assignments(),
            "coordination_plan": self._create_coordination_plan()
        }
        
        # Calculate plan quality score
        self.state.plan_quality_score = self._calculate_plan_quality()
        
        print("✅ Delegation strategy optimized")
        return {
            "delegation_complete": True,
            "strategy": self.state.delegation_strategy,
            "quality_score": self.state.plan_quality_score
        }
    
    def _parse_execution_plan(self, plan_text: str) -> List[PlanStep]:
        """Parse the execution plan text into structured PlanStep objects."""
        steps = []
        lines = plan_text.split('\n')
        current_step = None
        
        for line in lines:
            line = line.strip()
            
            # Look for numbered steps
            if line and (line[0].isdigit() or line.startswith('Step')):
                if current_step:
                    steps.append(current_step)
                
                # Create new step
                step_id = f"step_{len(steps) + 1}"
                description = line
                
                current_step = PlanStep(
                    step_id=step_id,
                    description=description,
                    assigned_agent=self._extract_agent_assignment(line),
                    estimated_time=self._extract_time_estimate(line),
                    status=AgentStatus.IDLE
                )
            elif current_step and line:
                # Add additional details to current step
                current_step.description += f"\n{line}"
        
        # Don't forget the last step
        if current_step:
            steps.append(current_step)
        
        return steps
    
    def _extract_agent_assignment(self, text: str) -> Optional[str]:
        """Extract agent assignment from step description."""
        text_lower = text.lower()
        if 'research' in text_lower or 'web search' in text_lower:
            return "Researcher"
        elif 'librarian' in text_lower or 'document' in text_lower or 'knowledge base' in text_lower:
            return "Librarian"
        elif 'analys' in text_lower or 'data process' in text_lower:
            return "DataProcessor"
        return None
    
    def _extract_time_estimate(self, text: str) -> Optional[int]:
        """Extract time estimate from step description."""
        # Look for patterns like "5 minutes", "10 min", etc.
        pattern = r'(\d+)\s*(min|minute|minutes)'
        match = re.search(pattern, text.lower())
        if match:
            return int(match.group(1)) * 60  # Convert to seconds
        return 300  # Default 5 minutes
    
    def _format_plan_for_agent(self) -> str:
        """Format execution plan for agent consumption."""
        formatted = ""
        for i, step in enumerate(self.state.execution_plan, 1):
            formatted += f"{i}. {step.description}\n"
            if step.assigned_agent:
                formatted += f"   Assigned to: {step.assigned_agent}\n"
            if step.estimated_time:
                formatted += f"   Estimated time: {step.estimated_time // 60} minutes\n"
            formatted += "\n"
        return formatted
    
    def _identify_parallel_groups(self) -> List[List[str]]:
        """Identify groups of steps that can be executed in parallel."""
        # Simple implementation - group by agent type
        groups = {"Researcher": [], "Librarian": [], "DataProcessor": []}
        
        for step in self.state.execution_plan:
            if step.assigned_agent and step.assigned_agent in groups:
                groups[step.assigned_agent].append(step.step_id)
        
        # Return non-empty groups
        return [group for group in groups.values() if group]
    
    def _create_specialist_assignments(self) -> Dict[str, List[str]]:
        """Create specialist assignment mapping."""
        assignments = {}
        for step in self.state.execution_plan:
            if step.assigned_agent:
                if step.assigned_agent not in assignments:
                    assignments[step.assigned_agent] = []
                assignments[step.assigned_agent].append(step.step_id)
        return assignments
    
    def _create_coordination_plan(self) -> Dict[str, Any]:
        """Create coordination plan for specialists."""
        return {
            "handoff_points": ["after_research", "after_retrieval", "before_synthesis"],
            "shared_context": True,
            "result_format": "structured_json",
            "timeout_handling": "graceful_degradation",
            "error_recovery": "partial_results_accepted"
        }
    
    def _calculate_plan_quality(self) -> float:
        """Calculate a quality score for the execution plan."""
        score = 0.0
        
        # Base score for having a plan
        if self.state.execution_plan:
            score += 0.3
        
        # Score for having assigned agents
        assigned_steps = sum(1 for step in self.state.execution_plan if step.assigned_agent)
        if self.state.execution_plan:
            score += 0.3 * (assigned_steps / len(self.state.execution_plan))
        
        # Score for having time estimates
        timed_steps = sum(1 for step in self.state.execution_plan if step.estimated_time)
        if self.state.execution_plan:
            score += 0.2 * (timed_steps / len(self.state.execution_plan))
        
        # Score for plan completeness (4-8 steps is optimal)
        step_count = len(self.state.execution_plan)
        if 4 <= step_count <= 8:
            score += 0.2
        elif step_count > 0:
            score += 0.1
        
        return min(score, 1.0)


# Convenience function for running TaskManager flow standalone
async def run_task_manager_flow(query: str):
    """Run TaskManager flow with a specific query."""
    flow = TaskManagerFlow()
    flow.state.original_query = query
    result = await flow.kickoff_async()
    return result


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_task_manager_flow("What are the latest trends in artificial intelligence?")) 