"""
SSE Integration utilities for CrewAI flows.

Provides methods to send real-time updates during workflow execution.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class SSEFlowIntegration:
    """
    Utility class to integrate SSE updates into CrewAI flows.
    
    Provides methods to send real-time updates during workflow execution
    including agent status, phase transitions, and tool usage.
    """
    
    def __init__(self, sse_manager=None, session_id: str = None):
        self.sse_manager = sse_manager
        self.session_id = session_id
        self.is_enabled = sse_manager is not None and session_id is not None
        
    async def send_phase_start(self, phase_name: str, phase_data: Dict[str, Any] = None):
        """Send phase start notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_phase_update(self.session_id, {
                "phase": phase_name,
                "status": "started",
                "message": f"Starting {phase_name} phase",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(phase_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send phase start update: {e}")
    
    async def send_phase_complete(self, phase_name: str, phase_data: Dict[str, Any] = None):
        """Send phase completion notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_phase_update(self.session_id, {
                "phase": phase_name,
                "status": "completed",
                "message": f"Completed {phase_name} phase",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(phase_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send phase complete update: {e}")
    
    async def send_agent_start(self, agent_id: str, agent_data: Dict[str, Any] = None):
        """Send agent start notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_agent_update(self.session_id, {
                "agent_id": agent_id,
                "status": "started",
                "message": f"Starting {agent_id} execution",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(agent_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send agent start update: {e}")
    
    async def send_agent_complete(self, agent_id: str, agent_data: Dict[str, Any] = None):
        """Send agent completion notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_agent_update(self.session_id, {
                "agent_id": agent_id,
                "status": "completed",
                "message": f"Completed {agent_id} execution",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(agent_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send agent complete update: {e}")
    
    async def send_agent_error(self, agent_id: str, error_msg: str, agent_data: Dict[str, Any] = None):
        """Send agent error notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_agent_update(self.session_id, {
                "agent_id": agent_id,
                "status": "error",
                "message": f"Error in {agent_id}: {error_msg}",
                "error": error_msg,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(agent_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send agent error update: {e}")
    
    async def send_crew_start(self, crew_name: str, crew_data: Dict[str, Any] = None):
        """Send crew execution start notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_workflow_status_update(self.session_id, {
                "status": "crew_executing",
                "crew_name": crew_name,
                "message": f"Starting {crew_name} crew execution",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(crew_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send crew start update: {e}")
    
    async def send_crew_complete(self, crew_name: str, crew_data: Dict[str, Any] = None):
        """Send crew execution completion notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_workflow_status_update(self.session_id, {
                "status": "crew_completed",
                "crew_name": crew_name,
                "message": f"Completed {crew_name} crew execution",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(crew_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send crew complete update: {e}")
    
    async def send_tool_usage(self, tool_name: str, tool_status: str, tool_data: Dict[str, Any] = None):
        """Send tool usage notification."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_tool_status_update(self.session_id, {
                "tool_name": tool_name,
                "status": tool_status,
                "message": f"Tool {tool_name} {tool_status}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **(tool_data or {})
            })
        except Exception as e:
            logger.error(f"Failed to send tool usage update: {e}")
    
    async def send_workflow_progress(self, progress_data: Dict[str, Any]):
        """Send workflow progress update."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_workflow_status_update(self.session_id, {
                "status": "progress",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **progress_data
            })
        except Exception as e:
            logger.error(f"Failed to send workflow progress update: {e}")
    
    async def send_specialist_parallel_start(self, specialists: list):
        """Send notification for parallel specialist execution start."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_workflow_status_update(self.session_id, {
                "status": "parallel_execution_started",
                "specialists": specialists,
                "message": f"Starting parallel execution of {len(specialists)} specialists",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        except Exception as e:
            logger.error(f"Failed to send parallel start update: {e}")
    
    async def send_specialist_parallel_complete(self, specialists: list, results_count: int):
        """Send notification for parallel specialist execution completion."""
        if not self.is_enabled:
            return
            
        try:
            await self.sse_manager.send_workflow_status_update(self.session_id, {
                "status": "parallel_execution_completed",
                "specialists": specialists,
                "results_count": results_count,
                "message": f"Completed parallel execution of {len(specialists)} specialists with {results_count} results",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        except Exception as e:
            logger.error(f"Failed to send parallel complete update: {e}")


def get_sse_integration(config: Dict[str, Any]) -> SSEFlowIntegration:
    """
    Get SSE integration instance from workflow config.
    
    Args:
        config: Workflow configuration containing sse_manager and session_id
        
    Returns:
        SSEFlowIntegration instance
    """
    # Handle both dict and AgentConfig objects
    if hasattr(config, 'get'):
        # It's a dictionary - use get method
        sse_manager = config.get("sse_manager")
        session_id = config.get("session_id")
    else:
        # It's an AgentConfig object - use getattr
        sse_manager = getattr(config, "sse_manager", None)
        session_id = getattr(config, "session_id", None)
    
    return SSEFlowIntegration(sse_manager, session_id)


# Utility function to wrap crew execution with SSE updates
async def execute_crew_with_sse(
    crew, 
    crew_name: str, 
    sse_integration: SSEFlowIntegration,
    inputs: Dict[str, Any] = None
):
    """
    Execute a CrewAI crew with SSE progress updates.
    
    Args:
        crew: CrewAI Crew instance
        crew_name: Name of the crew for logging
        sse_integration: SSE integration instance
        inputs: Inputs for crew execution
        
    Returns:
        Crew execution result
    """
    try:
        # Send crew start notification
        await sse_integration.send_crew_start(crew_name)
        
        # Execute crew
        if inputs:
            result = await crew.kickoff_async(inputs=inputs)
        else:
            result = await crew.kickoff_async()
        
        # Send crew completion notification
        await sse_integration.send_crew_complete(crew_name, {
            "result_length": len(str(result)) if result else 0,
            "success": True
        })
        
        return result
        
    except Exception as e:
        # Send error notification
        await sse_integration.send_crew_complete(crew_name, {
            "success": False,
            "error": str(e)
        })
        raise 