import asyncio
import time
import uuid
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from crewai.flow.flow import Flow, listen, start, router, and_, or_
from crewai import Agent, Task, Crew, Process
import dspy
from datetime import datetime

# Import required flows for integration
from .task_manager_flow import TaskManagerFlow
from .writer_flow import Writer<PERSON>low

from ...agents.specialists.search_specialist import EnhancedSearchSpecialist
from ...agents.specialists.knowledge_specialist import MathEnabledKnowledgeSpecialist
from ...agents.specialists.react_search_specialist import ReActSearchSpecialist
from ...agents.specialists.react_knowledge_specialist import ReActKnowledgeSpecialist
from .state_management import WorkflowState, TaskResult, AgentMetrics
from .flow_monitoring import FlowMonitor, MonitoringConfig

# Import state models for proper integration
from ...core.models.state_models import TaskPhase
from ...infrastructure.config.settings import get_llm_for_agent

# Import evaluation components
from ...optimization.dspy.evaluation.evaluation_pipeline import AutomatedEvaluationPipeline
from ...optimization.dspy.evaluation.quality_gates import QualityGateSystem, QualityThresholds

# Import debug logging
from ...infrastructure.monitoring.debug_logger import get_debug_logger

class ComplexityAnalysis(BaseModel):
    """Result of query complexity analysis."""
    complexity_score: float = Field(ge=0.0, le=10.0)
    capabilities: List[str] = Field(default_factory=list)
    time_estimate: float = 0.0
    can_parallelize: bool = False
    reasoning: str = ""

class ComplexityAnalyzer(dspy.Signature):
    """Analyze query complexity and requirements using DSPy Chain of Thought."""
    query = dspy.InputField(desc="The user query to analyze")
    complexity_score = dspy.OutputField(desc="Complexity score from 0-10 (0=simple, 10=extremely complex)")
    capabilities = dspy.OutputField(desc="Required capabilities as comma-separated list")
    time_estimate = dspy.OutputField(desc="Estimated execution time in seconds")
    can_parallelize = dspy.OutputField(desc="Whether task can be parallelized (true/false)")
    reasoning = dspy.OutputField(desc="Brief reasoning for the complexity assessment")

class AdvancedCoordinationFlow(Flow[WorkflowState]):
    """
    Advanced multi-agent orchestration using CrewAI Flows 2025.
    
    Features:
    - Event-driven agent coordination
    - Dynamic workflow routing based on task complexity
    - Parallel processing with intelligent load balancing
    - Real-time monitoring and error recovery
    - Persistent state management across sessions
    - Integrated TaskManagerFlow for comprehensive planning
    """
    
    def __init__(self, config: Dict[str, Any]):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = "pending"
        
        # Store configuration
        self.config = config
        
        # Initialize specialized flows - COPIED FROM MAIN WORKFLOW
        self._task_manager_flow = TaskManagerFlow()
        self._writer_flow = WriterFlow()
        
        # Initialize enhanced agents
        self.agents = self._initialize_agents()
        
        # Initialize monitoring with proper configuration
        monitoring_config = MonitoringConfig(
            enable_real_time_monitoring=config.get('enhanced_flow', {}).get('real_time_monitoring', True),
            performance_threshold_ms=config.get('enhanced_flow', {}).get('alert_thresholds', {}).get('max_execution_time', 300) * 1000,
            error_rate_threshold=config.get('enhanced_flow', {}).get('alert_thresholds', {}).get('max_error_rate', 0.2),
            alert_cooldown_minutes=5,
            max_memory_usage=0.85,
            max_cpu_usage=0.90
        )
        
        self.monitor = FlowMonitor(monitoring_config)
        
        # Initialize DSPy with proper configuration  
        self._setup_dspy(config.get('enhanced_flow', {}).get('dspy', {}))
        
        # Initialize debug logging
        self.debug_logger = get_debug_logger()
        
        # DEBUG: Add immediate config inspection
        print(f"🔍 DEBUG - AdvancedCoordinationFlow config inspection:")
        print(f"   Config type: {type(config)}")
        print(f"   Config keys: {list(config.keys()) if isinstance(config, dict) else 'Not a dict'}")
        print(f"   Config['evaluation']: {config.get('evaluation', 'MISSING')}")
        if isinstance(config.get('evaluation'), dict):
            print(f"   Config['evaluation']['enabled']: {config.get('evaluation', {}).get('enabled', 'MISSING')}")
        
        # Validate configuration loading
        eval_config = config.get('evaluation', {})
        eval_enabled_in_config = eval_config.get('enabled', False)
        
        print(f"🔍 DEBUG - After extraction:")
        print(f"   eval_config: {eval_config}")
        print(f"   eval_enabled_in_config: {eval_enabled_in_config} (type: {type(eval_enabled_in_config)})")
        
        self.debug_logger.log_config_validation(
            'evaluation.enabled', 
            True, 
            eval_enabled_in_config, 
            eval_enabled_in_config == True
        )
        
        # Initialize evaluation components if enabled
        self.evaluation_enabled = eval_enabled_in_config
        if self.evaluation_enabled:
            self._setup_evaluation_pipeline(eval_config)
        else:
            self.evaluation_pipeline = None
            self.quality_gates = None
            
        self.debug_logger.log_workflow_execution(
            'AdvancedCoordinationFlow',
            'initialization',
            {'evaluation_enabled': self.evaluation_enabled, 'config_evaluation_enabled': eval_enabled_in_config},
            'evaluation_enabled should match config',
            f'evaluation_enabled={self.evaluation_enabled}',
            self.evaluation_enabled == eval_enabled_in_config
        )
        
        # Call super().__init__() after setting attributes
        super().__init__()

    def _setup_dspy(self, dspy_config: Dict[str, Any]):
        """Setup DSPy with proper LLM configuration."""
        try:
            # Use the same configuration approach as main workflow
            self.complexity_analyzer = dspy.ChainOfThought(ComplexityAnalyzer)
            print(f"✅ DSPy configured for AdvancedCoordinationFlow")
        except Exception as e:
            print(f"⚠️ DSPy setup failed: {str(e)}, using fallback")
            self.complexity_analyzer = None

    def _setup_evaluation_pipeline(self, eval_config: Dict[str, Any]):
        """Setup evaluation pipeline for advanced flow."""
        try:
            print("🔍 Initializing automated evaluation pipeline for AdvancedCoordinationFlow...")
            
            # Initialize evaluation pipeline
            self.evaluation_pipeline = AutomatedEvaluationPipeline(eval_config)
            
            # Setup quality gates with thresholds
            thresholds = QualityThresholds(
                relevance_min=eval_config.get('metrics', {}).get('relevance', {}).get('minimum_threshold', 0.7),
                coherence_min=eval_config.get('metrics', {}).get('coherence', {}).get('minimum_threshold', 0.8),
                instruction_following_min=eval_config.get('metrics', {}).get('instruction_following', {}).get('minimum_threshold', 0.75),
                tool_efficiency_min=eval_config.get('metrics', {}).get('tool_usage_efficiency', {}).get('minimum_threshold', 0.6)
            )
            
            self.quality_gates = QualityGateSystem(thresholds, eval_config.get('quality_gates', {}))
            
            print("✅ Advanced evaluation pipeline initialized for AdvancedCoordinationFlow")
            print("   📊 Enabled metrics: relevance, coherence, instruction following, tool efficiency")
            print("   🚪 Quality gates activated with automated thresholds")
            
        except ImportError as e:
            print(f"⚠️ Evaluation pipeline not available: {e}")
            self.evaluation_pipeline = None
            self.quality_gates = None
        except Exception as e:
            print(f"⚠️ Evaluation setup failed: {e}")
            self.evaluation_pipeline = None
            self.quality_gates = None

    @property
    def flow_id(self) -> str:
        """Get the flow ID."""
        return self._flow_id

    @property  
    def status(self) -> str:
        """Get the current flow status."""
        return self._status

    def _initialize_agents(self) -> Dict[str, Any]:
        """Initialize only specialist flows like MainWorkflow (no individual agents)."""
        try:
            # Import enhanced specialist flows (same as MainWorkflow)
            from .specialist_flows import (
                EnhancedResearcherFlow, 
                EnhancedLibrarianFlow, 
                EnhancedDataProcessorFlow
            )
            
            return {
                # Only specialist flows (like MainWorkflow) - NO individual agents
                'researcher_flow': EnhancedResearcherFlow(),
                'librarian_flow': EnhancedLibrarianFlow(),
                'data_processor_flow': EnhancedDataProcessorFlow()
            }
            
        except ImportError as e:
            print(f"⚠️ Enhanced specialist flows not available: {str(e)}")
            # Fallback to basic flows
            return self._create_fallback_flows()
    
    def _create_fallback_flows(self) -> Dict[str, Any]:
        """Create fallback flows when enhanced flows are not available."""
        class FallbackFlow:
            def __init__(self, flow_type):
                self.flow_type = flow_type
            
            async def kickoff_async(self, inputs=None):
                query = inputs.get('query', 'No query provided') if inputs else 'No query provided'
                return {
                    'success': True,
                    'results': [
                        {
                            'query': query,
                            'sources': [],
                            'summary': f'Fallback {self.flow_type} processing completed for: {query}',
                            'key_findings': [f'Basic {self.flow_type} analysis performed'],
                            'confidence_score': 0.6,
                            'timestamp': datetime.now(),
                            'agent_id': f'Fallback_{self.flow_type}_{uuid.uuid4()}'
                        }
                    ],
                    'enhanced_agents_used': [f'Fallback_{self.flow_type}'],
                    'reliability_stats': {'average_confidence': 0.6},
                    'tools_performance': {},
                    'success': True
                }
        
        return {
            'researcher_flow': FallbackFlow('Research'),
            'librarian_flow': FallbackFlow('Library'),
            'data_processor_flow': FallbackFlow('DataProcessor')
        }
    
    def kickoff_async(self, inputs=None):
        """Custom kickoff_async that properly handles inputs."""
        if inputs:
            # Set inputs in state before starting the flow (workaround for CrewAI Flows limitation)
            for key, value in inputs.items():
                if hasattr(self.state, key):
                    setattr(self.state, key, value)
                else:
                    # For new attributes, we need to add them to the state
                    # This is a workaround since StateWithId doesn't allow arbitrary attributes
                    # We'll store them in a way that's accessible during the flow
                    if not hasattr(self.state, '_inputs'):
                        setattr(self.state, '_inputs', {})
                    self.state._inputs[key] = value
        
        # Call the parent kickoff_async method
        return super().kickoff_async()

    @start()
    async def analyze_request(self):
        """Enhanced request analysis with proper state management."""
        print(f"🚀 Starting Advanced Coordination Flow Analysis")
        print("=" * 70)
        print("🎯 Advanced Features Active:")
        print("   ✅ Dynamic Complexity Analysis")
        print("   ✅ Intelligent Workflow Routing")
        print("   ✅ Parallel Processing with Load Balancing")  
        print("   ✅ Real-time Monitoring and Error Recovery")
        print("   ✅ Enhanced Specialist Integration")
        print("   ✅ DSPy Chain-of-Thought Reasoning")
        print("=" * 70)
        
        # Get query from inputs (stored in state by custom kickoff_async)
        query = None
        
        # Try to get from inputs stored by kickoff_async first (most reliable)
        if hasattr(self.state, '_inputs') and self.state._inputs:
            query = self.state._inputs.get('original_query') or self.state._inputs.get('query')
        
        # Try to get from direct state attributes 
        if not query and hasattr(self.state, 'original_query'):
            query = getattr(self.state, 'original_query')
        
        # Final fallback - use a generic question instead of renewable energy
        if not query:
            query = "What is artificial intelligence?"
            print("⚠️ No query provided, using default question")
        
        print(f"\n🎯 Query: {query}")
        print(f"🆔 Workflow ID: {self._flow_id}")
        print(f"🔧 Enhancement Level: Advanced Coordination Flow")
        
        # Store query in accessible format for later methods
        if not hasattr(self.state, '_inputs'):
            setattr(self.state, '_inputs', {})
        
        self.state._inputs['original_query'] = query
        self.state._inputs['processed_query'] = query
        self.state._inputs['workflow_id'] = self._flow_id
        self.state._inputs['current_stage'] = "complexity_analysis"
        
        # Remove problematic monitoring setup - handle monitoring differently like main workflow
        
        try:
            # Complexity analysis using DSPy patterns
            if self.complexity_analyzer:
                analysis_result = self.complexity_analyzer(query=query)
                
                # Parse capabilities from comma-separated string
                capabilities = [cap.strip() for cap in analysis_result.capabilities.split(',') if cap.strip()]
                
                # Create structured analysis
                analysis = ComplexityAnalysis(
                    complexity_score=float(analysis_result.complexity_score),
                    capabilities=capabilities,
                    time_estimate=float(analysis_result.time_estimate),
                    can_parallelize=str(analysis_result.can_parallelize).lower() == 'true',
                    reasoning=analysis_result.reasoning
                )
            else:
                # Fallback analysis when DSPy unavailable
                analysis = ComplexityAnalysis(
                    complexity_score=5.0,
                    capabilities=["search", "analysis"],
                    time_estimate=60.0,
                    can_parallelize=True,
                    reasoning="Fallback analysis - DSPy unavailable"
                )
            
            # Store analysis results in inputs for access by other methods
            self.state._inputs['complexity_score'] = analysis.complexity_score
            self.state._inputs['required_capabilities'] = analysis.capabilities
            self.state._inputs['estimated_time'] = analysis.time_estimate
            self.state._inputs['parallel_eligible'] = analysis.can_parallelize
            self.state._inputs['current_stage'] = "complexity_analysis_complete"
            
            print(f"📊 Analysis complete:")
            print(f"   Complexity: {analysis.complexity_score}/10")
            print(f"   Capabilities: {', '.join(analysis.capabilities)}")
            print(f"   Parallel eligible: {analysis.can_parallelize}")
            print(f"   Reasoning: {analysis.reasoning}")
            
            return analysis
            
        except Exception as e:
            error_msg = f"Complexity analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            # Fallback to standard workflow
            fallback_analysis = ComplexityAnalysis(
                complexity_score=5.0,
                capabilities=["search", "analysis"],
                time_estimate=60.0,
                can_parallelize=False,
                reasoning="Fallback due to analysis error"
            )
            return fallback_analysis
    
    @listen(analyze_request)
    async def task_planning_phase(self):
        """Enhanced task planning using TaskManagerFlow (copied from MainWorkflow)."""
        print(f"\n📋 PHASE: Enhanced Task Planning (Advanced Flow)")
        print("-" * 50)
        
        # Update stage
        if hasattr(self.state, '_inputs'):
            self.state._inputs['current_stage'] = "planning"
        
        try:
            # Get data from inputs storage
            inputs = getattr(self.state, '_inputs', {})
            
            # Prepare context for TaskManager flow (same as MainWorkflow)
            planning_context = {
                "query": inputs.get('original_query', ''),
                "context": inputs.get('context', ''),
                "workflow_id": inputs.get('workflow_id', ''),
                "complexity_analysis": {
                    "score": inputs.get('complexity_score', 5.0),
                    "capabilities": inputs.get('required_capabilities', []),
                    "parallel_eligible": inputs.get('parallel_eligible', True)
                }
            }
            
            print("🔄 Starting enhanced task planning...")
            
            # Run TaskManager flow to create execution plan
            planning_result = await self._task_manager_flow.kickoff_async(inputs=planning_context)
            
            # Update state with plan (handle different result formats)
            execution_plan = []
            if hasattr(planning_result, 'execution_plan'):
                execution_plan = planning_result.execution_plan
            elif isinstance(planning_result, dict) and 'execution_plan' in planning_result:
                execution_plan = planning_result['execution_plan']
            elif hasattr(self._task_manager_flow, 'state'):
                if hasattr(self._task_manager_flow.state, 'execution_plan'):
                    execution_plan = self._task_manager_flow.state.execution_plan
                elif hasattr(self._task_manager_flow.state, '__getitem__'):
                    execution_plan = getattr(self._task_manager_flow.state, 'execution_plan', [])
            
            # Store execution plan in inputs
            if hasattr(self.state, '_inputs'):
                self.state._inputs['execution_plan'] = execution_plan
            
            print(f"✅ Enhanced execution plan created with {len(execution_plan)} steps")
            return {"planning_complete": True, "plan": execution_plan}
            
        except Exception as e:
            error_msg = f"Enhanced task planning failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {"planning_complete": False, "error": error_msg}

    @router(task_planning_phase)
    def route_by_complexity(self):
        """Dynamic routing based on task complexity, requirements, and execution plan."""
        
        # Get values from inputs storage
        inputs = getattr(self.state, '_inputs', {})
        complexity_score = inputs.get('complexity_score', 5.0)
        parallel_eligible = inputs.get('parallel_eligible', True)
        execution_plan = inputs.get('execution_plan', [])
        
        # Consider execution plan size
        plan_size = len(execution_plan)
        
        # Enhanced routing logic - restored parallel workflow
        if complexity_score >= 8.0 or plan_size >= 6:
            print("🔄 Routing to complex workflow (multi-stage + hierarchical)")
            return "complex_workflow"
        elif parallel_eligible and complexity_score >= 5.0 and plan_size >= 3:
            print("⚡ Routing to parallel workflow (load balanced + specialist flows)")
            return "parallel_workflow"
        else:
            print("📋 Routing to standard workflow (sequential + planned)")
            return "standard_workflow"
    
    @listen("complex_workflow")
    async def execute_complex_workflow(self):
        """
        Multi-stage workflow for complex queries requiring multiple specialist agents.
        Uses the same approach as main workflow with specialist flows.
        """
        print("🔄 Executing complex multi-agent workflow")
        
        # Update stage
        inputs = getattr(self.state, '_inputs', {})
        inputs['current_stage'] = "complex_execution"
        
        try:
            # Get query from inputs storage
            query = inputs.get('original_query', 'What is AI?')
            
            print("   Stage 1: Parallel Enhanced Specialist Execution (like main workflow)")
            
            # Use the same context structure as main workflow
            specialist_context = {
                "query": query,
                "execution_plan": inputs.get('execution_plan', []),
                "workflow_id": inputs.get('workflow_id', ''),
                "enhanced_mode": True,
                "quick_wins_active": True,
                "complexity_analysis": {
                    "score": inputs.get('complexity_score', 5.0),
                    "capabilities": inputs.get('required_capabilities', []),
                    "parallel_eligible": inputs.get('parallel_eligible', True)
                }
            }
            
            # Execute all enhanced specialists in parallel (like main workflow)
            print("      🔄 Starting parallel execution of enhanced specialists...")
            
            specialist_tasks = [
                self.agents['researcher_flow'].kickoff_async(inputs=specialist_context),
                self.agents['librarian_flow'].kickoff_async(inputs=specialist_context),
                self.agents['data_processor_flow'].kickoff_async(inputs=specialist_context)
            ]
            
            # Wait for all specialists to complete
            results = await asyncio.gather(*specialist_tasks, return_exceptions=True)
            researcher_result, librarian_result, processor_result = results
            
            # Process results like main workflow does
            combined_results = []
            
            # Process Enhanced Researcher result
            if isinstance(researcher_result, dict) and researcher_result.get('success'):
                flow_results = researcher_result.get('results', [])
                enhanced_agents = researcher_result.get('enhanced_agents_used', [])
                tools_performance = researcher_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Research: {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Research findings: {content}")
            
            # Process Enhanced Librarian result  
            if isinstance(librarian_result, dict) and librarian_result.get('success'):
                flow_results = librarian_result.get('results', [])
                enhanced_agents = librarian_result.get('enhanced_agents_used', [])
                tools_performance = librarian_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Library: {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Knowledge analysis: {content}")
            
            # Process Enhanced Data Processor result
            if isinstance(processor_result, dict) and processor_result.get('success'):
                flow_results = processor_result.get('results', [])
                enhanced_agents = processor_result.get('enhanced_agents_used', [])
                tools_performance = processor_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Data Analysis: {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Data analysis: {content}")
            
            # Stage 2: Synthesis using WriterFlow (like main workflow)
            print("   Stage 2: Enhanced Synthesis using WriterFlow")
            
            if combined_results:
                # Prepare synthesis context like main workflow
                synthesis_context = {
                    "query": query,
                    "research_results": [{"content": r} for r in combined_results if "Research" in r],
                    "library_results": [{"content": r} for r in combined_results if "Knowledge" in r],
                    "analysis_results": [{"content": r} for r in combined_results if "Data" in r],
                    "workflow_id": inputs.get('workflow_id', ''),
                    "enhanced_mode": True,
                    "enhancement_summary": {
                        "total_enhanced_agents": len(combined_results),
                        "reliability_features_used": True,
                        "professional_tools_used": True,
                        "colbert_search_used": True,
                        "math_computation_used": True,
                        "react_reasoning_used": True
                    }
                }
                
                # Use WriterFlow for final synthesis
                synthesis_result = await self._writer_flow.kickoff_async(inputs=synthesis_context)
                
                if hasattr(synthesis_result, 'final_answer'):
                    final_result = synthesis_result.final_answer
                elif isinstance(synthesis_result, dict):
                    final_result = synthesis_result.get('final_output', str(synthesis_result))
                else:
                    final_result = str(synthesis_result)
            else:
                # Fallback if no specialist results
                final_result = f"Complex analysis completed for: {query}. Multiple specialist systems were engaged but encountered issues with result aggregation."
            
            # Store final result in inputs
            inputs['final_result'] = final_result
            inputs['workflow_completed'] = True
            inputs['current_stage'] = "complex_complete"
            
            print("✅ Complex workflow completed successfully")
            return final_result
            
        except Exception as e:
            error_msg = f"Complex workflow execution failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            # Fallback to simpler approach
            return await self.execute_standard_workflow()
    
    @listen("parallel_workflow")
    async def execute_parallel_workflow(self):
        """
        Parallel execution using specialist flows (like main workflow approach).
        Executes all specialist flows simultaneously for faster processing.
        """
        print("⚡ Executing parallel workflow with specialist flows")
        
        # Update stage
        inputs = getattr(self.state, '_inputs', {})
        inputs['current_stage'] = "parallel_execution"
        
        try:
            # Get query from inputs storage
            query = inputs.get('original_query', 'What is AI?')
            
            print("   🔄 Executing all specialist flows in parallel (like main workflow)...")
            
            # Use the same context structure as main workflow and complex workflow
            specialist_context = {
                "query": query,
                "execution_plan": inputs.get('execution_plan', []),
                "workflow_id": inputs.get('workflow_id', ''),
                "enhanced_mode": True,
                "quick_wins_active": True,
                "parallel_mode": True,  # Signal parallel execution
                "complexity_analysis": {
                    "score": inputs.get('complexity_score', 5.0),
                    "capabilities": inputs.get('required_capabilities', []),
                    "parallel_eligible": inputs.get('parallel_eligible', True)
                }
            }
            
            # Execute all specialist flows in parallel (exactly like main workflow)
            print("      🔄 Starting parallel execution of all specialist flows...")
            
            specialist_tasks = [
                self.agents['researcher_flow'].kickoff_async(inputs=specialist_context),
                self.agents['librarian_flow'].kickoff_async(inputs=specialist_context),
                self.agents['data_processor_flow'].kickoff_async(inputs=specialist_context)
            ]
            
            # Wait for all specialists to complete (same as main workflow)
            results = await asyncio.gather(*specialist_tasks, return_exceptions=True)
            researcher_result, librarian_result, processor_result = results
            
            # Process results exactly like main workflow and complex workflow
            combined_results = []
            
            # Process Enhanced Researcher result
            if isinstance(researcher_result, dict) and researcher_result.get('success'):
                flow_results = researcher_result.get('results', [])
                enhanced_agents = researcher_result.get('enhanced_agents_used', [])
                tools_performance = researcher_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Research (Parallel): {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Research findings: {content}")
            
            # Process Enhanced Librarian result  
            if isinstance(librarian_result, dict) and librarian_result.get('success'):
                flow_results = librarian_result.get('results', [])
                enhanced_agents = librarian_result.get('enhanced_agents_used', [])
                tools_performance = librarian_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Library (Parallel): {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Knowledge analysis: {content}")
            
            # Process Enhanced Data Processor result
            if isinstance(processor_result, dict) and processor_result.get('success'):
                flow_results = processor_result.get('results', [])
                enhanced_agents = processor_result.get('enhanced_agents_used', [])
                tools_performance = processor_result.get('tools_performance', {})
                
                print(f"      ✅ Enhanced Data Analysis (Parallel): {len(flow_results)} results")
                print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                print(f"         Tools performance: {tools_performance}")
                
                for result_dict in flow_results:
                    if isinstance(result_dict, dict):
                        content = result_dict.get('content', '')
                        combined_results.append(f"Data analysis: {content}")
            
            # Synthesis using WriterFlow (like complex workflow)
            print("   ⚡ Fast parallel synthesis using WriterFlow...")
            
            if combined_results:
                # Prepare synthesis context for parallel results
                synthesis_context = {
                    "query": query,
                    "research_results": [{"content": r} for r in combined_results if "Research" in r],
                    "library_results": [{"content": r} for r in combined_results if "Knowledge" in r],
                    "analysis_results": [{"content": r} for r in combined_results if "Data" in r],
                    "workflow_id": inputs.get('workflow_id', ''),
                    "enhanced_mode": True,
                    "parallel_mode": True,
                    "enhancement_summary": {
                        "total_enhanced_agents": len(combined_results),
                        "parallel_execution_used": True,
                        "reliability_features_used": True,
                        "professional_tools_used": True,
                        "colbert_search_used": True,
                        "math_computation_used": True,
                        "react_reasoning_used": True
                    }
                }
                
                # Use WriterFlow for final synthesis
                synthesis_result = await self._writer_flow.kickoff_async(inputs=synthesis_context)
                
                if hasattr(synthesis_result, 'final_answer'):
                    final_result = synthesis_result.final_answer
                elif isinstance(synthesis_result, dict):
                    final_result = synthesis_result.get('final_output', str(synthesis_result))
                else:
                    final_result = str(synthesis_result)
            else:
                # Fallback if no specialist results
                final_result = f"Parallel analysis completed for: {query}. All specialist systems were engaged simultaneously."
            
            # Store final result in inputs
            inputs['final_result'] = final_result
            inputs['workflow_completed'] = True
            inputs['current_stage'] = "parallel_complete"
            
            print("✅ Parallel workflow completed successfully with enhanced speed")
            return final_result
            
        except Exception as e:
            error_msg = f"Parallel workflow execution failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            # Fallback to standard approach
            return await self.execute_standard_workflow()
    
    @listen("standard_workflow")
    async def execute_standard_workflow(self):
        """Standard sequential workflow for moderate complexity queries."""
        print("📋 Executing standard sequential workflow")
        
        # Update stage
        inputs = getattr(self.state, '_inputs', {})
        inputs['current_stage'] = "standard_execution"
        
        try:
            # Get query from inputs storage
            query = inputs.get('original_query', 'What is AI?')
            
            # Select primary specialist flow based on query type
            primary_flow_name = self._select_primary_agent()
            print(f"   Selected primary specialist flow: {primary_flow_name}")
            
            # Use the same context structure as main workflow and complex workflow
            specialist_context = {
                "query": query,
                "execution_plan": inputs.get('execution_plan', []),
                "workflow_id": inputs.get('workflow_id', ''),
                "enhanced_mode": True,
                "quick_wins_active": True,
                "complexity_analysis": {
                    "score": inputs.get('complexity_score', 5.0),
                    "capabilities": inputs.get('required_capabilities', []),
                    "parallel_eligible": inputs.get('parallel_eligible', True)
                }
            }
            
            # Execute the selected specialist flow using the same approach as main workflow
            print(f"      🔄 Executing {primary_flow_name} as specialist flow")
            
            if primary_flow_name in self.agents and hasattr(self.agents[primary_flow_name], 'kickoff_async'):
                # Execute specialist flow
                specialist_result = await self.agents[primary_flow_name].kickoff_async(inputs=specialist_context)
                
                # Process result like main workflow does
                if isinstance(specialist_result, dict) and specialist_result.get('success'):
                    flow_results = specialist_result.get('results', [])
                    enhanced_agents = specialist_result.get('enhanced_agents_used', [])
                    tools_performance = specialist_result.get('tools_performance', {})
                    
                    print(f"      ✅ {primary_flow_name} flow completed: {len(flow_results)} results")
                    print(f"         Enhanced agents: {', '.join(enhanced_agents)}")
                    print(f"         Tools performance: {tools_performance}")
                    
                    # Extract content from results for final answer
                    if flow_results:
                        # Get the best result content
                        best_result = flow_results[0] if flow_results else {}
                        result_content = best_result.get('content', str(specialist_result))
                        final_result = result_content
                    else:
                        final_result = str(specialist_result)
                else:
                    print(f"      ⚠️ {primary_flow_name} completed but returned no results")
                    final_result = f"Standard workflow analysis completed for: {query}. {primary_flow_name} was engaged but encountered issues."
            else:
                # Fallback if specialist flow not available
                print(f"      ⚠️ {primary_flow_name} not available, using fallback")
                final_result = f"Standard analysis for: {query}. Primary specialist {primary_flow_name} was not available."
            
            # Store final result in inputs
            inputs['final_result'] = final_result
            inputs['workflow_completed'] = True
            inputs['current_stage'] = "standard_complete"
            
            print("✅ Standard workflow completed successfully")
            return final_result
            
        except Exception as e:
            error_msg = f"Standard workflow execution failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            # Return simple fallback result
            inputs = getattr(self.state, '_inputs', {})
            query = inputs.get('original_query', 'What is AI?')
            inputs['final_result'] = f"Analysis completed for: {query}"
            return f"Analysis completed for: {query}"
    
    @listen(or_(execute_complex_workflow, execute_parallel_workflow, execute_standard_workflow))
    async def finalize_workflow(self):
        """Enhanced finalization with automated evaluation and training data collection."""
        print("\n🏁 PHASE: Advanced Workflow Finalization with Automated Evaluation")
        print("-" * 70)
        
        # Update stage
        inputs = getattr(self.state, '_inputs', {})
        inputs['current_stage'] = "finalization"
        
        try:
            # Get final result and other data from inputs storage
            final_result = inputs.get('final_result', 'No result generated')
            query = inputs.get('original_query', '')
            workflow_id = inputs.get('workflow_id', self.flow_id)
            
            # Collect final metrics for analysis
            final_metrics = await self._collect_metrics()
            
            # Create comprehensive workflow result structure
            workflow_result = {
                'final_answer': final_result,
                'workflow_id': workflow_id,
                'execution_time': final_metrics.get('average_execution_time', 0),
                'complexity_score': inputs.get('complexity_score', 0),
                'tools_used': inputs.get('tools_used', []),
                'enhanced_agents_used': inputs.get('enhanced_agents_used', []),
                'workflow_type': 'AdvancedCoordinationFlow',
                'final_metrics': final_metrics
            }
            
            # Run automated evaluation if enabled
            if self.evaluation_enabled and self.evaluation_pipeline:
                await self._run_automated_evaluation(inputs, workflow_result)
            
            # Collect training data with quality metrics
            await self._collect_training_data(inputs, workflow_result)
            
            # Generate summary
            summary = {
                'session_id': workflow_id,
                'query': query,
                'final_result': final_result,
                'current_stage': inputs.get('current_stage', 'finalization'),
                'total_errors': final_metrics.get('total_errors', 0),
                'status': 'completed',
                'workflow_result': workflow_result
            }
            
            print(f"🎯 Enhanced Workflow completed successfully:")
            print(f"   Session: {workflow_id[:8]}..." if workflow_id else "   Session: N/A")
            print(f"   Stage: finalization")
            print(f"   Enhancement features: Advanced Coordination Flow")
            print(f"   Evaluation enabled: {self.evaluation_enabled}")
            print(f"   Training data collected: True")
            
            # Set completion flag
            inputs['workflow_completed'] = True
            inputs['final_evaluation_complete'] = True
            inputs['training_data_collected'] = True
            
            return summary
            
        except Exception as e:
            error_msg = f"Finalization failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {'status': 'error', 'error': error_msg}
    
    # Helper methods
    
    async def _run_automated_evaluation(self, inputs: Dict[str, Any], workflow_result: Dict[str, Any]):
        """Run comprehensive automated evaluation on workflow result."""
        query = inputs.get('original_query', '')
        final_answer = workflow_result.get('final_answer', '')
        
        try:
            print("🔍 Running automated evaluation on advanced flow result...")
            
            # DEBUG: Log evaluation attempt
            self.debug_logger.log_evaluation_execution(
                query, final_answer, None, self.evaluation_enabled
            )
            
            if not self.evaluation_pipeline:
                error_msg = "Evaluation pipeline not initialized despite evaluation_enabled=True"
                self.debug_logger.log_evaluation_execution(
                    query, final_answer, None, self.evaluation_enabled, error_msg
                )
                print(f"❌ {error_msg}")
                return
            
            # Extract information for evaluation
            
            # Run comprehensive evaluation
            evaluation_results = await self.evaluation_pipeline.evaluate_comprehensive(
                question=query,
                answer=final_answer,
                context={
                    'instructions': 'Provide comprehensive multi-agent analysis using advanced coordination',
                    'available_tools': workflow_result.get('tools_used', []),
                    'workflow_type': 'AdvancedCoordinationFlow',
                    'complexity_analysis': {
                        'score': workflow_result.get('complexity_score', 0),
                        'capabilities': inputs.get('required_capabilities', [])
                    },
                    'enhanced_agents_used': workflow_result.get('enhanced_agents_used', [])
                },
                tools_used=workflow_result.get('tools_used', [])
            )
            
            print(f"   📊 Evaluation complete: {evaluation_results.composite_score:.3f}")
            print(f"   📈 Relevance: {evaluation_results.relevance_score:.3f}")
            print(f"   📈 Coherence: {evaluation_results.coherence_score:.3f}")
            print(f"   📈 Instruction Following: {evaluation_results.instruction_following_score:.3f}")
            print(f"   📈 Tool Efficiency: {evaluation_results.tool_efficiency_score:.3f}")
            
            # DEBUG: Log successful evaluation
            self.debug_logger.log_evaluation_execution(
                query, final_answer, evaluation_results, self.evaluation_enabled
            )
            
            # Run quality gates if available
            quality_gate_results = {}
            if self.quality_gates:
                # Convert EvaluationResult to dict for quality gates
                evaluation_dict = evaluation_results.to_dict()
                quality_gate_results = await self.quality_gates.evaluate_quality_gates(
                    evaluation_dict,
                    {
                        'session_id': inputs.get('session_id', ''),
                        'query': query,
                        'workflow_type': 'AdvancedCoordinationFlow'
                    }
                )
                
                passed_gates = len(quality_gate_results.passed_gates)
                failed_gates = len(quality_gate_results.failed_gates)
                print(f"   🚪 Quality gates: {passed_gates} passed, {failed_gates} failed")
                print(f"   🏆 Overall quality: {quality_gate_results.overall_quality.value}")
                
                # DEBUG: Log quality gates
                try:
                    self.debug_logger.log_quality_gates(
                        evaluation_dict, quality_gate_results, True
                    )
                except Exception as e:
                    print(f"   ⚠️ Quality gates logging failed: {e}")
                    # Continue without logging rather than failing
            
            # Add evaluation results to workflow result
            print(f"   🔍 DEBUG - About to update workflow_result with evaluation data...")
            print(f"   🔍 DEBUG - evaluation_results.composite_score: {evaluation_results.composite_score}")
            print(f"   🔍 DEBUG - quality_gate_results type: {type(quality_gate_results)}")
            
            quality_gate_dict = {}
            if quality_gate_results and hasattr(quality_gate_results, 'to_dict'):
                print(f"   🔍 DEBUG - Converting quality_gate_results to dict...")
                quality_gate_dict = quality_gate_results.to_dict()
            elif quality_gate_results:
                # Handle case where quality_gate_results is already a dict
                print(f"   🔍 DEBUG - Using quality_gate_results as-is (already dict or similar)...")
                quality_gate_dict = quality_gate_results if isinstance(quality_gate_results, dict) else {}
            
            print(f"   🔍 DEBUG - Calling workflow_result.update()...")
            workflow_result.update({
                'evaluation_results': evaluation_results.to_dict(),
                'quality_gate_results': quality_gate_dict,
                'answer_quality_score': evaluation_results.composite_score
            })
            
            print(f"   🔍 DEBUG - Successfully updated workflow_result!")
            print(f"   🔍 DEBUG - workflow_result now has keys: {list(workflow_result.keys())}")
            print(f"   🔍 DEBUG - answer_quality_score now: {workflow_result.get('answer_quality_score', 'STILL MISSING')}")
            
            print("   ✅ Automated evaluation completed successfully")
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ⚠️ Automated evaluation failed: {error_msg}")
            
            # DEBUG: Log evaluation failure
            self.debug_logger.log_evaluation_execution(
                query, final_answer, None, self.evaluation_enabled, error_msg
            )
            # Continue without evaluation rather than failing the whole workflow
    
    async def _collect_training_data(self, inputs: Dict[str, Any], workflow_result: Dict[str, Any]):
        """Collect training data with quality metrics for continuous learning."""
        session_id = inputs.get('session_id', str(uuid.uuid4()))
        query = inputs.get('original_query', '')
        
        try:
            print("💾 Collecting training data for continuous learning...")
            
            # Import training data collector
            from ...optimization.dspy.training_data_collector import get_training_data_collector
            
            training_collector = get_training_data_collector()
            
            # Prepare training example with comprehensive context
            example_id = await training_collector.collect_training_example(
                session_id=session_id,
                original_query=query,
                optimized_query=inputs.get('processed_query', query),
                workflow_result=workflow_result,
                context={
                    'workflow_type': 'AdvancedCoordinationFlow',
                    'complexity_analysis': {
                        'score': workflow_result.get('complexity_score', 0),
                        'capabilities': inputs.get('required_capabilities', [])
                    },
                    'tools_used': workflow_result.get('tools_used', []),
                    'enhanced_agents_used': workflow_result.get('enhanced_agents_used', []),
                    'execution_metrics': workflow_result.get('final_metrics', {}),
                    'evaluation_enabled': self.evaluation_enabled
                },
                quality_metrics={
                    'answer_quality_score': workflow_result.get('answer_quality_score', 0.0),
                    'relevance_score': workflow_result.get('evaluation_results', {}).get('relevance_score', 0.0),
                    'coherence_score': workflow_result.get('evaluation_results', {}).get('coherence_score', 0.0),
                    'instruction_following_score': workflow_result.get('evaluation_results', {}).get('instruction_following_score', 0.0),
                    'tool_efficiency_score': workflow_result.get('evaluation_results', {}).get('tool_efficiency_score', 0.0)
                }
            )
            
            print(f"   ✅ Training example collected: {example_id}")
            print(f"   📊 Quality score: {workflow_result.get('answer_quality_score', 0.0):.3f}")
            
            # Get current dataset stats to check if optimization should trigger
            stats = await training_collector.get_dataset_stats()
            print(f"   📈 Dataset now has {stats['total_examples']} examples (avg quality: {stats['avg_quality']:.3f})")
            
            if stats['total_examples'] >= training_collector.min_examples_for_optimization:
                print(f"   🚀 Optimization threshold reached! ({stats['total_examples']} >= {training_collector.min_examples_for_optimization})")
            
            # DEBUG: Validate training data was stored correctly
            print(f"   🔍 DEBUG - workflow_result keys: {list(workflow_result.keys())}")
            print(f"   🔍 DEBUG - workflow_result['answer_quality_score']: {workflow_result.get('answer_quality_score', 'MISSING')}")
            print(f"   🔍 DEBUG - workflow_result['evaluation_results']: {workflow_result.get('evaluation_results', 'MISSING')}")
            if isinstance(workflow_result.get('evaluation_results'), dict):
                eval_results = workflow_result.get('evaluation_results', {})
                print(f"   🔍 DEBUG - evaluation_results keys: {list(eval_results.keys())}")
                print(f"   🔍 DEBUG - evaluation_results['relevance_score']: {eval_results.get('relevance_score', 'MISSING')}")
                print(f"   🔍 DEBUG - evaluation_results['coherence_score']: {eval_results.get('coherence_score', 'MISSING')}")
            
            quality_metrics = {
                'answer_quality_score': workflow_result.get('answer_quality_score', 0.0),
                'relevance_score': workflow_result.get('evaluation_results', {}).get('relevance_score', 0.0),
                'coherence_score': workflow_result.get('evaluation_results', {}).get('coherence_score', 0.0),
                'instruction_following_score': workflow_result.get('evaluation_results', {}).get('instruction_following_score', 0.0),
                'tool_efficiency_score': workflow_result.get('evaluation_results', {}).get('tool_efficiency_score', 0.0)
            }
            print(f"   🔍 DEBUG - final quality_metrics: {quality_metrics}")
            
            # Verify the data was actually stored
            stored_correctly = example_id is not None and len(str(example_id)) > 0
            
            self.debug_logger.log_training_data_collection(
                session_id, query, workflow_result, example_id, 
                quality_metrics, stored_correctly
            )
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ⚠️ Training data collection failed: {error_msg}")
            
            # DEBUG: Log training data collection failure
            self.debug_logger.log_training_data_collection(
                session_id, query, workflow_result, None, {}, False, error_msg
            )
            # Continue without training data collection rather than failing the whole workflow
    
    def _select_primary_agent(self) -> str:
        """Select primary agent based on query characteristics. Prioritize specialist flows like main workflow."""
        inputs = getattr(self.state, '_inputs', {})
        query = inputs.get('original_query', '')
        query_lower = query.lower()
        capabilities = inputs.get('required_capabilities', [])
        capabilities_lower = [cap.lower() for cap in capabilities]
        
        # Check for mathematical/computational requirements -> use librarian flow (has math capabilities)
        if any(word in query_lower for word in ['calculate', 'math', 'equation', 'data', 'statistics']):
            return 'librarian_flow'  # Math-enabled knowledge specialist flow
        
        # Check for search-intensive requirements -> use researcher flow (has web search)
        if any(word in query_lower for word in ['find', 'search', 'latest', 'current', 'recent']):
            return 'researcher_flow'  # Enhanced search specialist flow
        
        # Check for analysis requirements -> use data processor flow
        if any(word in query_lower for word in ['analyze', 'analysis', 'compare', 'trends']):
            return 'data_processor_flow'  # Enhanced data processor flow
        
        # Check capabilities for more specific routing
        if 'reasoning' in capabilities_lower or 'analysis' in capabilities_lower:
            return 'librarian_flow'  # Has ReAct reasoning + math
        
        if 'search' in capabilities_lower or 'research' in capabilities_lower:
            return 'researcher_flow'  # Has ColBERTv2 + ReAct search
        
        # For simple queries like "What is AI?", use researcher flow (best for general knowledge)
        return 'researcher_flow'
    
    async def _collect_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive performance metrics."""
        total_tasks = len(self.state.task_results)
        successful_tasks = sum(1 for r in self.state.task_results if r.success)
        
        execution_times = [r.execution_time for r in self.state.task_results if r.execution_time > 0]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        return {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'success_rate': successful_tasks / total_tasks if total_tasks > 0 else 0,
            'average_execution_time': avg_execution_time,
            'total_errors': len(self.state.errors_encountered),
            'complexity_score': self.state.complexity_score,
            'workflow_type': self.state.current_stage,
            'parallel_eligible': self.state.parallel_eligible
        } 