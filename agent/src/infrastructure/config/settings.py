"""
Simple configuration management for the Multi-Agent DSPy system.

Provides environment variable support, YAML configuration, and runtime parameters
suitable for prototype development.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
from langchain_openai import Chat<PERSON>penAI


@dataclass
class LLMConfig:
    """Configuration for language models."""
    provider: str = "openai"  # openai, anthropic, local, etc.
    
    # Default model (fallback)
    model_name: str = "gpt-4.1-mini"
    
    # Specialized models for different roles (cost-effective primary configuration)
    teacher_model: str = "gpt-4.1-mini"      # DSPy optimization teacher ($0.40/$1.60)
    task_manager_model: str = "gpt-4.1-nano"  # Task coordination ($0.10/$0.40)
    researcher_model: str = "gpt-4.1-mini"    # Web research ($0.40/$1.60)
    librarian_model: str = "gpt-4.1-nano"     # Document retrieval ($0.10/$0.40)
    data_processor_model: str = "gpt-4.1-mini" # Data analysis ($0.40/$1.60)
    writer_model: str = "gpt-4.1-mini"        # Final synthesis ($0.40/$1.60)
    
    api_key: Optional[str] = "********************************************************************************************************************************************************************"  # Production API key
    base_url: Optional[str] = None
    max_tokens: int = 20000
    temperature: float = 0.7
    timeout: int = 30
    
    # Cost optimization features
    enable_caching: bool = True         # 75% savings on cached inputs
    use_batch_api: bool = False         # 50% additional savings for non-realtime
    cache_system_prompts: bool = True   # Cache common system prompts
    cache_training_examples: bool = True # Cache DSPy training examples
    
    def get_model_for_agent(self, agent_type: str) -> str:
        """Get the appropriate model for a specific agent type."""
        model_mapping = {
            "teacher": self.teacher_model,
            "task_manager": self.task_manager_model,
            "researcher": self.researcher_model,
            "librarian": self.librarian_model,
            "data_processor": self.data_processor_model,
            "writer": self.writer_model,
        }
        return model_mapping.get(agent_type, self.model_name)


@dataclass
class ToolConfig:
    """Configuration for tools."""
    web_search_delay: float = 1.5  # Delay between web searches
    max_search_results: int = 5
    document_max_length: int = 5000
    analysis_types: List[str] = field(default_factory=lambda: ["summary", "keywords", "sentiment_basic"])


@dataclass
class OptimizationConfig:
    """Configuration for DSPy optimization."""
    enable_optimization: bool = True
    max_bootstrapped_demos: int = 4
    max_labeled_demos: int = 16
    num_candidate_programs: int = 8
    num_threads: int = 4
    similarity_threshold: float = 0.7


@dataclass
class SystemConfig:
    """Main system configuration."""
    # Core settings
    debug_mode: bool = False
    log_level: str = "INFO"
    max_concurrent_tasks: int = 3
    
    # Component configurations
    llm: LLMConfig = field(default_factory=LLMConfig)
    tools: ToolConfig = field(default_factory=ToolConfig)
    optimization: OptimizationConfig = field(default_factory=OptimizationConfig)
    
    # Paths
    data_dir: str = "./data"
    cache_dir: str = "./cache"
    logs_dir: str = "./logs"


class ConfigManager:
    """Simple configuration manager for prototype use."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config = SystemConfig()
        self._load_configuration()
    
    def _load_configuration(self):
        """Load configuration from file and environment variables."""
        # Try to load from YAML file
        self._load_from_yaml()
        
        # Override with environment variables
        self._load_from_env()
        
        # Ensure CrewAI environment variables are set
        self._ensure_crewai_env_vars()
        
        # Validate configuration
        self._validate_config()
    
    def _load_from_yaml(self):
        """Load configuration from YAML file."""
        config_path = Path(self.config_file)
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                
                if yaml_config:
                    # Handle API keys section specially
                    if 'api_keys' in yaml_config:
                        api_keys = yaml_config['api_keys']
                        if 'serper' in api_keys and not os.getenv("SERPER_API_KEY"):
                            os.environ["SERPER_API_KEY"] = api_keys['serper']
                            print("✅ Set SERPER_API_KEY environment variable from config")
                    
                    self._update_config_from_dict(yaml_config)
                    print(f"✅ Configuration loaded from {config_path}")
            except Exception as e:
                print(f"⚠️ Error loading config file {config_path}: {e}")
        else:
            print(f"ℹ️ Config file {config_path} not found, using defaults")
    
    def _load_from_env(self):
        """Load configuration from environment variables."""
        env_mappings = {
            # LLM Configuration
            'OPENAI_API_KEY': ('llm', 'api_key'),
            'LLM_PROVIDER': ('llm', 'provider'),
            'LLM_MODEL': ('llm', 'model_name'),
            'LLM_TEMPERATURE': ('llm', 'temperature', float),
            'LLM_MAX_TOKENS': ('llm', 'max_tokens', int),
            
            # Specialized Model Configuration
            'TEACHER_MODEL': ('llm', 'teacher_model'),
            'TASK_MANAGER_MODEL': ('llm', 'task_manager_model'),
            'RESEARCHER_MODEL': ('llm', 'researcher_model'),
            'LIBRARIAN_MODEL': ('llm', 'librarian_model'),
            'DATA_PROCESSOR_MODEL': ('llm', 'data_processor_model'),
            'WRITER_MODEL': ('llm', 'writer_model'),
            
            # Cost Optimization Configuration
            'ENABLE_CACHING': ('llm', 'enable_caching', bool),
            'USE_BATCH_API': ('llm', 'use_batch_api', bool),
            'CACHE_SYSTEM_PROMPTS': ('llm', 'cache_system_prompts', bool),
            'CACHE_TRAINING_EXAMPLES': ('llm', 'cache_training_examples', bool),
            
            # System Configuration
            'DEBUG_MODE': ('debug_mode',),
            'LOG_LEVEL': ('log_level',),
            'MAX_CONCURRENT_TASKS': ('max_concurrent_tasks',),
            
            # Tool Configuration
            'WEB_SEARCH_DELAY': ('tools', 'web_search_delay', float),
            'MAX_SEARCH_RESULTS': ('tools', 'max_search_results', int),
            
            # Optimization Configuration
            'ENABLE_OPTIMIZATION': ('optimization', 'enable_optimization', bool),
            'MAX_THREADS': ('optimization', 'num_threads', int),
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                self._set_config_value(config_path, env_value)
    
    def _set_config_value(self, config_path: tuple, value: str):
        """Set configuration value from environment variable."""
        try:
            # Handle nested configuration
            if len(config_path) == 2:
                section, key = config_path
                section_obj = getattr(self.config, section)
                setattr(section_obj, key, value)
            elif len(config_path) == 3:
                section, key, value_type = config_path
                section_obj = getattr(self.config, section)
                converted_value = value_type(value)
                setattr(section_obj, key, converted_value)
            else:
                # Handle single-level config
                key = config_path[0]
                # Convert boolean strings
                if value.lower() in ('true', 'false'):
                    converted_value = value.lower() == 'true'
                # Convert numeric strings
                elif value.isdigit():
                    converted_value = int(value)
                # Keep as string
                else:
                    converted_value = value
                setattr(self.config, key, converted_value)

        except (ValueError, AttributeError, TypeError) as e:
            print(f"⚠️ Error setting config value {config_path}: {e}")
            print(f"   Value: {value}, Type: {type(value)}")
            # Continue without failing - use default values
    
    def _ensure_crewai_env_vars(self):
        """Ensure CrewAI environment variables are set from configuration."""
        # Set OpenAI API key for CrewAI if not already set and we have one in config
        if self.config.llm.provider == "openai" and self.config.llm.api_key:
            if not os.getenv("OPENAI_API_KEY"):
                os.environ["OPENAI_API_KEY"] = self.config.llm.api_key
                print("✅ Set OPENAI_API_KEY environment variable for CrewAI")
            
            # Also set the base URL if configured
            if self.config.llm.base_url and not os.getenv("OPENAI_BASE_URL"):
                os.environ["OPENAI_BASE_URL"] = self.config.llm.base_url
                print("✅ Set OPENAI_BASE_URL environment variable for CrewAI")
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary."""
        for section, values in config_dict.items():
            if hasattr(self.config, section) and isinstance(values, dict):
                section_obj = getattr(self.config, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
            elif hasattr(self.config, section):
                setattr(self.config, section, values)
    
    def _validate_config(self):
        """Validate configuration values."""
        # Check for required API key
        if self.config.llm.provider == "openai" and not self.config.llm.api_key:
            print("⚠️ Warning: OpenAI API key not set. Set OPENAI_API_KEY environment variable.")
        
        # Ensure directories exist
        for dir_path in [self.config.data_dir, self.config.cache_dir, self.config.logs_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # Validate numeric values
        if self.config.tools.web_search_delay < 0.5:
            print("⚠️ Warning: Web search delay is very low, may trigger rate limiting")
        
        if self.config.optimization.num_threads > 10:
            print("⚠️ Warning: High thread count may cause performance issues")
    
    def get_config(self) -> SystemConfig:
        """Get the current configuration."""
        return self.config
    
    def save_config(self, file_path: Optional[str] = None):
        """Save current configuration to YAML file."""
        output_file = file_path or self.config_file
        
        config_dict = {
            'llm': {
                'provider': self.config.llm.provider,
                'model_name': self.config.llm.model_name,
                # Specialized models
                'teacher_model': self.config.llm.teacher_model,
                'task_manager_model': self.config.llm.task_manager_model,
                'researcher_model': self.config.llm.researcher_model,
                'librarian_model': self.config.llm.librarian_model,
                'data_processor_model': self.config.llm.data_processor_model,
                'writer_model': self.config.llm.writer_model,
                # LLM parameters
                'max_tokens': self.config.llm.max_tokens,
                'temperature': self.config.llm.temperature,
                'timeout': self.config.llm.timeout,
                # Cost optimization
                'enable_caching': self.config.llm.enable_caching,
                'use_batch_api': self.config.llm.use_batch_api,
                'cache_system_prompts': self.config.llm.cache_system_prompts,
                'cache_training_examples': self.config.llm.cache_training_examples,
                # Don't save API key to file for security
            },
            'tools': {
                'web_search_delay': self.config.tools.web_search_delay,
                'max_search_results': self.config.tools.max_search_results,
                'document_max_length': self.config.tools.document_max_length,
                'analysis_types': self.config.tools.analysis_types,
            },
            'optimization': {
                'enable_optimization': self.config.optimization.enable_optimization,
                'max_bootstrapped_demos': self.config.optimization.max_bootstrapped_demos,
                'max_labeled_demos': self.config.optimization.max_labeled_demos,
                'num_candidate_programs': self.config.optimization.num_candidate_programs,
                'num_threads': self.config.optimization.num_threads,
                'similarity_threshold': self.config.optimization.similarity_threshold,
            },
            'system': {
                'debug_mode': self.config.debug_mode,
                'log_level': self.config.log_level,
                'max_concurrent_tasks': self.config.max_concurrent_tasks,
                'data_dir': self.config.data_dir,
                'cache_dir': self.config.cache_dir,
                'logs_dir': self.config.logs_dir,
            }
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            print(f"✅ Configuration saved to {output_file}")
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")


# Global configuration instance
_config_manager = None


def get_config() -> SystemConfig:
    """Get the global configuration instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager.get_config()


def initialize_config(config_file: Optional[str] = None) -> SystemConfig:
    """Initialize configuration with optional config file."""
    global _config_manager
    _config_manager = ConfigManager(config_file)
    return _config_manager.get_config()


def save_config(file_path: Optional[str] = None):
    """Save current configuration to file."""
    global _config_manager
    if _config_manager:
        _config_manager.save_config(file_path)


def get_model_for_agent(agent_type: str) -> str:
    """Get the appropriate model for a specific agent type."""
    config = get_config()
    return config.llm.get_model_for_agent(agent_type)


def get_llm_config_dict() -> Dict[str, Any]:
    """Get LLM configuration as dictionary for external libraries."""
    config = get_config()
    return {
        "provider": config.llm.provider,
        "api_key": config.llm.api_key,
        "base_url": config.llm.base_url,
        "temperature": config.llm.temperature,
        "max_tokens": config.llm.max_tokens,
        "timeout": config.llm.timeout,
        "enable_caching": config.llm.enable_caching,
        "use_batch_api": config.llm.use_batch_api,
    }


def create_model_config_for_component(component: str) -> Dict[str, Any]:
    """Create model configuration for specific system components."""
    config = get_config()
    base_config = get_llm_config_dict()
    
    # Map component to appropriate model
    component_model_map = {
        "dspy": config.llm.teacher_model,
        "crewai": config.llm.model_name,  # Default for general CrewAI use
        "task_manager": config.llm.task_manager_model,
        "researcher": config.llm.researcher_model,
        "librarian": config.llm.librarian_model,
        "data_processor": config.llm.data_processor_model,
        "writer": config.llm.writer_model,
        "teacher": config.llm.teacher_model,
    }
    
    model_name = component_model_map.get(component, config.llm.model_name)
    
    return {
        **base_config,
        "model_name": model_name,
        "model": model_name,  # Alternative key name for some libraries
    }


def get_llm_for_agent(agent_type: str, temperature: Optional[float] = None, **kwargs):
    """
    Get the appropriate LLM object for an agent type with DSPy-compatible optimization support.
    
    This function allows DSPy optimizers to override generation parameters like temperature
    while keeping infrastructure parameters (timeout, max_retries) fixed for system stability.
    
    Args:
        agent_type: Type of agent (researcher, librarian, etc.)
        temperature: Override temperature (DSPy can optimize this parameter)
        **kwargs: Additional LM parameters that DSPy might want to optimize
    
    Returns:
        Configured LLM instance with optimizable parameters
    """
    config = get_config()
    
    # Map agent types to specialized models for cost optimization
    model_mapping = {
        "task_manager": config.llm.task_manager_model,
        "researcher": config.llm.researcher_model,
        "librarian": config.llm.librarian_model,
        "data_processor": config.llm.data_processor_model,
        "writer": config.llm.writer_model,
        "complexity_analyzer": config.llm.model_name,  # Use main model for DSPy
        "search_specialist": config.llm.researcher_model,
        "knowledge_specialist": config.llm.librarian_model
    }
    
    model_name = model_mapping.get(agent_type, config.llm.model_name)
    
    # Default generation parameters (DSPy can optimize these)
    generation_params = {
        'model': model_name,
        'temperature': temperature if temperature is not None else 0.1,  # DSPy optimizable
        'max_tokens': kwargs.get('max_tokens', 25000),  # DSPy could optimize this
        'top_p': kwargs.get('top_p', 1.0),  # DSPy optimizable
        'frequency_penalty': kwargs.get('frequency_penalty', 0.0),  # DSPy optimizable
        'presence_penalty': kwargs.get('presence_penalty', 0.0),  # DSPy optimizable
    }
    
    # Infrastructure parameters (NOT optimizable by DSPy - system stability)
    infrastructure_params = {
        'timeout': 90,  # Fixed - system stability requirement
        'max_retries': 3,  # Fixed - system reliability requirement
        'api_key': config.llm.api_key
    }
    
    # Combine parameters
    all_params = {**generation_params, **infrastructure_params}
    
    # Create LLM instance
    try:
        # Check for litellm compatibility
        if 'openai/' in model_name or model_name.startswith('gpt-'):
            from litellm import completion
            # For LiteLLM, we need to handle the model parameter differently
            if not model_name.startswith('openai/'):
                all_params['model'] = f"openai/{model_name}"
            
        # Try LangChain OpenAI first
        from langchain_openai import ChatOpenAI
        
        # Convert max_tokens to max_tokens for OpenAI
        openai_params = all_params.copy()
        if 'max_tokens' in openai_params:
            openai_params['max_tokens'] = openai_params['max_tokens']
        
        llm = ChatOpenAI(**openai_params)
        
        print(f"✅ LLM configured for {agent_type}: {model_name}")
        if temperature is not None:
            print(f"   🎯 DSPy temperature override: {temperature}")
        
        return llm
        
    except Exception as e:
        print(f"⚠️ LLM setup failed for {agent_type}: {str(e)}")
        # Fallback to basic configuration
        fallback_params = {
            'model': 'gpt-4o-mini',
            'temperature': temperature if temperature is not None else 0.1,
            'max_tokens': 25000,
            'timeout': 90,
            'max_retries': 3,
            'api_key': config.llm.api_key
        }
        
        try:
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(**fallback_params)
        except Exception as fallback_error:
            print(f"❌ Fallback LLM creation failed: {str(fallback_error)}")
            raise fallback_error


def get_current_date_context() -> str:
    """
    Get current date and time context for agent prompts.
    
    Returns:
        Formatted string with current date and time information for agent context
    """
    from datetime import datetime
    
    current_time = datetime.now()
    
    # Format the current date context
    date_context = f"""
IMPORTANT: Current Date and Time Context:
- Today's Date: {current_time.strftime('%A, %B %d, %Y')}
- Current Time: {current_time.strftime('%I:%M %p %Z')}
- Year: {current_time.year}
"""
    
    return date_context.strip()


def enhance_backstory_with_current_date(original_backstory: str) -> str:
    """
    Enhance an agent backstory with current date context.
    
    Args:
        original_backstory: The original agent backstory
        
    Returns:
        Enhanced backstory with current date awareness
    """
    date_context = get_current_date_context()
    
    enhanced_backstory = f"""{original_backstory}

{date_context}"""
    
    return enhanced_backstory


def create_dspy_llm_for_optimization(agent_type: str = "default", **optimization_params):
    """
    Create LLM instance specifically for DSPy optimization with full parameter control.
    
    This function is designed to be called by DSPy optimizers like MIPROv2 that need
    to create LLM instances with specific parameters during optimization trials.
    
    Args:
        agent_type: Type of agent being optimized
        **optimization_params: Parameters DSPy wants to optimize (temperature, etc.)
    
    Returns:
        LLM instance configured for DSPy optimization
    """
    config = get_config()
    
    # Use specialized model for the agent type
    model_mapping = {
        "task_manager": config.llm.task_manager_model,
        "researcher": config.llm.researcher_model,
        "librarian": config.llm.librarian_model,
        "data_processor": config.llm.data_processor_model,
        "writer": config.llm.writer_model,
        "complexity_analyzer": config.llm.model_name,
        "default": config.llm.model_name
    }
    
    model_name = model_mapping.get(agent_type, config.llm.model_name)
    
    # DSPy optimization parameters (these can be overridden by optimizer)
    dspy_params = {
        'model': model_name,
        'temperature': optimization_params.get('temperature', 0.1),
        'max_tokens': optimization_params.get('max_tokens', 25000),
        'top_p': optimization_params.get('top_p', 1.0),
        'frequency_penalty': optimization_params.get('frequency_penalty', 0.0),
        'presence_penalty': optimization_params.get('presence_penalty', 0.0),
        # Infrastructure parameters remain fixed for system stability
        'timeout': 90,
        'max_retries': 3,
        'api_key': config.llm.api_key
    }
    
    # Allow DSPy to override any additional parameters
    dspy_params.update(optimization_params)
    
    try:
        from langchain_openai import ChatOpenAI
        llm = ChatOpenAI(**dspy_params)
        
        print(f"✅ DSPy optimization LLM created for {agent_type}: {model_name}")
        if 'temperature' in optimization_params:
            print(f"   🎯 DSPy optimizing temperature: {optimization_params['temperature']}")
        
        return llm
        
    except Exception as e:
        print(f"❌ DSPy LLM creation failed for {agent_type}: {str(e)}")
        raise e


# Example usage and testing
if __name__ == "__main__":
    print("🧪 Testing configuration system...")
    
    # Initialize configuration
    config = initialize_config()
    
    print(f"LLM Provider: {config.llm.provider}")
    print(f"Model: {config.llm.model_name}")
    print(f"Debug Mode: {config.debug_mode}")
    print(f"Web Search Delay: {config.tools.web_search_delay}")
    print(f"Optimization Enabled: {config.optimization.enable_optimization}")
    
    # Test saving configuration
    save_config("test_config.yaml")
    
    print("✅ Configuration system test completed!") 