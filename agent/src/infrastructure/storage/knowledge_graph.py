from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import logging
import numpy as np

# Graph library imports with fallbacks
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

try:
    from pyvis.network import Network
    PYVIS_AVAILABLE = True
except ImportError:
    PYVIS_AVAILABLE = False

class KnowledgeGraphManager:
    """
    Advanced knowledge graph for relationship modeling and reasoning.
    
    Features:
    - Entity and relationship extraction
    - Graph-based reasoning and inference
    - Integration with vector search
    - Visual graph exploration
    - Temporal relationship tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.entity_embeddings = {}
        self.relationship_types = set()
        self.logger = logging.getLogger(__name__)
        
        # Initialize graph
        if NETWORKX_AVAILABLE:
            self.graph = nx.MultiDiGraph()
        else:
            self.logger.warning("NetworkX not available, using simple dict-based graph")
            self.graph = {"nodes": {}, "edges": []}
            
        # Graph settings
        self.max_entities = config.get('max_entities', 100000)
        self.relationship_threshold = config.get('relationship_threshold', 0.8)
        self.visualization_max_nodes = config.get('visualization_max_nodes', 200)
        
    async def initialize(self):
        """Initialize the knowledge graph."""
        self.logger.info("Knowledge graph manager initialized")
        print("✅ Knowledge graph manager initialized")
    
    async def add_entity(
        self, 
        entity_id: str, 
        entity_type: str, 
        properties: Dict[str, Any],
        embedding: Optional[List[float]] = None
    ):
        """Add entity to knowledge graph with optional embedding."""
        if NETWORKX_AVAILABLE:
            self.graph.add_node(
                entity_id,
                type=entity_type,
                properties=properties,
                created_at=datetime.now().isoformat()
            )
        else:
            self.graph["nodes"][entity_id] = {
                "type": entity_type,
                "properties": properties,
                "created_at": datetime.now().isoformat()
            }
        
        if embedding:
            self.entity_embeddings[entity_id] = embedding
        
        self.logger.debug(f"Added entity: {entity_id} ({entity_type})")
    
    async def add_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        properties: Dict[str, Any] = None,
        confidence: float = 1.0
    ):
        """Add relationship between entities."""
        if NETWORKX_AVAILABLE:
            self.graph.add_edge(
                source_id,
                target_id,
                type=relationship_type,
                properties=properties or {},
                confidence=confidence,
                created_at=datetime.now().isoformat()
            )
        else:
            self.graph["edges"].append({
                "source": source_id,
                "target": target_id,
                "type": relationship_type,
                "properties": properties or {},
                "confidence": confidence,
                "created_at": datetime.now().isoformat()
            })
        
        self.relationship_types.add(relationship_type)
        self.logger.debug(f"Added relationship: {source_id} -[{relationship_type}]-> {target_id}")
    
    async def find_related_entities(
        self,
        entity_id: str,
        max_depth: int = 2,
        relationship_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Find entities related to given entity within specified depth."""
        if NETWORKX_AVAILABLE:
            return await self._find_related_networkx(entity_id, max_depth, relationship_types)
        else:
            return await self._find_related_simple(entity_id, max_depth, relationship_types)
    
    async def _find_related_networkx(
        self,
        entity_id: str,
        max_depth: int,
        relationship_types: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """Find related entities using NetworkX."""
        if entity_id not in self.graph:
            return []
        
        related = []
        visited = set()
        queue = [(entity_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if depth > max_depth or current_id in visited:
                continue
                
            visited.add(current_id)
            
            # Get neighbors
            for neighbor in self.graph.neighbors(current_id):
                edge_data = self.graph.get_edge_data(current_id, neighbor)
                
                for edge in edge_data.values():
                    if (relationship_types is None or 
                        edge.get('type') in relationship_types):
                        
                        related.append({
                            'entity_id': neighbor,
                            'relationship_type': edge.get('type'),
                            'depth': depth + 1,
                            'confidence': edge.get('confidence', 1.0),
                            'properties': self.graph.nodes[neighbor].get('properties', {})
                        })
                        
                        if depth + 1 <= max_depth:
                            queue.append((neighbor, depth + 1))
        
        return related
    
    async def _find_related_simple(
        self,
        entity_id: str,
        max_depth: int,
        relationship_types: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """Find related entities using simple graph structure."""
        if entity_id not in self.graph["nodes"]:
            return []
        
        related = []
        visited = set()
        queue = [(entity_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if depth > max_depth or current_id in visited:
                continue
                
            visited.add(current_id)
            
            # Find outgoing edges
            for edge in self.graph["edges"]:
                if edge["source"] == current_id:
                    target_id = edge["target"]
                    
                    if (relationship_types is None or 
                        edge.get('type') in relationship_types):
                        
                        related.append({
                            'entity_id': target_id,
                            'relationship_type': edge.get('type'),
                            'depth': depth + 1,
                            'confidence': edge.get('confidence', 1.0),
                            'properties': self.graph["nodes"].get(target_id, {}).get('properties', {})
                        })
                        
                        if depth + 1 <= max_depth:
                            queue.append((target_id, depth + 1))
        
        return related
    
    async def semantic_entity_search(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Tuple[str, float]]:
        """Find entities similar to query embedding."""
        if not self.entity_embeddings:
            return []
        
        similarities = []
        query_vec = np.array(query_embedding)
        
        for entity_id, embedding in self.entity_embeddings.items():
            entity_vec = np.array(embedding)
            similarity = np.dot(query_vec, entity_vec) / (
                np.linalg.norm(query_vec) * np.linalg.norm(entity_vec)
            )
            
            if similarity >= similarity_threshold:
                similarities.append((entity_id, similarity))
        
        # Sort by similarity and return top k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    async def extract_entities_from_text(
        self,
        text: str,
        entity_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract entities from text (simplified implementation).
        In production, this would use NER models like spaCy or Hugging Face.
        """
        # Simplified entity extraction - in production use proper NER
        entities = []
        
        # Basic keyword-based extraction
        keywords = {
            "PERSON": ["researcher", "scientist", "author", "developer", "engineer"],
            "ORGANIZATION": ["company", "university", "institute", "corporation"],
            "TECHNOLOGY": ["AI", "machine learning", "neural network", "algorithm"],
            "LOCATION": ["city", "country", "region", "area"]
        }
        
        text_lower = text.lower()
        
        for entity_type, type_keywords in keywords.items():
            if entity_types and entity_type not in entity_types:
                continue
                
            for keyword in type_keywords:
                if keyword in text_lower:
                    entities.append({
                        "text": keyword,
                        "type": entity_type,
                        "confidence": 0.8,
                        "start": text_lower.find(keyword),
                        "end": text_lower.find(keyword) + len(keyword)
                    })
        
        return entities
    
    async def generate_graph_visualization(
        self,
        output_path: str = "knowledge_graph.html",
        max_nodes: int = None
    ) -> str:
        """Generate interactive graph visualization."""
        if not PYVIS_AVAILABLE:
            self.logger.warning("Pyvis not available, cannot generate visualization")
            return "Visualization not available (pyvis not installed)"
        
        max_nodes = max_nodes or self.visualization_max_nodes
        
        net = Network(height="600px", width="100%", bgcolor="#222222", font_color="white")
        
        # Add nodes with limited count for performance
        nodes_added = 0
        
        if NETWORKX_AVAILABLE:
            for node_id, data in self.graph.nodes(data=True):
                if nodes_added >= max_nodes:
                    break
                    
                net.add_node(
                    node_id,
                    label=f"{data.get('type', '')}: {node_id}",
                    color=self._get_node_color(data.get('type')),
                    size=20
                )
                nodes_added += 1
            
            # Add edges
            for source, target, data in self.graph.edges(data=True):
                if source in [n['id'] for n in net.nodes] and target in [n['id'] for n in net.nodes]:
                    net.add_edge(
                        source,
                        target,
                        label=data.get('type', ''),
                        color=self._get_edge_color(data.get('type'))
                    )
        else:
            # Simple graph structure
            for node_id, node_data in list(self.graph["nodes"].items())[:max_nodes]:
                net.add_node(
                    node_id,
                    label=f"{node_data.get('type', '')}: {node_id}",
                    color=self._get_node_color(node_data.get('type')),
                    size=20
                )
                nodes_added += 1
            
            # Add edges
            for edge in self.graph["edges"]:
                source, target = edge["source"], edge["target"]
                if source in [n['id'] for n in net.nodes] and target in [n['id'] for n in net.nodes]:
                    net.add_edge(
                        source,
                        target,
                        label=edge.get('type', ''),
                        color=self._get_edge_color(edge.get('type'))
                    )
        
        try:
            net.save_graph(output_path)
            self.logger.info(f"Graph visualization saved to {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"Failed to save graph visualization: {e}")
            return f"Error generating visualization: {e}"
    
    def _get_node_color(self, node_type: str) -> str:
        """Get color for node based on type."""
        colors = {
            "PERSON": "#ff6b6b",
            "ORGANIZATION": "#4ecdc4",
            "TECHNOLOGY": "#45b7d1",
            "LOCATION": "#96ceb4",
            "CONCEPT": "#feca57",
            "EVENT": "#ff9ff3"
        }
        return colors.get(node_type, "#ddd")
    
    def _get_edge_color(self, edge_type: str) -> str:
        """Get color for edge based on type."""
        colors = {
            "WORKS_AT": "#ff6b6b",
            "LOCATED_IN": "#96ceb4",
            "USES": "#45b7d1",
            "RELATED_TO": "#ddd",
            "DEVELOPED": "#feca57"
        }
        return colors.get(edge_type, "#aaa")
    
    async def get_graph_statistics(self) -> Dict[str, Any]:
        """Get comprehensive graph statistics."""
        if NETWORKX_AVAILABLE:
            node_count = self.graph.number_of_nodes()
            edge_count = self.graph.number_of_edges()
            
            # Get node types distribution
            node_types = {}
            for node_id, data in self.graph.nodes(data=True):
                node_type = data.get('type', 'Unknown')
                node_types[node_type] = node_types.get(node_type, 0) + 1
        else:
            node_count = len(self.graph["nodes"])
            edge_count = len(self.graph["edges"])
            
            # Get node types distribution
            node_types = {}
            for node_data in self.graph["nodes"].values():
                node_type = node_data.get('type', 'Unknown')
                node_types[node_type] = node_types.get(node_type, 0) + 1
        
        return {
            "total_nodes": node_count,
            "total_edges": edge_count,
            "node_types": node_types,
            "relationship_types": list(self.relationship_types),
            "entities_with_embeddings": len(self.entity_embeddings),
            "graph_density": edge_count / (node_count * (node_count - 1)) if node_count > 1 else 0
        }
    
    async def find_shortest_path(
        self,
        source_id: str,
        target_id: str,
        relationship_types: Optional[List[str]] = None
    ) -> Optional[List[str]]:
        """Find shortest path between two entities."""
        if not NETWORKX_AVAILABLE:
            self.logger.warning("NetworkX not available, cannot compute shortest path")
            return None
        
        try:
            # Create subgraph with only specified relationship types if provided
            if relationship_types:
                edges_to_include = []
                for source, target, data in self.graph.edges(data=True):
                    if data.get('type') in relationship_types:
                        edges_to_include.append((source, target))
                subgraph = self.graph.edge_subgraph(edges_to_include)
            else:
                subgraph = self.graph
            
            path = nx.shortest_path(subgraph, source_id, target_id)
            return path
            
        except nx.NetworkXNoPath:
            return None
        except Exception as e:
            self.logger.error(f"Error finding shortest path: {e}")
            return None
    
    async def get_entity_recommendations(
        self,
        entity_id: str,
        recommendation_type: str = "similar",
        max_recommendations: int = 5
    ) -> List[Dict[str, Any]]:
        """Get entity recommendations based on graph structure."""
        recommendations = []
        
        if recommendation_type == "similar":
            # Find entities with similar connections
            if entity_id in self.entity_embeddings:
                similar_entities = await self.semantic_entity_search(
                    self.entity_embeddings[entity_id],
                    top_k=max_recommendations + 1,  # +1 to exclude self
                    similarity_threshold=0.5
                )
                
                for similar_id, similarity in similar_entities:
                    if similar_id != entity_id:
                        recommendations.append({
                            "entity_id": similar_id,
                            "type": "semantic_similarity",
                            "score": similarity,
                            "reason": f"Semantically similar (score: {similarity:.2f})"
                        })
        
        elif recommendation_type == "connected":
            # Find highly connected entities
            related = await self.find_related_entities(entity_id, max_depth=2)
            
            # Count connections
            connection_counts = {}
            for rel in related:
                rel_id = rel["entity_id"]
                connection_counts[rel_id] = connection_counts.get(rel_id, 0) + 1
            
            # Sort by connection count
            sorted_connections = sorted(
                connection_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            for entity_id, count in sorted_connections[:max_recommendations]:
                recommendations.append({
                    "entity_id": entity_id,
                    "type": "highly_connected",
                    "score": count / max(connection_counts.values()),
                    "reason": f"Highly connected ({count} connections)"
                })
        
        return recommendations[:max_recommendations]
    
    async def export_graph(self, format_type: str = "json") -> str:
        """Export graph in specified format."""
        if format_type == "json":
            if NETWORKX_AVAILABLE:
                data = nx.node_link_data(self.graph)
            else:
                data = self.graph
            
            return json.dumps(data, indent=2, default=str)
        
        else:
            raise ValueError(f"Unsupported export format: {format_type}")
    
    async def import_graph(self, data: str, format_type: str = "json"):
        """Import graph from specified format."""
        if format_type == "json":
            graph_data = json.loads(data)
            
            if NETWORKX_AVAILABLE:
                self.graph = nx.node_link_graph(graph_data)
            else:
                self.graph = graph_data
                
            self.logger.info("Graph imported successfully")
        else:
            raise ValueError(f"Unsupported import format: {format_type}") 