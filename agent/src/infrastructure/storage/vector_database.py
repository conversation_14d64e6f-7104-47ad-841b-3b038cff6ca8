import asyncio
import time
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np
import json
import logging

# Vector database client imports with fallbacks
try:
    from pymilvus import MilvusClient, DataType, Collection
    MILVUS_AVAILABLE = True
except ImportError:
    MILVUS_AVAILABLE = False

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False

try:
    import chromadb
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False

class VectorDBType(Enum):
    MILVUS = "milvus"
    QDRANT = "qdrant" 
    CHROMA = "chroma"

@dataclass
class SearchResult:
    """Structured search result with metadata."""
    content: str
    score: float
    metadata: Dict[str, Any]
    embedding_id: str
    source: str
    document_type: str = "text"

class EnterpriseVectorDatabase:
    """
    Production-grade vector database with multi-provider support.
    
    Features:
    - Multi-provider support (Mil<PERSON>s, Qdrant, Chroma)
    - Automatic failover and load balancing
    - Batch operations for performance
    - Advanced filtering and hybrid search
    - Monitoring and metrics collection
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Debug: Print config for troubleshooting
        print(f"🔍 VectorDB config received: {config}")
        
        # Safely initialize db_type with better error handling
        db_type_str = config.get('type', 'chroma')
        print(f"🔍 DB type from config: '{db_type_str}' (type: {type(db_type_str)})")
        
        try:
            self.db_type = VectorDBType(db_type_str)
            print(f"🔍 DB type enum created: {self.db_type} (value: {self.db_type.value})")
        except ValueError as e:
            # Handle invalid db type gracefully
            print(f"⚠️ Invalid database type '{db_type_str}', falling back to chroma: {e}")
            self.db_type = VectorDBType.CHROMA
        
        self.client = None
        self.collection_name = config.get('collection_name', 'test_dspy_knowledge')
        self.dimension = config.get('dimension', 1536)
        self.metric_type = config.get('metric_type', 'COSINE')
        
        # Performance monitoring
        self.query_metrics = {
            'total_queries': 0,
            'average_latency': 0.0,
            'success_rate': 1.0,
            'cache_hit_rate': 0.0
        }
        
        self.logger = logging.getLogger(__name__)
        
        # Validate provider availability
        self._validate_provider()
        
    def _validate_provider(self):
        """Validate that the selected provider is available."""
        if self.db_type == VectorDBType.MILVUS and not MILVUS_AVAILABLE:
            self.logger.warning("Milvus not available, falling back to Chroma")
            self.db_type = VectorDBType.CHROMA
        elif self.db_type == VectorDBType.QDRANT and not QDRANT_AVAILABLE:
            self.logger.warning("Qdrant not available, falling back to Chroma")
            self.db_type = VectorDBType.CHROMA
        elif self.db_type == VectorDBType.CHROMA and not CHROMA_AVAILABLE:
            raise ImportError("No vector database providers available. Please install chromadb, qdrant-client, or pymilvus.")
        
    async def initialize(self):
        """Initialize vector database connection based on type."""
        try:
            print(f"🔍 Initializing vector database with type: {self.db_type} (value: {self.db_type.value})")
            print(f"🔍 Available providers: MILVUS={MILVUS_AVAILABLE}, QDRANT={QDRANT_AVAILABLE}, CHROMA={CHROMA_AVAILABLE}")
            
            if self.db_type == VectorDBType.MILVUS and MILVUS_AVAILABLE:
                print("🔍 Initializing Milvus...")
                await self._initialize_milvus()
            elif self.db_type == VectorDBType.QDRANT and QDRANT_AVAILABLE:
                print("🔍 Initializing Qdrant...")
                await self._initialize_qdrant()
            elif self.db_type == VectorDBType.CHROMA and CHROMA_AVAILABLE:
                print("🔍 Initializing Chroma...")
                await self._initialize_chroma()
            else:
                print(f"🔍 No matching initialization found for type: {self.db_type}")
                print(f"🔍 Type checks: MILVUS={self.db_type == VectorDBType.MILVUS}, QDRANT={self.db_type == VectorDBType.QDRANT}, CHROMA={self.db_type == VectorDBType.CHROMA}")
            
            await self._create_indexes()
            self.logger.info(f"Vector database ({self.db_type.value}) initialized successfully")
            print(f"✅ Vector database ({self.db_type.value}) initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize vector database: {e}")
            print(f"❌ Failed to initialize vector database: {e}")
            raise
    
    async def _initialize_milvus(self):
        """Initialize Milvus connection with enterprise features."""
        self.client = MilvusClient(
            uri=self.config.get('uri', 'http://localhost:19530'),
            token=self.config.get('token', ''),
            db_name=self.config.get('database', 'default')
        )
        
        # Create collection if it doesn't exist
        if not self.client.has_collection(self.collection_name):
            schema = self._create_milvus_schema()
            self.client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                index_params=self._get_milvus_index_params()
            )
    
    async def _initialize_qdrant(self):
        """Initialize Qdrant connection with high-performance settings."""
        # Build URL from host and port if not provided directly
        if 'url' in self.config:
            url = self.config['url']
        else:
            host = self.config.get('host', 'localhost')
            port = self.config.get('port', 6333)
            url = f"http://{host}:{port}"
        
        print(f"🔗 Connecting to Qdrant at: {url}")
        
        # Only include API key if it's actually provided
        client_config = {
            'url': url,
            'timeout': self.config.get('timeout', 30)
        }
        
        if 'api_key' in self.config and self.config['api_key']:
            client_config['api_key'] = self.config['api_key']
            print(f"🔑 Using API key for Qdrant authentication")
        else:
            print(f"🔓 No API key - using local Qdrant without authentication")
        
        self.client = QdrantClient(**client_config)
        
        # Create collection if it doesn't exist
        try:
            self.client.get_collection(self.collection_name)
        except:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.dimension,
                    distance=Distance.COSINE
                )
            )
    
    async def _initialize_chroma(self):
        """Initialize Chroma for development/prototyping."""
        import os

        try:
            # Check if we're running in Docker with ChromaDB service
            chroma_host = os.getenv('CHROMA_HOST')
            chroma_port = os.getenv('CHROMA_PORT', '8000')

            if chroma_host:
                # Use HTTP client for containerized ChromaDB
                self.logger.info(f"Connecting to ChromaDB server at {chroma_host}:{chroma_port}")
                print(f"🔗 Connecting to ChromaDB server at {chroma_host}:{chroma_port}")
                self.client = chromadb.HttpClient(
                    host=chroma_host,
                    port=int(chroma_port)
                )
            else:
                # Use persistent client for local file-based storage
                persist_dir = self.config.get('persist_directory', './data/chroma_db')
                self.logger.info(f"Using ChromaDB persistent storage at {persist_dir}")
                print(f"📁 Using ChromaDB persistent storage at {persist_dir}")
                self.client = chromadb.PersistentClient(path=persist_dir)

            print(f"🔗 ChromaDB client created, attempting to get collection: {self.collection_name}")
            
            try:
                self.collection = self.client.get_collection(self.collection_name)
                self.logger.info(f"Connected to existing ChromaDB collection: {self.collection_name}")
                print(f"✅ Connected to existing ChromaDB collection: {self.collection_name}")
            except Exception as get_error:
                print(f"🔍 Get collection failed: {get_error}, attempting to create...")
                try:
                    self.collection = self.client.create_collection(
                        name=self.collection_name,
                        metadata={"hnsw:space": "cosine"}
                    )
                    self.logger.info(f"Created new ChromaDB collection: {self.collection_name}")
                    print(f"✅ Created new ChromaDB collection: {self.collection_name}")
                except Exception as create_error:
                    print(f"🔍 Create collection failed: {create_error}")
                    # Handle case where collection exists but get_collection failed for other reasons
                    if "already exists" in str(create_error):
                        try:
                            # Try to get the collection again
                            print(f"🔄 Retrying get collection after 'already exists' error...")
                            self.collection = self.client.get_collection(self.collection_name)
                            self.logger.info(f"Connected to existing ChromaDB collection after creation error: {self.collection_name}")
                            print(f"✅ Connected to existing ChromaDB collection (retry): {self.collection_name}")
                        except Exception as final_error:
                            print(f"🚨 Final retry failed: {final_error}")
                            raise RuntimeError(f"Failed to connect to ChromaDB collection '{self.collection_name}': {final_error}")
                    else:
                        raise create_error
                        
        except Exception as e:
            print(f"🚨 ChromaDB initialization completely failed: {e}")
            import traceback
            print(f"🚨 Full traceback: {traceback.format_exc()}")
            raise
    
    async def _create_indexes(self):
        """Create appropriate indexes for the vector database."""
        if self.db_type == VectorDBType.MILVUS and hasattr(self.client, 'create_index'):
            # Milvus indexes are created during collection creation
            pass
        elif self.db_type == VectorDBType.QDRANT:
            # Qdrant indexes are automatic
            pass
        elif self.db_type == VectorDBType.CHROMA:
            # Chroma indexes are automatic
            pass
    
    def _create_milvus_schema(self):
        """Create Milvus collection schema."""
        if not MILVUS_AVAILABLE:
            return None
            
        from pymilvus import CollectionSchema, FieldSchema, DataType
        
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.dimension),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="source", dtype=DataType.VARCHAR, max_length=500),
            FieldSchema(name="document_type", dtype=DataType.VARCHAR, max_length=100)
        ]
        
        return CollectionSchema(fields, "Test-DSPy knowledge collection")
    
    def _get_milvus_index_params(self):
        """Get Milvus index parameters."""
        return {
            "metric_type": self.metric_type,
            "index_type": "IVF_FLAT",
            "params": {"nlist": 1024}
        }
    
    async def upsert_documents(
        self, 
        documents: List[Dict[str, Any]], 
        batch_size: int = 100
    ) -> bool:
        """
        Batch upsert documents with embeddings and metadata.
        
        Args:
            documents: List of document dictionaries with content, metadata, embeddings
            batch_size: Batch size for bulk operations
        """
        try:
            total_batches = (len(documents) - 1) // batch_size + 1
            
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                if self.db_type == VectorDBType.MILVUS:
                    await self._upsert_milvus_batch(batch)
                elif self.db_type == VectorDBType.QDRANT:
                    await self._upsert_qdrant_batch(batch)
                elif self.db_type == VectorDBType.CHROMA:
                    await self._upsert_chroma_batch(batch)
                
                print(f"📄 Processed batch {i//batch_size + 1}/{total_batches}")
            
            print(f"✅ Successfully upserted {len(documents)} documents")
            return True
            
        except Exception as e:
            self.logger.error(f"Error upserting documents: {e}")
            print(f"❌ Error upserting documents: {e}")
            return False
    
    async def _upsert_milvus_batch(self, batch: List[Dict[str, Any]]):
        """Upsert batch to Milvus."""
        if not MILVUS_AVAILABLE:
            raise ImportError("Milvus not available")
            
        data = []
        for doc in batch:
            data.append({
                "id": doc.get('id', f"doc_{hash(doc['content'])}"),
                "embedding": doc['embedding'],
                "content": doc['content'],
                "metadata": json.dumps(doc.get('metadata', {})),
                "source": doc.get('source', 'unknown'),
                "document_type": doc.get('document_type', 'text')
            })
        
        self.client.insert(collection_name=self.collection_name, data=data)
    
    async def _upsert_qdrant_batch(self, batch: List[Dict[str, Any]]):
        """Upsert batch to Qdrant."""
        if not QDRANT_AVAILABLE:
            raise ImportError("Qdrant not available")
            
        points = []
        for doc in batch:
            points.append(PointStruct(
                id=hash(doc['content']) % (2**32),  # Simple hash-based ID
                vector=doc['embedding'],
                payload={
                    "content": doc['content'],
                    "metadata": doc.get('metadata', {}),
                    "source": doc.get('source', 'unknown'),
                    "document_type": doc.get('document_type', 'text')
                }
            ))
        
        self.client.upsert(collection_name=self.collection_name, points=points)
    
    async def _upsert_chroma_batch(self, batch: List[Dict[str, Any]]):
        """Upsert batch to Chroma."""
        if not CHROMA_AVAILABLE:
            raise ImportError("Chroma not available")
            
        ids = []
        embeddings = []
        metadatas = []
        documents = []
        
        for doc in batch:
            ids.append(doc.get('id', f"doc_{hash(doc['content'])}"))
            embeddings.append(doc['embedding'])
            documents.append(doc['content'])
            
            metadata = doc.get('metadata', {})
            metadata.update({
                'source': doc.get('source', 'unknown'),
                'document_type': doc.get('document_type', 'text')
            })
            metadatas.append(metadata)
        
        self.collection.upsert(
            ids=ids,
            embeddings=embeddings,
            metadatas=metadatas,
            documents=documents
        )
    
    async def semantic_search(
        self,
        query_vector: Union[List[float], np.ndarray],
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        include_metadata: bool = True
    ) -> List[SearchResult]:
        """
        Advanced semantic search with filtering and ranking.
        
        Args:
            query_vector: Query embedding vector
            limit: Maximum number of results
            filters: Metadata filters for hybrid search
            include_metadata: Whether to include full metadata
        """
        start_time = time.time()
        
        try:
            if self.db_type == VectorDBType.MILVUS:
                results = await self._search_milvus(query_vector, limit, filters)
            elif self.db_type == VectorDBType.QDRANT:
                results = await self._search_qdrant(query_vector, limit, filters)
            elif self.db_type == VectorDBType.CHROMA:
                results = await self._search_chroma(query_vector, limit, filters)
            else:
                results = []
            
            # Update metrics
            latency = time.time() - start_time
            self._update_search_metrics(latency, True)
            
            return results
            
        except Exception as e:
            self._update_search_metrics(time.time() - start_time, False)
            self.logger.error(f"Search error: {e}")
            print(f"❌ Search error: {e}")
            return []
    
    async def _search_milvus(
        self, 
        query_vector: List[float], 
        limit: int, 
        filters: Optional[Dict[str, Any]]
    ) -> List[SearchResult]:
        """Search in Milvus."""
        if not MILVUS_AVAILABLE:
            return []
            
        search_params = {"metric_type": self.metric_type, "params": {"nprobe": 10}}
        
        results = self.client.search(
            collection_name=self.collection_name,
            data=[query_vector],
            anns_field="embedding",
            search_params=search_params,
            limit=limit,
            output_fields=["content", "metadata", "source", "document_type"]
        )
        
        search_results = []
        for hit in results[0]:
            metadata = json.loads(hit.entity.get('metadata', '{}'))
            search_results.append(SearchResult(
                content=hit.entity.get('content', ''),
                score=hit.score,
                metadata=metadata,
                embedding_id=str(hit.id),
                source=hit.entity.get('source', 'unknown'),
                document_type=hit.entity.get('document_type', 'text')
            ))
        
        return search_results
    
    async def _search_qdrant(
        self, 
        query_vector: List[float], 
        limit: int, 
        filters: Optional[Dict[str, Any]]
    ) -> List[SearchResult]:
        """Search in Qdrant."""
        if not QDRANT_AVAILABLE:
            return []
            
        results = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_vector,
            limit=limit,
            query_filter=filters
        )
        
        search_results = []
        for hit in results:
            search_results.append(SearchResult(
                content=hit.payload.get('content', ''),
                score=hit.score,
                metadata=hit.payload.get('metadata', {}),
                embedding_id=str(hit.id),
                source=hit.payload.get('source', 'unknown'),
                document_type=hit.payload.get('document_type', 'text')
            ))
        
        return search_results
    
    async def _search_chroma(
        self, 
        query_vector: List[float], 
        limit: int, 
        filters: Optional[Dict[str, Any]]
    ) -> List[SearchResult]:
        """Search in Chroma."""
        if not CHROMA_AVAILABLE:
            return []
            
        results = self.collection.query(
            query_embeddings=[query_vector],
            n_results=limit,
            where=filters,
            include=['metadatas', 'documents', 'distances']
        )
        
        search_results = []
        if results['ids'] and results['ids'][0]:
            for i, doc_id in enumerate(results['ids'][0]):
                score = 1.0 - results['distances'][0][i] if results['distances'] else 0.0
                metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                content = results['documents'][0][i] if results['documents'] else ''
                
                search_results.append(SearchResult(
                    content=content,
                    score=score,
                    metadata=metadata,
                    embedding_id=doc_id,
                    source=metadata.get('source', 'unknown'),
                    document_type=metadata.get('document_type', 'text')
                ))
        
        return search_results
    
    async def hybrid_search(
        self,
        text_query: str,
        query_vector: List[float],
        alpha: float = 0.7,
        limit: int = 10
    ) -> List[SearchResult]:
        """
        Hybrid search combining semantic similarity and text matching.
        
        Args:
            text_query: Text query for keyword matching
            query_vector: Embedding vector for semantic search
            alpha: Weight for semantic vs text search (0.0-1.0)
            limit: Maximum results
        """
        # Get semantic search results
        semantic_results = await self.semantic_search(query_vector, limit * 2)
        
        # For now, return semantic results (text search can be added per provider)
        # In production, this would combine semantic + BM25/keyword search
        return semantic_results[:limit]
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get comprehensive collection statistics."""
        try:
            if self.db_type == VectorDBType.MILVUS:
                return await self._get_milvus_stats()
            elif self.db_type == VectorDBType.QDRANT:
                return await self._get_qdrant_stats()
            elif self.db_type == VectorDBType.CHROMA:
                return await self._get_chroma_stats()
        except Exception as e:
            self.logger.error(f"Error getting collection stats: {e}")
            return {"error": str(e)}
    
    async def _get_milvus_stats(self) -> Dict[str, Any]:
        """Get Milvus collection statistics."""
        if not MILVUS_AVAILABLE:
            return {}
            
        try:
            stats = self.client.get_collection_stats(self.collection_name)
            return {
                "provider": "milvus",
                "collection_name": self.collection_name,
                "row_count": stats.get("row_count", 0),
                "index_info": "Available"
            }
        except Exception as e:
            return {"provider": "milvus", "error": str(e)}
    
    async def _get_qdrant_stats(self) -> Dict[str, Any]:
        """Get Qdrant collection statistics."""
        if not QDRANT_AVAILABLE:
            return {}
            
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "provider": "qdrant",
                "collection_name": self.collection_name,
                "points_count": info.points_count,
                "vector_size": info.config.params.vectors.size
            }
        except Exception as e:
            return {"provider": "qdrant", "error": str(e)}
    
    async def _get_chroma_stats(self) -> Dict[str, Any]:
        """Get Chroma collection statistics."""
        if not CHROMA_AVAILABLE:
            return {}
            
        try:
            count = self.collection.count()
            return {
                "provider": "chroma",
                "collection_name": self.collection_name,
                "document_count": count
            }
        except Exception as e:
            return {"provider": "chroma", "error": str(e)}
    
    def _update_search_metrics(self, latency: float, success: bool):
        """Update performance metrics."""
        self.query_metrics['total_queries'] += 1
        
        # Update average latency
        current_avg = self.query_metrics['average_latency']
        total = self.query_metrics['total_queries']
        self.query_metrics['average_latency'] = (
            (current_avg * (total - 1) + latency) / total
        )
        
        # Update success rate
        if success:
            success_count = int(self.query_metrics['success_rate'] * (total - 1)) + 1
        else:
            success_count = int(self.query_metrics['success_rate'] * (total - 1))
        
        self.query_metrics['success_rate'] = success_count / total
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return {
            "provider": self.db_type.value,
            "collection": self.collection_name,
            **self.query_metrics
        } 