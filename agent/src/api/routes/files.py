"""
File upload and management routes for the DSPy Multi-Agent System API.

Handles session-based file uploads and retrieval with integrated document processing.
"""

import logging
from datetime import datetime
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, BackgroundTasks
from typing import List, Optional

from ..models.responses import FileUploadResponse, FileListResponse, FileInfo
from ..models.errors import ValidationError, FileSizeError, FileTypeError
from ..middleware.auth import get_api_key
from ..services.session_service import get_session_service
from ..services.document_processing_service import get_document_processing_service

logger = logging.getLogger(__name__)
router = APIRouter()

# File upload constraints
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_CONTENT_TYPES = {
    'text/plain',
    'text/markdown', 
    'text/csv',
    'application/json',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
}


async def process_uploaded_file_background(
    file_path: str, 
    session_id: str, 
    file_id: str,
    additional_metadata: Optional[dict] = None
):
    """
    Background task to process uploaded files through the document processing pipeline.
    
    This function:
    - Processes the file using MultimodalProcessor
    - Generates embeddings using DSPy best practices
    - Stores chunks in the vector database with session metadata
    - Sends real-time progress updates via SSE
    """
    try:
        logger.info(f"Starting background processing for file {file_id} in session {session_id}")
        
        # Get document processing service
        doc_service = await get_document_processing_service()
        
        # Prepare metadata
        metadata = {
            "session_id": session_id,
            "file_id": file_id,
            "upload_source": "api",
            "processing_timestamp": datetime.utcnow().isoformat()
        }
        
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Process the file through the complete pipeline
        result = await doc_service.process_file(
            file_path=file_path,
            session_id=session_id,
            metadata=metadata
        )
        
        logger.info(f"Background processing completed for file {file_id}: {result}")
        
    except Exception as e:
        logger.error(f"Background file processing failed for {file_id}: {e}")
        
        # Send error update via SSE if possible
        try:
            from ..services.sse_service import sse_manager
            await sse_manager.send_tool_status_update(session_id, {
                "type": "document_processing",
                "file_id": file_id,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            })
        except Exception as sse_error:
            logger.error(f"Failed to send SSE error update: {sse_error}")


@router.post("/files/upload", response_model=FileUploadResponse)
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    session_id: str = Form(...),
    file_name: Optional[str] = Form(None),
    enable_processing: bool = Form(True),  # New parameter to enable/disable vector processing
    api_key: str = Depends(get_api_key),
    session_service = Depends(get_session_service)
):
    """
    Upload file for session-specific access with integrated document processing.
    
    This endpoint:
    - Accepts files up to 10MB
    - Stores files with session isolation
    - **NEW**: Automatically processes files for vector search (if enabled)
    - **NEW**: Sends real-time processing updates via SSE
    - Returns file ID for future reference
    
    **Enhanced Integration:** 
    - Uses existing vector DB with session metadata filtering
    - Integrates with DSPy document processing pipeline
    - Provides real-time processing feedback via SSE endpoint
    """
    try:
        logger.info(f"File upload request: session={session_id}, filename={file.filename}, processing={enable_processing}")
        
        # Validate session ID
        if not session_id or len(session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty", field="session_id")
        
        # Validate file
        if not file.filename:
            raise ValidationError("Filename is required", field="file")
        
        # Check file size
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise FileSizeError(MAX_FILE_SIZE, len(file_content))
        
        # Reset file position for processing
        await file.seek(0)
        
        # Validate file type (optional - can be made stricter)
        if file.content_type and file.content_type not in ALLOWED_CONTENT_TYPES:
            logger.warning(f"Potentially unsupported file type: {file.content_type}")
            # Don't reject, just warn - we can handle most text-based files
        
        # Store file using session service
        file_id = await session_service.store_session_file(
            session_id=session_id,
            file=file,
            custom_name=file_name
        )
        
        # Get the file path for processing
        session_dir = session_service.upload_dir / session_id / "files"
        file_path = session_dir / f"{file_id}_{file_name or file.filename}"
        
        # Add background document processing task if enabled
        if enable_processing:
            background_tasks.add_task(
                process_uploaded_file_background,
                str(file_path),
                session_id,
                file_id,
                {
                    "original_filename": file.filename,
                    "custom_name": file_name,
                    "content_type": file.content_type,
                    "file_size": len(file_content)
                }
            )
            
            logger.info(f"File uploaded and processing started: {file_id} for session {session_id}")
            processing_message = "File uploaded successfully. Processing for vector search started in background."
        else:
            logger.info(f"File uploaded without processing: {file_id} for session {session_id}")
            processing_message = "File uploaded successfully. Vector processing disabled."
        
        return FileUploadResponse(
            success=True,
            file_id=file_id,
            file_name=file_name or file.filename,
            timestamp=datetime.utcnow(),
            processing_enabled=enable_processing,
            message=processing_message,
            sse_endpoint=f"/api/v1/sse/sessions/{session_id}" if enable_processing else None
        )
        
    except (ValidationError, FileSizeError, FileTypeError):
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"File upload failed for session {session_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"File upload failed: {str(e)}"
        )


@router.get("/files/{session_id}", response_model=FileListResponse)
async def get_session_files(
    session_id: str,
    api_key: str = Depends(get_api_key),
    session_service = Depends(get_session_service)
):
    """
    Get files uploaded for specific session.
    
    Returns list of files with metadata including:
    - File ID and name
    - Upload timestamp
    - Processing status
    - File size
    """
    try:
        logger.info(f"File list request for session: {session_id}")
        
        # Validate session ID
        if not session_id or len(session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty")
        
        # Get files from session service
        files_data = await session_service.get_session_files(session_id)
        
        # Convert to response format
        files = []
        for file_data in files_data:
            file_info = FileInfo(
                file_id=file_data.get("file_id", ""),
                file_name=file_data.get("file_name", "unknown"),
                upload_time=file_data.get("upload_time", datetime.utcnow().isoformat()),
                processed=file_data.get("processed", True),
                file_size=file_data.get("file_size")
            )
            files.append(file_info)
        
        logger.debug(f"Retrieved {len(files)} files for session {session_id}")
        
        return FileListResponse(files=files)
        
    except ValidationError:
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"Failed to get files for session {session_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve session files: {str(e)}"
        )


@router.delete("/files/{session_id}/{file_id}")
async def delete_file(
    session_id: str,
    file_id: str,
    api_key: str = Depends(get_api_key),
    session_service = Depends(get_session_service)
):
    """
    Delete a specific file from a session.
    
    **Note:** File deletion is not fully implemented yet.
    """
    try:
        logger.info(f"File deletion request: session={session_id}, file={file_id}")
        
        # Validate parameters
        if not session_id or len(session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty")
        if not file_id or len(file_id.strip()) == 0:
            raise ValidationError("File ID cannot be empty")
        
        # Attempt to delete file
        deleted = await session_service.delete_session_file(session_id, file_id)
        
        if not deleted:
            raise HTTPException(
                status_code=404,
                detail=f"File {file_id} not found in session {session_id}"
            )
        
        return {
            "success": True,
            "message": f"File {file_id} deleted from session {session_id}",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except ValidationError:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete file {file_id} from session {session_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete file: {str(e)}"
        )


@router.delete("/files/{session_id}")
async def cleanup_session_files(
    session_id: str,
    api_key: str = Depends(get_api_key),
    session_service = Depends(get_session_service)
):
    """
    Clean up all files for a session.
    
    This removes all uploaded files and their vector embeddings.
    """
    try:
        logger.info(f"Session cleanup request: {session_id}")
        
        # Validate session ID
        if not session_id or len(session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty")
        
        # Clean up session files
        cleaned = await session_service.cleanup_session_files(session_id)
        
        if not cleaned:
            logger.warning(f"Session cleanup may have failed for: {session_id}")
        
        return {
            "success": True,
            "message": f"Session {session_id} files cleaned up",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup session {session_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cleanup session files: {str(e)}"
        )


@router.get("/files/limits")
async def get_file_limits():
    """Get file upload limits and constraints."""
    return {
        "max_file_size": MAX_FILE_SIZE,
        "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024),
        "allowed_content_types": list(ALLOWED_CONTENT_TYPES),
        "supported_formats": [
            "Plain text (.txt)",
            "Markdown (.md)",
            "CSV (.csv)", 
            "JSON (.json)",
            "PDF (.pdf)",
            "Word documents (.doc, .docx)"
        ],
        "processing": {
            "text_extraction": "automatic",
            "vector_embedding": "enabled",
            "session_isolation": "enforced"
        },
        "retention": {
            "policy": "session-based",
            "cleanup": "manual or automatic on session end"
        }
    }


@router.get("/files/health")
async def files_health():
    """Health check for file upload endpoints."""
    try:
        # Test session service availability
        session_service = get_session_service()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "endpoints": {
                "upload": "available",
                "list": "available", 
                "delete": "available",
                "cleanup": "available"
            },
            "session_service": "connected",
            "vector_database": "connected"
        }
        
    except Exception as e:
        logger.error(f"Files health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Files service unhealthy: {str(e)}"
        )
