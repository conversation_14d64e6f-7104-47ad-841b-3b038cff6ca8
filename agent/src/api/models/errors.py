"""
Error models and exceptions for the DSPy Multi-Agent System API.

Defines custom exceptions and error response structures.
"""

from typing import Optional, Dict, Any
from datetime import datetime


class APIError(Exception):
    """Base API exception."""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = "api_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        self.timestamp = datetime.utcnow()
        super().__init__(message)

    def to_dict(self, request_id: str) -> Dict[str, Any]:
        """Convert to dictionary for JSON response."""
        return {
            "error": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat() + "Z",
            "request_id": request_id
        }


class ValidationError(APIError):
    """Validation error exception."""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        code: Optional[str] = None,
        **kwargs
    ):
        details = {
            "field": field,
            "code": code,
            **kwargs
        }
        # Remove None values
        details = {k: v for k, v in details.items() if v is not None}
        
        super().__init__(
            message=message,
            error_code="validation_error",
            status_code=422,
            details=details
        )


class NotFoundError(APIError):
    """Resource not found exception."""
    
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            message=f"{resource_type} not found",
            error_code="not_found",
            status_code=404,
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )


class UnauthorizedError(APIError):
    """Unauthorized access exception."""
    
    def __init__(self, message: str = "Invalid or missing API key"):
        super().__init__(
            message=message,
            error_code="unauthorized",
            status_code=401
        )


class ForbiddenError(APIError):
    """Forbidden access exception."""
    
    def __init__(self, message: str = "Access forbidden"):
        super().__init__(
            message=message,
            error_code="forbidden",
            status_code=403
        )


class RateLimitError(APIError):
    """Rate limit exceeded exception."""
    
    def __init__(self, retry_after: Optional[int] = None):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after
            
        super().__init__(
            message="Rate limit exceeded",
            error_code="rate_limit_exceeded",
            status_code=429,
            details=details
        )


class InternalServerError(APIError):
    """Internal server error exception."""
    
    def __init__(self, message: str = "An unexpected error occurred"):
        super().__init__(
            message=message,
            error_code="internal_server_error",
            status_code=500
        )


class ServiceUnavailableError(APIError):
    """Service unavailable exception."""
    
    def __init__(self, message: str = "Service temporarily unavailable"):
        super().__init__(
            message=message,
            error_code="service_unavailable",
            status_code=503
        )


class FileSizeError(ValidationError):
    """File size validation error."""
    
    def __init__(self, max_size: int, actual_size: int):
        super().__init__(
            message="File size exceeds limit",
            field="file",
            code="FILE_SIZE_EXCEEDED",
            max_size=max_size,
            actual_size=actual_size
        )


class FileTypeError(ValidationError):
    """File type validation error."""
    
    def __init__(self, allowed_types: list, actual_type: str):
        super().__init__(
            message="File type not allowed",
            field="file",
            code="FILE_TYPE_NOT_ALLOWED",
            allowed_types=allowed_types,
            actual_type=actual_type
        )


class WorkflowNotFoundError(NotFoundError):
    """Workflow not found exception."""
    
    def __init__(self, workflow_id: str):
        super().__init__("Workflow", workflow_id)


class SessionNotFoundError(NotFoundError):
    """Session not found exception."""
    
    def __init__(self, session_id: str):
        super().__init__("Session", session_id)
