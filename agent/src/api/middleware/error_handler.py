"""
Error handling middleware for the DSPy Multi-Agent System API.

Provides standardized error responses and exception handling.
"""

import uuid
import logging
from typing import Dict, Any
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from datetime import datetime

from ..models.errors import APIError

logger = logging.getLogger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for standardized error handling."""
    
    def __init__(self, app):
        super().__init__(app)
        
    async def dispatch(self, request: Request, call_next):
        """Handle request and standardize error responses."""
        # Ensure request has an ID
        request_id = getattr(request.state, 'request_id', str(uuid.uuid4()))
        request.state.request_id = request_id
        
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as e:
            # Handle FastAPI HTTP exceptions
            return self._create_error_response(
                error_code=self._get_error_code(e.status_code),
                message=e.detail,
                status_code=e.status_code,
                request_id=request_id
            )
            
        except APIError as e:
            # Handle custom API errors
            logger.warning(
                f"API Error [{request_id}]: {e.error_code} - {e.message}"
            )
            return JSONResponse(
                status_code=e.status_code,
                content=e.to_dict(request_id)
            )
            
        except ValueError as e:
            # Handle validation errors
            logger.warning(f"Validation Error [{request_id}]: {str(e)}")
            return self._create_error_response(
                error_code="validation_error",
                message=str(e),
                status_code=422,
                request_id=request_id
            )
            
        except Exception as e:
            # Handle unexpected errors
            logger.exception(f"Unhandled error in request [{request_id}]: {str(e)}")
            return self._create_error_response(
                error_code="internal_server_error",
                message="An unexpected error occurred",
                status_code=500,
                request_id=request_id,
                details={"exception_type": type(e).__name__}
            )
    
    def _create_error_response(
        self,
        error_code: str,
        message: str,
        status_code: int,
        request_id: str,
        details: Dict[str, Any] = None
    ) -> JSONResponse:
        """Create standardized error response."""
        error_data = {
            "error": error_code,
            "message": message,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "request_id": request_id
        }
        
        if details:
            error_data["details"] = details
            
        return JSONResponse(
            status_code=status_code,
            content=error_data
        )
    
    def _get_error_code(self, status_code: int) -> str:
        """Map HTTP status codes to error codes."""
        error_code_map = {
            400: "bad_request",
            401: "unauthorized", 
            403: "forbidden",
            404: "not_found",
            405: "method_not_allowed",
            409: "conflict",
            422: "validation_error",
            429: "rate_limit_exceeded",
            500: "internal_server_error",
            502: "bad_gateway",
            503: "service_unavailable",
            504: "gateway_timeout"
        }
        
        return error_code_map.get(status_code, "unknown_error")


class ValidationErrorHandler:
    """Helper class for handling validation errors."""
    
    @staticmethod
    def format_pydantic_error(error: Exception) -> Dict[str, Any]:
        """Format Pydantic validation errors."""
        if hasattr(error, 'errors'):
            # Pydantic validation error
            errors = []
            for err in error.errors():
                errors.append({
                    "field": ".".join(str(x) for x in err.get("loc", [])),
                    "message": err.get("msg", ""),
                    "type": err.get("type", ""),
                    "input": err.get("input")
                })
            return {
                "validation_errors": errors,
                "error_count": len(errors)
            }
        else:
            return {"message": str(error)}


def handle_async_errors(func):
    """Decorator for handling async function errors."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except APIError:
            # Re-raise API errors as-is
            raise
        except Exception as e:
            # Convert other exceptions to API errors
            logger.exception(f"Error in {func.__name__}: {str(e)}")
            raise APIError(
                message="An error occurred while processing the request",
                error_code="processing_error",
                status_code=500,
                details={"function": func.__name__, "exception": str(e)}
            )
    return wrapper
