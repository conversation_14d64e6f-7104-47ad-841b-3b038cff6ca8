"""
Authentication middleware for the DSPy Multi-Agent System API.

Handles API key validation and authentication.
"""

import os
from typing import Optional
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import uuid
from datetime import datetime

from ..models.errors import UnauthorizedError


class APIKeyMiddleware(BaseHTTPMiddleware):
    """Middleware for API key authentication."""
    
    def __init__(self, app, api_key: Optional[str] = None):
        super().__init__(app)
        # Get API key from environment or parameter
        self.api_key = api_key or os.getenv("API_KEY", "dev-api-key-12345")
        
        # Paths that don't require authentication
        self.public_paths = {
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/api/v1/health"
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request and validate API key."""
        # Skip authentication for public paths
        if request.url.path in self.public_paths:
            return await call_next(request)
        
        # Skip authentication for OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return await call_next(request)
        
        # Get API key from header
        api_key = request.headers.get("Authorization")
        if api_key and api_key.startswith("Bearer "):
            api_key = api_key[7:]  # Remove "Bearer " prefix
        
        # Validate API key
        if not api_key or api_key != self.api_key:
            error = UnauthorizedError("Invalid or missing API key")
            request_id = str(uuid.uuid4())
            
            return JSONResponse(
                status_code=error.status_code,
                content=error.to_dict(request_id)
            )
        
        # Store API key in request state for later use
        request.state.api_key = api_key
        request.state.authenticated = True
        
        return await call_next(request)


# Security scheme for OpenAPI documentation
security = HTTPBearer()


async def get_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """
    Dependency to get and validate API key.
    
    This is used as a FastAPI dependency in route handlers.
    """
    api_key = credentials.credentials
    expected_key = os.getenv("API_KEY", "dev-api-key-12345")
    
    if api_key != expected_key:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return api_key


async def get_optional_api_key(request: Request) -> Optional[str]:
    """
    Get API key from request if available.
    
    Used for endpoints that may or may not require authentication.
    """
    return getattr(request.state, 'api_key', None)


def is_authenticated(request: Request) -> bool:
    """Check if request is authenticated."""
    return getattr(request.state, 'authenticated', False)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware."""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In production, use Redis
        
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting."""
        # Get client identifier (API key or IP)
        client_id = getattr(request.state, 'api_key', None) or request.client.host
        
        # Simple in-memory rate limiting (use Redis in production)
        current_time = datetime.utcnow()
        minute_key = current_time.strftime("%Y-%m-%d-%H-%M")
        key = f"{client_id}:{minute_key}"
        
        # Count requests
        if key not in self.request_counts:
            self.request_counts[key] = 0
        
        self.request_counts[key] += 1
        
        # Check limit
        if self.request_counts[key] > self.requests_per_minute:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "rate_limit_exceeded",
                    "message": "Too many requests",
                    "details": {
                        "limit": self.requests_per_minute,
                        "window": "1 minute"
                    },
                    "timestamp": current_time.isoformat() + "Z",
                    "request_id": str(uuid.uuid4())
                }
            )
        
        # Clean old entries (basic cleanup)
        if len(self.request_counts) > 1000:
            # Keep only current minute entries
            current_keys = [k for k in self.request_counts.keys() if minute_key in k]
            self.request_counts = {k: v for k, v in self.request_counts.items() if k in current_keys}
        
        return await call_next(request)
