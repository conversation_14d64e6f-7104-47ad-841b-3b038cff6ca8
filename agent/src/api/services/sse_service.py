"""
Server-Sent Events (SSE) service for real-time tool updates.

Provides real-time tool status updates via SSE connections for better
performance and simpler implementation than WebSockets.
"""

import asyncio
import json
import logging
from typing import Dict, Set, Any, Optional
from datetime import datetime, timezone
from fastapi import Request
from fastapi.responses import StreamingResponse

logger = logging.getLogger(__name__)


class SSEManager:
    """
    Manages Server-Sent Events connections for real-time tool updates.
    
    Provides simple one-way communication from server to client for
    tool execution status, perfect for monitoring LLM tool calls.
    """
    
    def __init__(self):
        # Active connections: session_id -> set of connection queues
        self.active_connections: Dict[str, Set[asyncio.Queue]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[asyncio.Queue, Dict[str, Any]] = {}
        
        logger.info("SSEManager initialized")

    async def add_connection(self, session_id: str, request: Request) -> asyncio.Queue:
        """Add a new SSE connection for a session."""
        try:
            # Create a queue for this connection
            connection_queue = asyncio.Queue(maxsize=100)
            
            # Add to active connections
            if session_id not in self.active_connections:
                self.active_connections[session_id] = set()
            self.active_connections[session_id].add(connection_queue)
            
            # Store connection metadata
            self.connection_metadata[connection_queue] = {
                "session_id": session_id,
                "connected_at": datetime.now(timezone.utc),
                "client_ip": request.client.host if request.client else "unknown"
            }
            
            logger.info(f"SSE connection added for session {session_id}")
            
            # Send initial connection message
            await self._send_to_connection(connection_queue, {
                "type": "connection_established",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": {
                    "message": f"Connected to session {session_id}",
                    "ready_for_tool_updates": True
                }
            })
            
            return connection_queue
            
        except Exception as e:
            logger.error(f"Failed to add SSE connection for session {session_id}: {e}")
            raise

    async def remove_connection(self, connection_queue: asyncio.Queue):
        """Remove an SSE connection."""
        try:
            metadata = self.connection_metadata.get(connection_queue, {})
            session_id = metadata.get("session_id")
            
            if session_id and session_id in self.active_connections:
                self.active_connections[session_id].discard(connection_queue)
                
                # If no more connections for this session, clean up
                if not self.active_connections[session_id]:
                    del self.active_connections[session_id]
            
            # Remove connection metadata
            if connection_queue in self.connection_metadata:
                del self.connection_metadata[connection_queue]
            
            logger.info(f"SSE connection removed for session {session_id}")
            
        except Exception as e:
            logger.error(f"Error during SSE connection removal: {e}")

    async def send_tool_status_update(self, session_id: str, tool_data: Dict[str, Any]):
        """Send tool status update to all connections for a session."""
        if session_id not in self.active_connections:
            logger.debug(f"No active SSE connections for session {session_id}")
            return

        try:
            message = {
                "type": "tool_status",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": tool_data
            }
            
            logger.debug(f"Sending SSE tool status update for {session_id}: {tool_data}")

            await self._send_to_all(session_id, message)
            logger.debug(f"Successfully sent SSE tool status update for {session_id}")

        except Exception as e:
            logger.error(f"Failed to send SSE tool status update for {session_id}: {e}")

    async def send_question_complete(self, session_id: str, final_answer: str):
        """Send notification that question processing is complete."""
        if session_id not in self.active_connections:
            return

        try:
            message = {
                "type": "question_complete",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": {
                    "final_answer": final_answer,
                    "status": "completed"
                }
            }
            
            await self._send_to_all(session_id, message)
            logger.info(f"Question completion notification sent for session {session_id}")

        except Exception as e:
            logger.error(f"Failed to send question completion for {session_id}: {e}")

    async def send_error_notification(self, session_id: str, error_message: str):
        """Send error notification to all connections for a session."""
        if session_id not in self.active_connections:
            return

        try:
            message = {
                "type": "error",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": {
                    "error_message": error_message,
                    "status": "error"
                }
            }
            
            await self._send_to_all(session_id, message)
            logger.error(f"Error notification sent for session {session_id}: {error_message}")

        except Exception as e:
            logger.error(f"Failed to send error notification for {session_id}: {e}")

    async def send_workflow_status_update(self, session_id: str, status_data: Dict[str, Any]):
        """Send workflow status update to all connections for a session."""
        if session_id not in self.active_connections:
            logger.debug(f"No active SSE connections for session {session_id}")
            return

        try:
            message = {
                "type": "workflow_status",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": status_data
            }

            logger.debug(f"Sending SSE workflow status update for {session_id}: {status_data}")

            await self._send_to_all(session_id, message)
            logger.debug(f"Successfully sent SSE workflow status update for {session_id}")

        except Exception as e:
            logger.error(f"Failed to send SSE workflow status update for {session_id}: {e}")

    async def _send_to_all(self, session_id: str, message: Dict[str, Any]):
        """Send message to all connections for a session."""
        if session_id not in self.active_connections:
            return
        
        disconnected = set()
        
        for connection_queue in self.active_connections[session_id].copy():
            try:
                await self._send_to_connection(connection_queue, message)
            except Exception as e:
                logger.error(f"Failed to send SSE message to connection: {e}")
                disconnected.add(connection_queue)
        
        # Remove disconnected connections
        for connection_queue in disconnected:
            await self.remove_connection(connection_queue)

    async def _send_to_connection(self, connection_queue: asyncio.Queue, message: Dict[str, Any]):
        """Send message to a specific connection queue."""
        try:
            if not connection_queue.full():
                await connection_queue.put(message)
            else:
                # Queue is full, drop oldest messages
                logger.warning("SSE connection queue full, dropping messages")
                try:
                    while not connection_queue.empty():
                        connection_queue.get_nowait()
                except asyncio.QueueEmpty:
                    pass
                await connection_queue.put(message)
        except Exception as e:
            logger.error(f"Failed to send message to SSE connection: {e}")
            raise

    async def stream_events(self, connection_queue: asyncio.Queue) -> str:
        """Generate SSE event stream for a connection."""
        try:
            while True:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(connection_queue.get(), timeout=30.0)
                    
                    # Format as SSE event
                    sse_data = json.dumps(message)
                    yield f"data: {sse_data}\n\n"
                    
                except asyncio.TimeoutError:
                    # Send keepalive ping
                    yield f"data: {json.dumps({'type': 'ping', 'timestamp': datetime.now(timezone.utc).isoformat()})}\n\n"
                    
                except Exception as e:
                    logger.error(f"Error in SSE stream: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"SSE stream error: {e}")
        finally:
            # Clean up connection
            await self.remove_connection(connection_queue)

    def get_connection_count(self, session_id: str) -> int:
        """Get number of active connections for a session."""
        return len(self.active_connections.get(session_id, set()))

    def get_total_connections(self) -> int:
        """Get total number of active connections."""
        return sum(len(connections) for connections in self.active_connections.values())


# Global SSE manager instance
sse_manager = SSEManager() 