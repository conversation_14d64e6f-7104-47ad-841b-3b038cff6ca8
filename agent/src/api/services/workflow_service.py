"""
Optimized workflow service for the DSPy Multi-Agent System API.

High-performance workflow execution with pre-initialized components and async operations.
"""

import asyncio
import uuid
import json
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

# Import existing system components (corrected paths)
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.infrastructure.config.settings import get_config
except ImportError as e:
    logging.error(f"Failed to import config: {e}")
    def get_config():
        return type('Config', (), {})()

# Import optimized storage and component manager
from ..utils.async_session_storage import get_async_session_storage
from .component_manager import get_component_manager
from ..models.errors import WorkflowNotFoundError, InternalServerError

logger = logging.getLogger(__name__)


class OptimizedWorkflowService:
    """
    High-performance workflow service with pre-initialized components.

    Features:
    - Async database operations for non-blocking I/O
    - Pre-initialized system components
    - Real-time progress tracking
    - Optimized session context loading
    - Connection pooling for better performance
    """

    def __init__(self):
        self.storage = None  # Will be initialized async
        self.component_manager = None  # Will be initialized async
        self.websocket_manager = None  # Will be set by main app
        try:
            self.config = get_config()
        except:
            # Fallback if config not available
            self.config = type('Config', (), {})()

        # Track active workflows with enhanced metadata
        self.active_workflows = {}

        # Performance metrics
        self.performance_metrics = {
            "total_workflows": 0,
            "avg_response_time": 0.0,
            "avg_execution_time": 0.0
        }

        logger.info("OptimizedWorkflowService initialized")

    def set_websocket_manager(self, websocket_manager):
        """Set the WebSocket manager for real-time updates."""
        self.websocket_manager = websocket_manager

    async def _send_websocket_update(self, workflow_id: str, update_type: str, data: Dict[str, Any]):
        """Send WebSocket update if manager is available."""
        if self.websocket_manager:
            try:
                if update_type == "status_update":
                    # Create workflow data structure for status update
                    workflow_data = {
                        "status": data.get("status", "unknown"),
                        "data": data
                    }
                    await self.websocket_manager.broadcast_status_update(workflow_id, workflow_data)
                elif update_type == "agent_update":
                    await self.websocket_manager.send_agent_update(
                        workflow_id,
                        data.get("agent_id", "unknown"),
                        data.get("status", "unknown"),
                        data.get("progress", 0.0),
                        data.get("output", "")
                    )
                elif update_type == "final_result":
                    await self.websocket_manager.broadcast_final_result(workflow_id, data)
                elif update_type == "workflow_complete":
                    # Create workflow data structure for completion
                    workflow_data = {
                        "status": "completed",
                        "data": {"result": data}
                    }
                    await self.websocket_manager.broadcast_workflow_complete(workflow_id, workflow_data)
            except Exception as e:
                logger.warning(f"Failed to send WebSocket update for {workflow_id}: {e}")

    async def _ensure_initialized(self):
        """Ensure async components are initialized."""
        if self.storage is None:
            self.storage = await get_async_session_storage()
        if self.component_manager is None:
            self.component_manager = await get_component_manager()

    async def start_workflow(
        self,
        question: str,
        session_id: str,
        workflow_type: str = "standard",
        config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Start optimized workflow execution with pre-initialized components.

        Performance optimizations:
        1. Async database operations (non-blocking)
        2. Pre-initialized system components
        3. Parallel session context loading
        4. Real-time progress tracking
        """
        start_time = time.time()
        workflow_id = str(uuid.uuid4())

        try:
            # Ensure async components are ready
            await self._ensure_initialized()

            # Store initial workflow metadata (async, non-blocking)
            workflow_data = {
                "session_id": session_id,
                "question": question,
                "workflow_type": workflow_type,
                "config": config or {},
                "status": "pending",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "start_time": start_time,
                "progress": {
                    "completion_percentage": 0.0,
                    "current_step": "initialization",
                    "estimated_remaining_time": None
                }
            }

            # Fast async database write
            await self.storage.store_workflow(
                workflow_id=workflow_id,
                session_id=session_id,
                status="pending",
                data=workflow_data
            )

            # Prepare execution config
            execution_config = dict(config or {})
            execution_config.update({
                "session_id": session_id,
                "workflow_id": workflow_id,
                "session_files": []  # Will be loaded in parallel
            })

            # Start optimized workflow execution immediately (non-blocking)
            asyncio.create_task(
                self._execute_optimized_workflow(workflow_id, question, workflow_type, execution_config)
            )

            # Track performance metrics
            response_time = time.time() - start_time
            self.performance_metrics["total_workflows"] += 1
            self.performance_metrics["avg_response_time"] = (
                (self.performance_metrics["avg_response_time"] * (self.performance_metrics["total_workflows"] - 1) + response_time) /
                self.performance_metrics["total_workflows"]
            )

            logger.info(f"Workflow started: {workflow_id} (type: {workflow_type}, response_time: {response_time:.3f}s)")
            return workflow_id

        except Exception as e:
            logger.error(f"Failed to start workflow {workflow_id}: {e}")
            # Update workflow status to failed (async)
            if hasattr(self, 'storage') and self.storage:
                await self.storage.update_workflow_status(
                    workflow_id,
                    "failed",
                    {"error": str(e), "failed_at": datetime.now(timezone.utc).isoformat()}
                )
            raise InternalServerError(f"Failed to start workflow: {str(e)}")

    async def _execute_optimized_workflow(
        self,
        workflow_id: str,
        question: str,
        workflow_type: str,
        config: Dict[str, Any]
    ):
        """Execute workflow with optimized performance using pre-initialized components."""
        execution_start_time = time.time()

        try:
            # Update status to executing (fast async operation)
            await self.storage.update_workflow_status(
                workflow_id,
                "executing",
                {
                    "status": "executing",
                    "current_step": "starting_execution",
                    "execution_start_time": execution_start_time
                }
            )

            # Track in active workflows with enhanced metadata
            self.active_workflows[workflow_id] = {
                "status": "executing",
                "start_time": datetime.now(timezone.utc),
                "execution_start_time": execution_start_time,
                "workflow_type": workflow_type,
                "question": question[:100] + "..." if len(question) > 100 else question
            }

            # Execute the actual workflow with optimizations
            await self._execute_workflow_optimized(workflow_id, question, workflow_type, config)

        except Exception as e:
            logger.error(f"Failed to execute optimized workflow {workflow_id}: {e}")
            # Update workflow status to failed (async)
            await self.storage.update_workflow_status(
                workflow_id,
                "failed",
                {"error": str(e), "failed_at": datetime.now(timezone.utc).isoformat()}
            )

            # Update active workflows tracking
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "failed"
                self.active_workflows[workflow_id]["error"] = str(e)

    async def _execute_workflow_optimized(
        self,
        workflow_id: str,
        question: str,
        workflow_type: str,
        config: Dict[str, Any]
    ):
        """
        Execute workflow using pre-initialized components for optimal performance.

        Optimizations:
        - Pre-initialized system components
        - Parallel session context loading
        - Real-time progress updates
        - Async database operations
        """
        try:
            logger.info(f"Executing optimized workflow {workflow_id} (type: {workflow_type})")

            # Load session file context in parallel (non-blocking)
            session_context_task = asyncio.create_task(
                self._load_session_context_parallel(config["session_id"], workflow_id)
            )

            # Update progress: session loading
            await self.storage.update_workflow_status(
                workflow_id,
                "executing",
                {
                    "status": "executing",
                    "current_step": "loading_session_context",
                    "progress": {"completion_percentage": 10.0}
                }
            )

            # Get session context (this should be fast due to parallel loading)
            session_files = await session_context_task
            config["session_files"] = session_files

            # Update progress: starting execution
            await self.storage.update_workflow_status(
                workflow_id,
                "executing",
                {
                    "status": "executing",
                    "current_step": "starting_workflow_execution",
                    "progress": {"completion_percentage": 20.0}
                }
            )

            # Send WebSocket update for execution start
            await self._send_websocket_update(workflow_id, "status_update", {
                "status": "executing",
                "current_phase": "starting_workflow_execution",
                "progress": {"completion_percentage": 20.0}
            })

            # Execute workflow using pre-initialized components
            # FIXED: Run workflow in thread to prevent blocking the event loop
            if workflow_type == "enhanced":
                # Use pre-initialized AdvancedCoordinationFlow
                flow_class = self.component_manager.get_advanced_coordination_flow_class()
                workflow = flow_class(config)

                inputs = {
                    "original_query": question,
                    "processed_query": question,
                    "workflow_id": workflow_id,
                    "session_id": config["session_id"]
                }

                # Run in thread to prevent blocking the event loop
                result = await asyncio.to_thread(self._run_enhanced_workflow_sync, workflow, inputs)
                # Extract final answer from enhanced workflow result
                final_answer = self._extract_final_answer_from_result(result)
                result = {"final_answer": final_answer, "workflow_type": "enhanced", "raw_result": str(result)}
            else:
                # Use pre-initialized MultiAgentSystem
                system = self.component_manager.get_multi_agent_system()
                # Run in thread to prevent blocking the event loop
                result = await asyncio.to_thread(self._run_standard_workflow_sync, system, question, config)
                # Extract final answer from standard workflow result
                final_answer = self._extract_final_answer_from_result(result)
                result = {"final_answer": final_answer, "workflow_type": "standard", "raw_result": result}

            # Calculate execution time
            execution_time = time.time() - self.active_workflows[workflow_id]["execution_start_time"]

            # Store complete result with performance metrics
            completion_data = {
                "status": "completed",
                "result": result,
                "completed_at": datetime.now(timezone.utc).isoformat(),
                "execution_time": execution_time,
                "session_files_count": len(session_files)
            }

            await self.storage.update_workflow_status(
                workflow_id,
                "completed",
                completion_data
            )

            # Send WebSocket updates for completion
            final_answer = result.get("final_answer", str(result))
            await self._send_websocket_update(workflow_id, "final_result", {
                "chunk": final_answer,
                "final_answer": final_answer
            })

            await self._send_websocket_update(workflow_id, "workflow_complete", {
                "status": "completed",
                "final_answer": final_answer,
                "execution_time": execution_time,
                "quality_score": 1.0
            })

            # Update active workflows tracking
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "completed"
                self.active_workflows[workflow_id]["result"] = result
                self.active_workflows[workflow_id]["execution_time"] = execution_time

            # Update performance metrics
            self.performance_metrics["avg_execution_time"] = (
                (self.performance_metrics["avg_execution_time"] * (self.performance_metrics["total_workflows"] - 1) + execution_time) /
                self.performance_metrics["total_workflows"]
            )

            logger.info(f"Workflow completed: {workflow_id} (execution_time: {execution_time:.2f}s)")

        except Exception as e:
            logger.error(f"Optimized workflow execution failed {workflow_id}: {e}")

            # Store error with enhanced details
            error_data = {
                "status": "failed",
                "error": str(e),
                "failed_at": datetime.now(timezone.utc).isoformat(),
                "workflow_type": workflow_type
            }

            await self.storage.update_workflow_status(
                workflow_id,
                "failed",
                error_data
            )

            # Update active workflows tracking
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "failed"
                self.active_workflows[workflow_id]["error"] = str(e)

    def _run_enhanced_workflow_sync(self, workflow, inputs):
        """Run enhanced workflow synchronously in a thread."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(workflow.kickoff_async(inputs=inputs))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Enhanced workflow execution failed: {e}")
            raise

    def _run_standard_workflow_sync(self, system, question, config):
        """Run standard workflow synchronously in a thread."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(system.answer_question(question, config))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Standard workflow execution failed: {e}")
            raise

    async def execute_workflow_with_sse(
        self,
        question: str,
        session_id: str,
        workflow_type: str = "enhanced",
        config: Optional[Dict[str, Any]] = None,
        sse_manager = None
    ) -> str:
        """
        Execute enhanced workflow with real-time SSE updates.

        This method integrates SSE streaming into the workflow execution,
        sending status updates and final answer in the same format as the simple endpoint.
        """
        try:
            logger.info(f"Starting enhanced workflow with SSE for session {session_id}")

            # Send initial status update
            if sse_manager:
                await sse_manager.send_tool_status_update(session_id, {
                    "status": "workflow_starting",
                    "message": "Enhanced workflow initialization",
                    "workflow_type": "enhanced"
                })

            # Prepare configuration with SSE integration
            execution_config = dict(config or {})
            execution_config.update({
                "session_id": session_id,
                "sse_manager": sse_manager,
                "sse_enabled": True
            })

            # Load session file context
            if sse_manager:
                await sse_manager.send_tool_status_update(session_id, {
                    "status": "loading_context",
                    "message": "Loading session context and files"
                })

            session_context = await self._load_session_context_parallel(session_id, "sse_workflow")
            execution_config["session_context"] = session_context

            # Execute enhanced workflow
            if sse_manager:
                await sse_manager.send_tool_status_update(session_id, {
                    "status": "executing_workflow",
                    "message": "Running enhanced multi-agent workflow"
                })

            # Get pre-initialized AdvancedCoordinationFlow
            flow_class = self.component_manager.get_advanced_coordination_flow_class()
            workflow = flow_class(execution_config)

            inputs = {
                "original_query": question,
                "processed_query": question,
                "workflow_id": f"sse_{session_id}",
                "session_id": session_id
            }

            # Run enhanced workflow in thread to prevent blocking
            result = await asyncio.to_thread(self._run_enhanced_workflow_sync, workflow, inputs)

            # Extract final answer
            final_answer = self._extract_final_answer_from_result(result)

            logger.info(f"Enhanced workflow completed for session {session_id}")
            return final_answer

        except Exception as e:
            logger.error(f"Enhanced workflow with SSE failed for session {session_id}: {e}")
            if sse_manager:
                await sse_manager.send_tool_status_update(session_id, {
                    "status": "workflow_error",
                    "message": f"Workflow execution failed: {str(e)}"
                })
            raise

    def _extract_final_answer_from_result(self, result):
        """Extract final answer from workflow result, handling different formats."""
        try:
            # Handle different result formats
            if isinstance(result, dict):
                # Try different possible keys for the final answer
                final_answer = (
                    result.get("final_answer") or
                    result.get("answer") or
                    result.get("output") or
                    result.get("content")
                )
                
                # If not found directly, try nested structures
                if not final_answer:
                    workflow_summary = result.get("workflow_summary", {})
                    if isinstance(workflow_summary, dict):
                        final_answer = workflow_summary.get("final_answer")
                
                # If still not found, try to get from nested result
                if not final_answer and "result" in result:
                    nested_result = result["result"]
                    if isinstance(nested_result, dict):
                        final_answer = nested_result.get("final_answer")
                
                # Last resort for dict: convert to string
                if not final_answer:
                    final_answer = str(result)
                    
            elif isinstance(result, str):
                final_answer = result
            else:
                # Handle other types (convert to string)
                final_answer = str(result)
                
            return final_answer
            
        except Exception as e:
            logger.error(f"Failed to extract final answer from result: {e}")
            return str(result)

    async def _load_session_context_parallel(self, session_id: str, workflow_id: str) -> List[Dict[str, Any]]:
        """Load session file context in parallel for better performance."""
        try:
            # This would be implemented to load session context efficiently
            # For now, return empty list to avoid blocking
            logger.info(f"Loading session context for {session_id} (workflow: {workflow_id})")

            # Simulate parallel loading (replace with actual implementation)
            await asyncio.sleep(0.1)  # Simulate fast loading

            return []  # Return empty list for now

        except Exception as e:
            logger.warning(f"Could not load session context for {session_id}: {e}")
            return []
            
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status with progress details."""
        # Ensure storage is initialized
        await self._ensure_initialized()

        workflow_data = await self.storage.get_workflow(workflow_id)

        if not workflow_data:
            raise WorkflowNotFoundError(workflow_id)

        status = workflow_data["status"]
        data = workflow_data["data"]

        if status == "completed":
            return self._format_completed_status(workflow_id, workflow_data)
        elif status == "executing":
            return self._format_executing_status(workflow_id, workflow_data)
        elif status == "failed":
            return self._format_failed_status(workflow_id, workflow_data)
        else:
            return self._format_basic_status(workflow_id, workflow_data)
    
    def _format_completed_status(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format completed workflow status."""
        data = workflow_data["data"]
        result = data.get("result", {})

        # Extract final answer from result - now using structured format
        final_answer = ""
        if isinstance(result, dict):
            # First try the structured format (new format)
            final_answer = result.get("final_answer")
            
            # If not found, try legacy format for backward compatibility
            if not final_answer:
                final_answer = (
                    result.get("answer") or
                    result.get("output") or
                    result.get("content") or
                    str(result)
                )
        elif isinstance(result, str):
            # Handle legacy string results
            final_answer = result
        else:
            final_answer = str(result)

        # Calculate execution time safely
        execution_time = 0.0
        try:
            execution_time = float(data.get("execution_time", 0.0))
        except (ValueError, TypeError):
            execution_time = 0.0

        # Get completion timestamp
        completed_at = data.get("completed_at", workflow_data.get("updated_at", ""))

        return {
            "workflow_id": workflow_id,
            "status": "completed",
            "current_phase": "completed",
            "final_answer": final_answer,
            "progress": {
                "completion_percentage": 100.0,
                "current_step": "completed",
                "estimated_remaining_time": 0.0
            },
            "agents": {
                "task_manager": {"status": "completed", "progress": 100.0},
                "researcher": {"status": "completed", "progress": 100.0},
                "librarian": {"status": "completed", "progress": 100.0},
                "writer": {"status": "completed", "progress": 100.0}
            },
            "metrics": {
                "execution_time": execution_time,
                "completed_at": completed_at,
                "session_files_count": data.get("session_files_count", 0),
                "total_tokens": data.get("total_tokens", 0),
                "total_cost": data.get("total_cost", 0.0)
            },
            "real_time_updates": None
        }
    
    def _format_executing_status(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format executing workflow status."""
        data = workflow_data["data"]

        # Get progress from active workflows if available
        active_info = self.active_workflows.get(workflow_id, {})

        # Calculate elapsed time safely
        elapsed_time = 0.0
        try:
            # Try different timestamp fields - check both data and workflow_data levels
            created_at = (
                data.get("created_at") or
                workflow_data.get("created_at") or
                data.get("start_time") or
                workflow_data.get("start_time")
            )
            if created_at:
                # Handle different timestamp formats
                if isinstance(created_at, str):
                    # Remove 'Z' and add timezone if needed
                    if created_at.endswith('Z'):
                        created_at = created_at[:-1] + '+00:00'
                    start_time = datetime.fromisoformat(created_at)
                else:
                    start_time = created_at
                elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            elif "execution_start_time" in active_info:
                elapsed_time = time.time() - active_info["execution_start_time"]
        except Exception as e:
            logger.warning(f"Could not calculate elapsed time for workflow {workflow_id}: {e}")
            # Fallback to active workflow info
            if "start_time" in active_info:
                try:
                    elapsed_time = time.time() - active_info["start_time"]
                except:
                    elapsed_time = 0.0

        # Get current step from data
        current_step = data.get("current_step", "agent_execution")
        current_phase = data.get("current_phase", "processing")

        # Estimate progress based on elapsed time and current step
        progress_percentage = min(50.0 + (elapsed_time / 300.0) * 40.0, 90.0)  # Cap at 90% until complete

        return {
            "workflow_id": workflow_id,
            "status": "executing",
            "current_phase": current_phase,
            "final_answer": "",
            "progress": {
                "completion_percentage": progress_percentage,
                "current_step": current_step,
                "estimated_remaining_time": max(60.0 - elapsed_time, 10.0)  # Estimate remaining time
            },
            "agents": {
                "task_manager": {"status": "completed", "progress": 100.0},
                "researcher": {"status": "working", "progress": min(75.0, progress_percentage)},
                "librarian": {"status": "working", "progress": min(60.0, progress_percentage)},
                "writer": {"status": "pending", "progress": 0.0}
            },
            "metrics": {
                "elapsed_time": elapsed_time,
                "start_time": active_info.get("start_time", "unknown")
            },
            "real_time_updates": {
                "websocket_url": f"ws://localhost:8000/api/v1/ws/workflows/{workflow_id}"
            }
        }
    
    def _format_failed_status(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format failed workflow status."""
        data = workflow_data["data"]

        # Get error information safely
        error_message = data.get("error", "Unknown error")
        failed_at = data.get("failed_at", workflow_data.get("updated_at", ""))

        return {
            "workflow_id": workflow_id,
            "status": "failed",
            "current_phase": "error",
            "final_answer": "",
            "progress": {
                "completion_percentage": 0.0,
                "current_step": "failed",
                "estimated_remaining_time": 0.0
            },
            "agents": {
                "task_manager": {"status": "failed", "progress": 0.0},
                "researcher": {"status": "failed", "progress": 0.0},
                "librarian": {"status": "failed", "progress": 0.0},
                "writer": {"status": "failed", "progress": 0.0}
            },
            "metrics": {
                "error": error_message,
                "failed_at": failed_at,
                "execution_time": data.get("execution_time", 0.0)
            },
            "real_time_updates": None
        }
    
    def _format_basic_status(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format basic workflow status."""
        status = workflow_data["status"]
        data = workflow_data.get("data", {})

        return {
            "workflow_id": workflow_id,
            "status": status,
            "current_phase": "pending",
            "final_answer": "",
            "progress": {
                "completion_percentage": 0.0,
                "current_step": "initialization",
                "estimated_remaining_time": None
            },
            "agents": {
                "task_manager": {"status": "pending", "progress": 0.0},
                "researcher": {"status": "pending", "progress": 0.0},
                "librarian": {"status": "pending", "progress": 0.0},
                "writer": {"status": "pending", "progress": 0.0}
            },
            "metrics": {
                "created_at": workflow_data.get("created_at", ""),
                "updated_at": workflow_data.get("updated_at", "")
            },
            "real_time_updates": {
                "websocket_url": f"ws://localhost:8000/api/v1/ws/workflows/{workflow_id}"
            }
        }
    
    def _extract_agent_status(self, result: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract agent status from workflow result."""
        # This would extract agent information from the workflow result
        # For now, return a basic structure
        return {
            "task_manager": {
                "status": "completed",
                "progress": 100.0,
                "output": "Execution plan created"
            },
            "researcher": {
                "status": "completed", 
                "progress": 100.0,
                "output": "Research completed"
            },
            "librarian": {
                "status": "completed",
                "progress": 100.0, 
                "output": "Document retrieval completed"
            },
            "writer": {
                "status": "completed",
                "progress": 100.0,
                "output": "Final synthesis completed"
            }
        }
    
    async def cancel_workflow(self, workflow_id: str, reason: Optional[str] = None) -> bool:
        """Cancel a running workflow."""
        workflow_data = await self.storage.get_workflow(workflow_id)
        
        if not workflow_data:
            raise WorkflowNotFoundError(workflow_id)
        
        if workflow_data["status"] not in ["pending", "executing"]:
            return False  # Cannot cancel completed/failed workflows
        
        # Update status to cancelled
        cancel_data = {
            "status": "cancelled",
            "reason": reason,
            "cancelled_at": datetime.now(timezone.utc).isoformat()
        }
        
        await self.storage.update_workflow_status(workflow_id, "cancelled", cancel_data)
        
        # Remove from active workflows
        if workflow_id in self.active_workflows:
            del self.active_workflows[workflow_id]
        
        logger.info(f"Workflow cancelled: {workflow_id}")
        return True


# Global optimized workflow service instance
_workflow_service = None


def get_workflow_service() -> OptimizedWorkflowService:
    """Get the global optimized workflow service instance."""
    global _workflow_service
    if _workflow_service is None:
        _workflow_service = OptimizedWorkflowService()
    return _workflow_service


async def get_optimized_workflow_service() -> OptimizedWorkflowService:
    """Get the global optimized workflow service instance (async version)."""
    service = get_workflow_service()
    await service._ensure_initialized()
    return service
