"""
Session service for the DSPy Multi-Agent System API.

Manages session-based file storage and isolation using the existing vector database.
"""

import aiofiles
import uuid
import logging
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import UploadFile
from datetime import datetime, timezone

# Import existing components
import sys
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Import real vector database implementation
from src.infrastructure.storage.vector_database import EnterpriseVectorDatabase

# Import embedding service with mock fallback (embedding functionality not yet implemented)
try:
    from src.infrastructure.storage.embedding_service import EnterpriseEmbeddingService
except ImportError as e:
    logging.warning(f"Embedding service not available, using mock: {e}")
    # TEMPORARY: Mock embedding service until vector features implemented
    class EnterpriseEmbeddingService:
        """Temporary mock - will be replaced when embedding features are implemented"""
        def __init__(self, config):
            self.config = config

        async def get_embedding_async(self, text):
            raise NotImplementedError("Embedding service not yet implemented")

        async def embed_texts(self, texts):
            raise NotImplementedError("Embedding service not yet implemented")

from ..models.errors import InternalServerError

logger = logging.getLogger(__name__)


class SessionService:
    """
    Manages session-based file storage and isolation.
    
    Integration with existing system:
    - Uses EnterpriseVectorDatabase with metadata filtering for session isolation
    - Uses EnterpriseEmbeddingService for file processing
    - Reuses existing vector DB configuration
    """
    
    def __init__(self):
        self.upload_dir = Path("data/uploads")
        self.upload_dir.mkdir(parents=True, exist_ok=True)

        # Use real vector database implementation (FIXED: No more mocks)
        self.vector_db = EnterpriseVectorDatabase({
            "type": "chroma",  # Using Chroma for development
            "collection_name": "multi_agent_kb",  # Same as main system
            "persist_directory": "./data/chroma_db",  # Same as main system
            "dimension": 1536
        })

        # Initialize vector database asynchronously when first used
        self._vector_db_initialized = False

        # Use embedding service (mock for now until embedding features implemented)
        self.embedding_service = EnterpriseEmbeddingService({
            "provider": "openai",
            "model_name": "text-embedding-3-small",
            "dimension": 1536,
            "batch_size": 100,
            "cache_enabled": True
        })

        logger.info("SessionService initialized with real vector DB implementation")

    async def _ensure_vector_db_initialized(self):
        """Ensure vector database is initialized."""
        if not self._vector_db_initialized:
            try:
                await self.vector_db.initialize()
                self._vector_db_initialized = True
                logger.info("Vector database initialized successfully")
            except Exception as e:
                logger.warning(f"Vector database initialization failed: {e}")
                # Continue without vector DB for now

    async def store_session_file(
        self,
        session_id: str,
        file: UploadFile,
        custom_name: Optional[str] = None
    ) -> str:
        """
        Store file with session isolation to filesystem.

        FIXED: Uploads files to session-specific directories without requiring embedding.
        Vector database integration is disabled until embedding service is fully implemented.
        """
        try:
            file_id = str(uuid.uuid4())
            filename = custom_name or file.filename or f"file_{file_id}"

            # Validate file size (10MB limit)
            content = await file.read()
            if len(content) > 10 * 1024 * 1024:
                raise ValueError("File size exceeds 10MB limit")

            # Create session-specific directory structure
            session_dir = self.upload_dir / session_id / "files"
            session_dir.mkdir(parents=True, exist_ok=True)

            # Save file to disk with original filename
            file_path = session_dir / f"{file_id}_{filename}"

            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)

            # Store file metadata in session directory (JSON file)
            metadata = {
                "file_id": file_id,
                "filename": filename,
                "file_path": str(file_path),
                "upload_time": datetime.now(timezone.utc).isoformat(),
                "file_size": len(content),
                "content_type": file.content_type or "application/octet-stream",
                "session_id": session_id,
                "status": "uploaded"  # Not "processed" since no embedding
            }

            # Save metadata to JSON file
            metadata_path = session_dir / f"{file_id}_metadata.json"
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(json.dumps(metadata, indent=2))

            logger.info(f"File stored for session {session_id}: {filename} ({file_id}) at {file_path}")
            return file_id
            
        except Exception as e:
            logger.error(f"Failed to store file for session {session_id}: {e}")
            raise InternalServerError(f"Failed to store file: {str(e)}")
        
    async def get_session_file_context(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get file context for session from filesystem.

        FIXED: Reads file metadata from JSON files in session directory.
        """
        try:
            session_dir = self.upload_dir / session_id / "files"
            if not session_dir.exists():
                logger.debug(f"No files directory for session {session_id}")
                return []

            file_context = []

            # Find all metadata files in session directory
            for metadata_file in session_dir.glob("*_metadata.json"):
                try:
                    async with aiofiles.open(metadata_file, 'r') as f:
                        content = await f.read()
                        metadata = json.loads(content)

                    # Read actual file content if needed
                    file_path = Path(metadata["file_path"])
                    if file_path.exists():
                        async with aiofiles.open(file_path, 'rb') as f:
                            file_content = await f.read()

                        # Extract text content for context
                        text_content = await self._extract_text_content(
                            file_content,
                            metadata.get("content_type", ""),
                            metadata.get("filename", "")
                        )

                        file_context.append({
                            "content": text_content,
                            "filename": metadata.get("filename", "unknown"),
                            "file_id": metadata.get("file_id"),
                            "upload_time": metadata.get("upload_time"),
                            "file_size": metadata.get("file_size")
                        })

                except Exception as e:
                    logger.warning(f"Failed to read metadata file {metadata_file}: {e}")
                    continue

            logger.debug(f"Retrieved {len(file_context)} files for session {session_id}")
            return file_context

        except Exception as e:
            logger.error(f"Failed to get file context for session {session_id}: {e}")
            return []
    
    async def get_session_files(self, session_id: str) -> List[Dict[str, Any]]:
        """Get list of files uploaded for a session."""
        try:
            # Get file context and format for API response
            file_context = await self.get_session_file_context(session_id)
            
            files = []
            for file_info in file_context:
                files.append({
                    "file_id": file_info.get("file_id"),
                    "file_name": file_info.get("filename"),
                    "upload_time": file_info.get("upload_time"),
                    "processed": False,  # Files are uploaded but not processed (no embedding)
                    "file_size": file_info.get("file_size")
                })
            
            return files
            
        except Exception as e:
            logger.error(f"Failed to get session files for {session_id}: {e}")
            return []
    
    async def _extract_text_content(self, content: bytes, content_type: str, filename: str) -> str:
        """Extract text content from uploaded file."""
        try:
            # Handle text files
            if content_type and content_type.startswith('text/'):
                return content.decode('utf-8', errors='ignore')
            
            # Handle common document formats
            if filename.lower().endswith('.txt'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.md'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.json'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.csv'):
                return content.decode('utf-8', errors='ignore')
            else:
                # For binary files, create a description
                return f"Binary file: {filename} (Size: {len(content)} bytes, Type: {content_type})"
                
        except Exception as e:
            logger.warning(f"Failed to extract text from {filename}: {e}")
            return f"File: {filename} (Content extraction failed)"
    
    async def delete_session_file(self, session_id: str, file_id: str) -> bool:
        """Delete a specific file from a session."""
        try:
            # This would require implementing delete functionality in the vector DB
            # For now, we'll mark it as deleted in metadata
            logger.warning(f"File deletion not fully implemented: {file_id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete file {file_id} from session {session_id}: {e}")
            return False
    
    async def cleanup_session_files(self, session_id: str) -> bool:
        """Clean up all files for a session."""
        try:
            # Remove files from disk
            session_dir = self.upload_dir / session_id
            if session_dir.exists():
                import shutil
                shutil.rmtree(session_dir)
                logger.info(f"Cleaned up session directory: {session_dir}")
            
            # Vector DB cleanup would require additional implementation
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup session files for {session_id}: {e}")
            return False


# Global session service instance
_session_service = None


def get_session_service() -> SessionService:
    """Get the global session service instance."""
    global _session_service
    if _session_service is None:
        _session_service = SessionService()
    return _session_service
