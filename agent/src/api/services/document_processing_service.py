"""
Document Processing Service for DSPy RAG Integration

This service implements document processing pipeline with:
- Multimodal content processing
- Intelligent chunking using DSPy best practices
- Vector database storage with metadata
- Real-time progress updates via SSE
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, AsyncGenerator
from pathlib import Path
from datetime import datetime, timezone

# Import existing infrastructure
from src.infrastructure.storage.multimodal_processor import MultimodalProcessor, ContentType, ProcessedContent
from src.infrastructure.storage.vector_database import EnterpriseVectorDatabase, SearchResult
from src.infrastructure.storage.embedding_service import EnterpriseEmbeddingService

# Import component manager to access initialized services
from .component_manager import get_component_manager

# Import SSE service for real-time updates
from .sse_service import sse_manager

logger = logging.getLogger(__name__)


class DocumentProcessingService:
    """
    Enterprise document processing service implementing DSPy RAG best practices.
    
    Features:
    - Smart chunking with semantic boundaries (DSPy best practice)
    - Multimodal content processing
    - Vector database integration
    - Real-time progress tracking via SSE
    - Metadata-rich storage for advanced retrieval
    """
    
    def __init__(self):
        self.processor = None
        self.vector_db = None
        self.embedding_service = None
        self.logger = logging.getLogger(__name__)
        
        # DSPy best practice settings
        self.chunk_size = 1000  # Optimal for most retrieval tasks
        self.chunk_overlap = 100  # Preserve context between chunks
        self.batch_size = 50  # Efficient batch processing
        
    async def initialize(self):
        """Initialize the document processing service with pre-initialized components."""
        try:
            # Get pre-initialized components from component manager
            component_manager = await get_component_manager()
            
            self.vector_db = component_manager.get_vector_database()
            self.embedding_service = component_manager.get_embedding_service()
            
            if not self.vector_db or not self.embedding_service:
                raise RuntimeError("Vector database or embedding service not initialized")
            
            # Initialize multimodal processor with optimized settings
            processor_config = {
                'max_text_chunk_size': self.chunk_size,
                'text_overlap': self.chunk_overlap,
                'supported_image_formats': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'],
                'image_max_size': (800, 800)
            }
            
            self.processor = MultimodalProcessor(processor_config)
            await self.processor.initialize()
            
            logger.info("✅ Document processing service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize document processing service: {e}")
            raise
    
    async def process_file(
        self, 
        file_path: str, 
        session_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a single file through the complete pipeline.
        
        Args:
            file_path: Path to the file to process
            session_id: Session ID for SSE updates
            metadata: Additional metadata to store with chunks
            
        Returns:
            Processing result with statistics
        """
        processing_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Send initial progress update
            await self._send_progress_update(
                session_id, 
                processing_id, 
                "starting", 
                {"file_path": file_path, "message": "Starting document processing"}
            )
            
            # Step 1: Process content with multimodal processor
            await self._send_progress_update(
                session_id, processing_id, "processing", 
                {"step": "content_extraction", "message": "Extracting content from file"}
            )
            
            processed_content = await self._process_file_content(file_path)
            
            # Step 2: Create intelligent chunks using DSPy best practices
            await self._send_progress_update(
                session_id, processing_id, "processing", 
                {"step": "chunking", "message": "Creating semantic chunks"}
            )
            
            chunks = await self._create_semantic_chunks(processed_content, file_path, metadata)
            
            # Step 3: Generate embeddings
            await self._send_progress_update(
                session_id, processing_id, "processing", 
                {"step": "embedding", "message": f"Generating embeddings for {len(chunks)} chunks"}
            )
            
            embedded_chunks = await self._generate_embeddings(chunks)
            
            # Step 4: Store in vector database
            await self._send_progress_update(
                session_id, processing_id, "processing", 
                {"step": "storage", "message": "Storing in vector database"}
            )
            
            storage_result = await self._store_in_vector_db(embedded_chunks)
            
            # Calculate final statistics
            processing_time = time.time() - start_time
            result = {
                "processing_id": processing_id,
                "file_path": file_path,
                "chunks_created": len(chunks),
                "chunks_stored": len(embedded_chunks) if storage_result else 0,
                "processing_time": processing_time,
                "success": storage_result,
                "content_type": processed_content.content_type.value,
                "metadata": processed_content.metadata
            }
            
            # Send completion update
            await self._send_progress_update(
                session_id, processing_id, "completed", 
                {"result": result, "message": f"Successfully processed {len(chunks)} chunks in {processing_time:.2f}s"}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            await self._send_progress_update(
                session_id, processing_id, "error", 
                {"error": str(e), "message": f"Failed to process file: {e}"}
            )
            raise
    
    async def _process_file_content(self, file_path: str) -> ProcessedContent:
        """Process file content using multimodal processor."""
        # Detect content type and process
        content_type = await self.processor._detect_content_type(file_path)
        
        # Read file content based on type
        if content_type in [ContentType.TEXT, ContentType.DOCUMENT]:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        else:
            with open(file_path, 'rb') as f:
                content = f.read()
        
        return await self.processor.process_content(content, content_type, file_path)
    
    async def _create_semantic_chunks(
        self, 
        processed_content: ProcessedContent, 
        file_path: str,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Create semantic chunks following DSPy best practices.
        
        Uses RecursiveCharacterTextSplitter approach with semantic boundaries.
        """
        text_content = processed_content.text_content
        
        # Use the processor's chunking method which implements best practices
        chunks = self.processor._chunk_text(text_content)
        
        # Create enhanced chunk documents with rich metadata
        chunk_documents = []
        for i, chunk_text in enumerate(chunks):
            # Calculate overlap information for context preservation
            start_char = i * (self.chunk_size - self.chunk_overlap) if i > 0 else 0
            end_char = start_char + len(chunk_text)
            
            chunk_metadata = {
                # File metadata
                "source_file": Path(file_path).name,
                "source_path": file_path,
                "file_type": Path(file_path).suffix.lower(),
                
                # Chunk metadata
                "chunk_index": i,
                "chunk_count": len(chunks),
                "chunk_start": start_char,
                "chunk_end": end_char,
                "chunk_size": len(chunk_text),
                
                # Processing metadata
                "processing_timestamp": datetime.now(timezone.utc).isoformat(),
                "content_type": processed_content.content_type.value,
                "language": processed_content.metadata.get("language", "unknown"),
                
                # Document structure metadata (for semantic search)
                "word_count": len(chunk_text.split()),
                "contains_numbers": any(char.isdigit() for char in chunk_text),
                "contains_urls": "http" in chunk_text or "www." in chunk_text,
            }
            
            # Add additional metadata if provided
            if additional_metadata:
                chunk_metadata.update(additional_metadata)
            
            # Add original document metadata
            chunk_metadata.update(processed_content.metadata)
            
            chunk_documents.append({
                "id": f"{Path(file_path).stem}_{i}_{uuid.uuid4().hex[:8]}",
                "content": chunk_text,
                "metadata": chunk_metadata
            })
        
        return chunk_documents
    
    async def _generate_embeddings(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate embeddings for chunks using the embedding service."""
        # Extract text content for embedding generation
        texts = [chunk["content"] for chunk in chunks]
        
        # Generate embeddings in batches for efficiency
        embeddings = await self.embedding_service.embed_texts(texts)
        
        # Combine chunks with their embeddings
        embedded_chunks = []
        for i, chunk in enumerate(chunks):
            chunk_with_embedding = chunk.copy()
            chunk_with_embedding["embedding"] = embeddings[i]
            embedded_chunks.append(chunk_with_embedding)
        
        return embedded_chunks
    
    async def _store_in_vector_db(self, embedded_chunks: List[Dict[str, Any]]) -> bool:
        """Store embedded chunks in the vector database."""
        try:
            # Transform chunks to the format expected by vector database
            documents = []
            for chunk in embedded_chunks:
                doc = {
                    "id": chunk["id"],
                    "content": chunk["content"],
                    "metadata": chunk["metadata"],
                    "embedding": chunk["embedding"],
                    "source": chunk["metadata"]["source_file"],
                    "document_type": chunk["metadata"]["content_type"]
                }
                documents.append(doc)
            
            # Store in vector database
            success = await self.vector_db.upsert_documents(documents, batch_size=self.batch_size)
            return success
            
        except Exception as e:
            logger.error(f"Error storing chunks in vector database: {e}")
            return False
    
    async def _send_progress_update(
        self, 
        session_id: str, 
        processing_id: str, 
        status: str, 
        data: Dict[str, Any]
    ):
        """Send real-time progress updates via SSE."""
        try:
            update_data = {
                "type": "document_processing",
                "processing_id": processing_id,
                "status": status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **data
            }
            
            await sse_manager.send_tool_status_update(session_id, update_data)
            
        except Exception as e:
            logger.warning(f"Failed to send progress update: {e}")
    
    async def search_documents(
        self, 
        query: str, 
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for relevant documents using the vector database."""
        try:
            # Generate query embedding
            query_embeddings = await self.embedding_service.embed_texts([query])
            query_vector = query_embeddings[0]
            
            # Search in vector database
            results = await self.vector_db.semantic_search(
                query_vector=query_vector,
                limit=limit,
                filters=filters,
                include_metadata=True
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics from the vector database."""
        try:
            stats = await self.vector_db.get_collection_stats()
            return {
                "total_documents": stats.get("total_count", 0),
                "collection_name": stats.get("collection_name", "unknown"),
                "vector_dimension": stats.get("vector_dimension", 0),
                "last_updated": stats.get("last_updated")
            }
        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {}


# Global service instance
_document_processing_service = None


async def get_document_processing_service() -> DocumentProcessingService:
    """Get or create the document processing service singleton."""
    global _document_processing_service
    
    if _document_processing_service is None:
        _document_processing_service = DocumentProcessingService()
        await _document_processing_service.initialize()
    
    return _document_processing_service 