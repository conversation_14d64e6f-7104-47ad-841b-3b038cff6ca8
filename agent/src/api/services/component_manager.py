"""
Component manager for pre-initializing heavy system components.

Manages lifecycle of MultiAgentSystem and other heavy components to avoid
initialization overhead during workflow execution.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class ComponentManager:
    """Manages pre-initialized system components for optimal performance."""
    
    def __init__(self):
        self._components = {}
        self._initialization_times = {}
        self._health_status = {}
        self._initialized = False
        self._init_lock = asyncio.Lock()
        
    async def initialize_all_components(self, config: Dict[str, Any] = None):
        """Initialize all heavy components at startup."""
        if self._initialized:
            return
            
        async with self._init_lock:
            if self._initialized:
                return
                
            logger.info("🚀 Initializing system components for optimal performance...")
            start_time = time.time()
            
            try:
                # Initialize components in parallel where possible
                await asyncio.gather(
                    self._init_multi_agent_system(config),
                    self._init_workflow_flows(config),
                    self._init_evaluation_pipeline(config),
                    self._init_vector_database(config),
                    self._init_react_retrieval_tools(config),
                    return_exceptions=True
                )
                
                self._initialized = True
                total_time = time.time() - start_time
                logger.info(f"✅ All components initialized in {total_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Component initialization failed: {e}")
                raise
    
    async def _init_multi_agent_system(self, config: Dict[str, Any] = None):
        """Initialize MultiAgentSystem."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing MultiAgentSystem...")

            # Import and initialize - this MUST work for the app to function
            from src.main import MultiAgentSystem
            system = MultiAgentSystem()

            # Store the initialized system
            self._components["multi_agent_system"] = system
            self._initialization_times["multi_agent_system"] = time.time() - start_time
            self._health_status["multi_agent_system"] = "healthy"

            logger.info(f"   ✅ MultiAgentSystem ready ({self._initialization_times['multi_agent_system']:.2f}s)")

        except Exception as e:
            logger.error(f"   ❌ MultiAgentSystem initialization failed: {e}")
            logger.error(f"   🔥 CRITICAL: Cannot start API without MultiAgentSystem!")
            self._health_status["multi_agent_system"] = "failed"
            # This is critical - we MUST have the MultiAgentSystem
            raise RuntimeError(f"CRITICAL: MultiAgentSystem initialization failed: {e}")
    
    async def _init_workflow_flows(self, config: Dict[str, Any] = None):
        """Initialize workflow flows."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing workflow flows...")
            
            # Import flow classes
            from src.orchestration.flows.advanced_coordination_flow import AdvancedCoordinationFlow
            from src.orchestration.flows.main_workflow import MainWorkflowFlow
            
            # Pre-initialize flow classes (but not instances)
            self._components["advanced_coordination_flow_class"] = AdvancedCoordinationFlow
            self._components["main_workflow_flow_class"] = MainWorkflowFlow
            
            self._initialization_times["workflow_flows"] = time.time() - start_time
            self._health_status["workflow_flows"] = "healthy"
            
            logger.info(f"   ✅ Workflow flows ready ({self._initialization_times['workflow_flows']:.2f}s)")
            
        except Exception as e:
            logger.error(f"   ❌ Workflow flows initialization failed: {e}")
            self._health_status["workflow_flows"] = "failed"
            raise
    
    async def _init_evaluation_pipeline(self, config: Dict[str, Any] = None):
        """Initialize evaluation pipeline."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing evaluation pipeline...")

            # Import evaluation components
            from src.optimization.dspy.evaluation.evaluation_pipeline import AutomatedEvaluationPipeline
            from src.optimization.dspy.evaluation.quality_gates import QualityGateSystem

            # Pre-initialize evaluation pipeline with config
            evaluation_config = config or {}
            evaluation_pipeline = AutomatedEvaluationPipeline(evaluation_config)

            # Initialize quality gates with default thresholds (optional component)
            try:
                from src.optimization.dspy.evaluation.quality_gates import QualityThresholds
                default_thresholds = QualityThresholds()
                quality_gates = QualityGateSystem(default_thresholds, evaluation_config)
            except Exception as qg_error:
                logger.warning(f"   ⚠️  Quality gates initialization failed: {qg_error}")
                quality_gates = None

            self._components["evaluation_pipeline"] = evaluation_pipeline
            self._components["quality_gates"] = quality_gates

            self._initialization_times["evaluation_pipeline"] = time.time() - start_time
            self._health_status["evaluation_pipeline"] = "healthy"

            logger.info(f"   ✅ Evaluation pipeline ready ({self._initialization_times['evaluation_pipeline']:.2f}s)")

        except Exception as e:
            logger.error(f"   ❌ Evaluation pipeline initialization failed: {e}")
            self._health_status["evaluation_pipeline"] = "failed"
            # Don't raise - evaluation is optional
            self._components["evaluation_pipeline"] = None
            self._components["quality_gates"] = None
    
    async def _init_vector_database(self, config: Dict[str, Any] = None):
        """Initialize vector database and embedding service."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing vector database...")

            # Import the vector database components
            from src.infrastructure.storage.vector_database import EnterpriseVectorDatabase
            from src.infrastructure.storage.embedding_service import EnterpriseEmbeddingService
            import os

            # Read vector database type from environment variable
            vector_db_type = os.getenv('VECTOR_DB_TYPE', 'qdrant')
            
            # Prepare vector database configuration
            vector_config = {
                'type': vector_db_type,
                'collection_name': 'dspy_knowledge_base',
                'dimension': 1536,  # OpenAI text-embedding-3-small dimension
                'metric_type': 'COSINE'
            }
            
            # Add database-specific configuration
            if vector_db_type == 'qdrant':
                # For local Qdrant, we don't need API key
                qdrant_api_key = os.getenv('QDRANT_API_KEY')
                if qdrant_api_key and qdrant_api_key not in ['', 'your-qdrant-api-key']:
                    # Only include API key if it's actually set and not a placeholder
                    vector_config.update({
                        'host': os.getenv('QDRANT_HOST', 'localhost'),
                        'port': int(os.getenv('QDRANT_PORT', '6333')),
                        'api_key': qdrant_api_key
                    })
                else:
                    # No API key for local Qdrant
                    vector_config.update({
                        'host': os.getenv('QDRANT_HOST', 'localhost'),
                        'port': int(os.getenv('QDRANT_PORT', '6333'))
                    })
            elif vector_db_type == 'chroma':
                vector_config.update({
                    'host': os.getenv('CHROMA_HOST', 'localhost'),
                    'port': int(os.getenv('CHROMA_PORT', '8000'))
                })
            
            logger.info(f"   🔧 Using {vector_db_type} vector database")
            print(f"   🔧 Using {vector_db_type} vector database with config: {vector_config}")

            # Override with config if provided
            if config and 'vector_database' in config:
                vector_config.update(config['vector_database'])

            # Initialize vector database
            vector_db = EnterpriseVectorDatabase(vector_config)
            await vector_db.initialize()

            # Initialize embedding service
            embedding_config = {
                'provider': 'openai',
                'model': 'text-embedding-3-small',
                'dimension': 1536,
                'batch_size': 100
            }

            if config and 'embedding_service' in config:
                embedding_config.update(config['embedding_service'])

            embedding_service = EnterpriseEmbeddingService(embedding_config)
            await embedding_service.initialize()

            # Store initialized components
            self._components["vector_database"] = vector_db
            self._components["embedding_service"] = embedding_service
            self._initialization_times["vector_database"] = time.time() - start_time
            self._health_status["vector_database"] = "healthy"

            logger.info(f"   ✅ Vector database ready ({self._initialization_times['vector_database']:.2f}s)")

        except Exception as e:
            logger.error(f"   ❌ Vector database initialization failed: {e}")
            self._health_status["vector_database"] = "failed"
            # Don't raise - vector database is optional for basic functionality
            self._components["vector_database"] = None
            self._components["embedding_service"] = None
    
    async def _init_react_retrieval_tools(self, config: Dict[str, Any] = None):
        """Initialize ReAct agent retrieval tools with enhanced cross-encoder reranking."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing ReAct agent retrieval tools with cross-encoder reranking...")
            
            # Import enhanced retrieval tools
            from src.agents.tools.dspy_retrieval_tools import (
                EnhancedEnterpriseDocumentRetriever, 
                EnterpriseRAGModule
            )
            
            # Check if we have vector database and embedding service
            vector_db = self._components.get("vector_database")
            embedding_service = self._components.get("embedding_service")
            
            if vector_db is None or embedding_service is None:
                logger.warning("   ⚠️  Vector database or embedding service not available, skipping ReAct retrieval tools")
                self._components["react_retrieval_tools"] = None
                return
            
            # Initialize enhanced retriever with cross-encoder reranking
            enhanced_retriever = EnhancedEnterpriseDocumentRetriever(
                k=5,
                similarity_threshold=0.35,
                enable_reranking=True
            )
            
            # Initialize the retriever components
            await enhanced_retriever.initialize()
            
            # Initialize RAG module with enhanced retriever
            rag_module = EnterpriseRAGModule(
                retriever=enhanced_retriever,
                num_passages=5,
                use_cot=True
            )
            await rag_module.initialize()
            
            # Store the retrieval components
            self._components["react_retrieval_tools"] = {
                "initialized": True,
                "enhanced_retriever": enhanced_retriever,
                "rag_module": rag_module,
                "reranking_enabled": True
            }
            
            self._initialization_times["react_retrieval_tools"] = time.time() - start_time
            self._health_status["react_retrieval_tools"] = "healthy"
            
            logger.info(f"   ✅ ReAct retrieval tools with cross-encoder reranking ready ({self._initialization_times['react_retrieval_tools']:.2f}s)")
            
        except Exception as e:
            logger.error(f"   ❌ ReAct retrieval tools initialization failed: {e}")
            self._health_status["react_retrieval_tools"] = "failed"
            # Don't raise - this is optional functionality
            self._components["react_retrieval_tools"] = None
    
    def get_multi_agent_system(self):
        """Get pre-initialized MultiAgentSystem."""
        if not self._initialized:
            raise RuntimeError("Components not initialized. Call initialize_all_components() first.")
        
        system = self._components.get("multi_agent_system")
        if not system:
            raise RuntimeError("MultiAgentSystem not available")
        
        return system
    
    def get_advanced_coordination_flow_class(self):
        """Get AdvancedCoordinationFlow class."""
        if not self._initialized:
            raise RuntimeError("Components not initialized. Call initialize_all_components() first.")
        
        flow_class = self._components.get("advanced_coordination_flow_class")
        if not flow_class:
            raise RuntimeError("AdvancedCoordinationFlow not available")
        
        return flow_class
    
    def get_evaluation_pipeline(self):
        """Get pre-initialized evaluation pipeline."""
        return self._components.get("evaluation_pipeline")
    
    def get_vector_database(self):
        """Get pre-initialized vector database."""
        return self._components.get("vector_database")
    
    def get_embedding_service(self):
        """Get pre-initialized embedding service."""
        return self._components.get("embedding_service")
    
    def get_react_retrieval_tools(self):
        """Get pre-initialized ReAct retrieval tools."""
        return self._components.get("react_retrieval_tools")
    
    async def health_check(self) -> Dict[str, Any]:
        """Get health status of all components."""
        return {
            "initialized": self._initialized,
            "components": self._health_status.copy(),
            "initialization_times": self._initialization_times.copy(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def reinitialize_component(self, component_name: str, config: Dict[str, Any] = None):
        """Reinitialize a specific component."""
        logger.info(f"🔄 Reinitializing component: {component_name}")
        
        if component_name == "multi_agent_system":
            await self._init_multi_agent_system(config)
        elif component_name == "workflow_flows":
            await self._init_workflow_flows(config)
        elif component_name == "evaluation_pipeline":
            await self._init_evaluation_pipeline(config)
        elif component_name == "vector_database":
            await self._init_vector_database(config)
        elif component_name == "react_retrieval_tools":
            await self._init_react_retrieval_tools(config)
        else:
            raise ValueError(f"Unknown component: {component_name}")
    
    async def shutdown(self):
        """Shutdown all components gracefully."""
        logger.info("🔄 Shutting down components...")
        
        # Cleanup components if they have cleanup methods
        for name, component in self._components.items():
            try:
                if hasattr(component, 'shutdown'):
                    await component.shutdown()
                elif hasattr(component, 'close'):
                    await component.close()
            except Exception as e:
                logger.warning(f"Error shutting down {name}: {e}")
        
        self._components.clear()
        self._health_status.clear()
        self._initialization_times.clear()
        self._initialized = False
        
        logger.info("✅ Components shutdown complete")


# Global component manager instance
_component_manager = None


async def get_component_manager() -> ComponentManager:
    """Get the global component manager instance."""
    global _component_manager
    if _component_manager is None:
        _component_manager = ComponentManager()
    return _component_manager


async def initialize_components_at_startup(config: Dict[str, Any] = None):
    """Initialize all components at application startup."""
    manager = await get_component_manager()
    await manager.initialize_all_components(config)


async def shutdown_components():
    """Shutdown all components at application shutdown."""
    global _component_manager
    if _component_manager:
        await _component_manager.shutdown()
        _component_manager = None
