"""
Enterprise RAG Tool for CrewAI integration.

Wraps the existing EnterpriseRAGModule for use by CrewAI agents.
"""

from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import asyncio

from src.agents.tools.dspy_retrieval_tools import EnterpriseRAGModule


class RAGQueryInput(BaseModel):
    """Input schema for RAG queries."""
    query: str = Field(..., description="The query to search for in the knowledge base")


class EnterpriseRAGTool(BaseTool):
    """CrewAI-compatible tool for Enterprise RAG queries."""
    
    name: str = "Enterprise Knowledge Base"
    description: str = (
        "Search the enterprise knowledge base for relevant information. "
        "Use this tool to find context, background information, and relevant facts "
        "from the organization's document repository. This tool accesses the same "
        "knowledge base used by the simple question endpoint."
    )
    args_schema: Type[BaseModel] = RAGQueryInput
    
    def __init__(self):
        super().__init__()
        # Initialize RAG module on first use to avoid Pydantic field issues
        self._rag_module = None
    
    def _get_rag_module(self):
        """Lazy initialization of RAG module."""
        if self._rag_module is None:
            self._rag_module = EnterpriseRAGModule()
        return self._rag_module
    
    def _run(self, query: str) -> str:
        """Execute RAG query synchronously."""
        try:
            # DSPy modules work synchronously
            rag_module = self._get_rag_module()
            prediction = rag_module.forward(question=query)
            
            if hasattr(prediction, 'answer') and prediction.answer:
                return f"Knowledge Base Result:\n{prediction.answer}"
            else:
                return "No relevant information found in knowledge base."
                
        except Exception as e:
            return f"Knowledge base search failed: {str(e)}"
    
    async def _arun(self, query: str) -> str:
        """Execute RAG query asynchronously."""
        # Run in thread pool to avoid blocking
        return await asyncio.to_thread(self._run, query) 