from typing import Type, Optional
import requests
from bs4 import BeautifulSoup
import time
import random
from urllib.parse import quote_plus, urljoin
from pydantic import BaseModel, Field
from crewai.tools import BaseTool


class WebSearchInput(BaseModel):
    """Input schema for WebSearchTool."""
    query: str = Field(..., description="Search query to find relevant web content")
    max_results: int = Field(default=5, description="Maximum number of results to return")


class WebSearchTool(BaseTool):
    name: str = "WebSearchTool"
    description: str = "Search the web for information using DuckDuckGo search. Returns title, snippet, and URL for each result."
    args_schema: Type[BaseModel] = WebSearchInput
    
    def _get_session(self):
        """Get or create a requests session with proper headers."""
        if not hasattr(self, '_session'):
            self._session = requests.Session()
            # Set up session with proper headers to avoid detection
            self._session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            })
        return self._session
    
    def _run(self, query: str, max_results: int = 5) -> str:
        """
        Execute web search using DuckDuckGo and return formatted results.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            
        Returns:
            Formatted string containing search results
        """
        try:
            # Use DuckDuckGo search (doesn't require API key)
            search_url = f"https://duckduckgo.com/html/?q={quote_plus(query)}"
            
            # Add random delay to avoid rate limiting
            time.sleep(random.uniform(1, 3))
            
            session = self._get_session()
            response = session.get(search_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract search results
            results = []
            result_elements = soup.find_all('div', class_='result')
            
            for i, element in enumerate(result_elements[:max_results]):
                try:
                    # Extract title
                    title_elem = element.find('a', class_='result__a')
                    title = title_elem.get_text(strip=True) if title_elem else "No title"
                    
                    # Extract URL
                    url = title_elem.get('href') if title_elem else ""
                    if url.startswith('/l/?uddg='):
                        # DuckDuckGo uses redirect URLs, extract actual URL
                        url = url.split('uddg=')[1] if 'uddg=' in url else url
                    
                    # Extract snippet
                    snippet_elem = element.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else "No description available"
                    
                    if title and url:
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet
                        })
                        
                except Exception as e:
                    print(f"Error parsing result {i}: {e}")
                    continue
            
            # Format results as readable text
            if not results:
                return f"No search results found for query: {query}"
            
            formatted_results = f"Search results for '{query}':\n\n"
            for i, result in enumerate(results, 1):
                formatted_results += f"{i}. **{result['title']}**\n"
                formatted_results += f"   URL: {result['url']}\n"
                formatted_results += f"   Summary: {result['snippet']}\n\n"
            
            return formatted_results
            
        except requests.RequestException as e:
            return f"Error performing web search: {str(e)}"
        except Exception as e:
            return f"Unexpected error during web search: {str(e)}"
    
    def __del__(self):
        """Clean up session when tool is destroyed."""
        if hasattr(self, '_session'):
            self._session.close() 