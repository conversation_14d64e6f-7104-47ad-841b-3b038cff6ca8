from typing import Type, Optional, Dict, List
import requests
from bs4 import BeautifulSoup
import time
import random
from urllib.parse import urljoin, urlparse
from pydantic import BaseModel, Field
from crewai.tools import BaseTool
import mimetypes
import chardet


class DocumentRetrievalInput(BaseModel):
    """Input schema for DocumentRetrievalTool."""
    url: str = Field(..., description="URL of the document or webpage to retrieve and extract text from")
    extract_links: bool = Field(default=False, description="Whether to extract links from the document")
    max_length: int = Field(default=5000, description="Maximum length of extracted text")


class DocumentRetrievalTool(BaseTool):
    name: str = "DocumentRetrievalTool"
    description: str = "Retrieve and extract text content from web pages and documents. Supports HTML pages and returns clean, readable text."
    args_schema: Type[BaseModel] = DocumentRetrievalInput
    
    def _get_session(self):
        """Get or create a requests session with proper headers."""
        if not hasattr(self, '_session'):
            self._session = requests.Session()
            # Set up session with proper headers
            self._session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            })
        return self._session
    
    def _run(self, url: str, extract_links: bool = False, max_length: int = 5000) -> str:
        """
        Retrieve and extract text content from a URL.
        
        Args:
            url: URL to retrieve content from
            extract_links: Whether to extract links from the document
            max_length: Maximum length of extracted text
            
        Returns:
            Extracted text content or error message
        """
        try:
            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return f"Invalid URL format: {url}"
            
            # Add random delay to avoid rate limiting
            time.sleep(random.uniform(0.5, 2))
            
            # Fetch the document
            session = self._get_session()
            response = session.get(url, timeout=15)
            response.raise_for_status()
            
            # Detect content type
            content_type = response.headers.get('content-type', '').lower()
            
            if 'html' in content_type or 'xml' in content_type:
                return self._extract_html_content(response, url, extract_links, max_length)
            elif 'text' in content_type:
                return self._extract_text_content(response, max_length)
            elif 'pdf' in content_type:
                return "PDF content extraction not implemented in this prototype. Please use an HTML version or text extract."
            else:
                return f"Unsupported content type: {content_type}. Only HTML, XML, and plain text are supported."
                
        except requests.RequestException as e:
            return f"Error fetching document from {url}: {str(e)}"
        except Exception as e:
            return f"Unexpected error retrieving document: {str(e)}"
    
    def _extract_html_content(self, response: requests.Response, url: str, extract_links: bool, max_length: int) -> str:
        """Extract text content from HTML response."""
        try:
            # Detect encoding
            encoding = response.encoding
            if not encoding or encoding.lower() == 'iso-8859-1':
                # Try to detect encoding from content
                detected = chardet.detect(response.content)
                if detected['confidence'] > 0.7:
                    encoding = detected['encoding']
                else:
                    encoding = 'utf-8'
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser', from_encoding=encoding)
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                script.decompose()
            
            # Extract title
            title = soup.find('title')
            title_text = title.get_text(strip=True) if title else "No title"
            
            # Extract main content
            main_content = self._find_main_content(soup)
            
            # Get clean text
            text = main_content.get_text(separator=' ', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.split('\n')]
            clean_lines = [line for line in lines if line and len(line) > 5]
            clean_text = ' '.join(clean_lines)
            
            # Truncate if necessary
            if len(clean_text) > max_length:
                clean_text = clean_text[:max_length] + "... [Content truncated]"
            
            result = f"Document Title: {title_text}\n"
            result += f"Source URL: {url}\n"
            result += f"Content Length: {len(clean_text)} characters\n\n"
            result += f"Content:\n{clean_text}"
            
            # Extract links if requested
            if extract_links:
                links = self._extract_links(soup, url)
                if links:
                    result += f"\n\nExtracted Links ({len(links)}):\n"
                    for i, link in enumerate(links[:10], 1):  # Limit to 10 links
                        result += f"{i}. {link['text']} - {link['url']}\n"
            
            return result
            
        except Exception as e:
            return f"Error parsing HTML content: {str(e)}"
    
    def _find_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Find the main content area of the page."""
        # Try to find main content using common patterns
        main_selectors = [
            'main', '[role="main"]', '.main-content', '#main-content',
            '.content', '#content', '.post-content', '.article-content',
            'article', '.entry-content', '.page-content'
        ]
        
        for selector in main_selectors:
            main = soup.select_one(selector)
            if main and len(main.get_text(strip=True)) > 100:
                return main
        
        # If no main content found, try to find the largest text block
        candidates = soup.find_all(['div', 'section', 'p'])
        if candidates:
            best_candidate = max(candidates, key=lambda x: len(x.get_text(strip=True)))
            if len(best_candidate.get_text(strip=True)) > 50:
                return best_candidate
        
        # Fallback to body
        return soup.find('body') or soup
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Extract links from the document."""
        links = []
        for a_tag in soup.find_all('a', href=True):
            href = a_tag.get('href', '').strip()
            text = a_tag.get_text(strip=True)
            
            if href and text and len(text) > 2:
                # Convert relative URLs to absolute
                full_url = urljoin(base_url, href)
                
                # Filter out non-useful links
                if not any(skip in href.lower() for skip in ['javascript:', 'mailto:', 'tel:', '#']):
                    links.append({
                        'text': text[:100],  # Limit text length
                        'url': full_url
                    })
        
        return links
    
    def _extract_text_content(self, response: requests.Response, max_length: int) -> str:
        """Extract content from plain text response."""
        try:
            text = response.text
            
            if len(text) > max_length:
                text = text[:max_length] + "... [Content truncated]"
            
            return f"Plain Text Content:\n{text}"
            
        except Exception as e:
            return f"Error extracting text content: {str(e)}"
    
    def __del__(self):
        """Clean up session when tool is destroyed."""
        if hasattr(self, '_session'):
            self._session.close() 