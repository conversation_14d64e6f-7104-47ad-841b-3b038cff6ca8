BEGIN;

-- Create table
CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY,
    file_id VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    chat_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_files_chat_id ON files(chat_id);
CREATE INDEX IF NOT EXISTS idx_files_deleted_at ON files(deleted_at);

-- Add foreign key constraint
ALTER TABLE files
ADD CONSTRAINT fk_files_chat_id
FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE;

COMMIT;
