BEGIN;

-- Drop foreign key constraint
ALTER TABLE chats DROP CONSTRAINT IF EXISTS fk_chats_user_id;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS fk_messages_chat_id;

-- Drop indexes
DROP INDEX IF EXISTS idx_messages_created_at;
DROP INDEX IF EXISTS idx_messages_deleted_at;
DROP INDEX IF EXISTS idx_messages_chat_id;
DROP INDEX IF EXISTS idx_chats_deleted_at;
DROP INDEX IF EXISTS idx_chats_user_id;
DROP INDEX IF EXISTS idx_users_email_active;
DROP INDEX IF EXISTS idx_users_deleted_at;

-- Drop tables
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS chats;
DROP TABLE IF EXISTS users;

COMMIT;
