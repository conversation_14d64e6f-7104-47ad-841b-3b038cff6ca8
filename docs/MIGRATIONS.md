# Database Migration Container

This container provides database migration capabilities using the `migrate/migrate` tool with a convenient wrapper script.

## Overview

The migration container is built on top of `migrate/migrate:4` and includes a bash wrapper script that automatically configures the database connection using environment variables.

## Environment Variables

The following environment variables must be set for the migration container to work:

- `POSTGRES_USER` - Database username
- `POSTGRES_PASSWORD` - Database password
- `POSTGRES_HOST` - Database hostname (usually `postgres` in Docker Compose)
- `POSTGRES_PORT` - Database port (default: 5432)
- `POSTGRES_DB` - Database name

## Usage

### With Docker Compose

#### Run migrations up (default)
```bash
docker compose run --rm migrate
```

#### Run specific migration commands
```bash
# Migrate down by 1 step
docker compose run --rm migrate down 1

# Migrate to specific version
docker compose run --rm migrate goto 2

# Check current migration version
docker compose run --rm migrate version

# Force set version without running migrations
docker compose run --rm migrate force 1

# Drop everything and re-run all migrations
docker compose run --rm migrate drop
docker compose run --rm migrate up
```

#### Create new migrations
```bash
# Create a new migration file
docker compose run --rm migrate create -ext sql -dir migrations add_users_table
```

### Standalone Docker

```bash
# Build the image
docker build -t aura-migrate ./migrate

# Run migrations
docker run --rm \
  -e POSTGRES_USER=admin \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_HOST=localhost \
  -e POSTGRES_PORT=5432 \
  -e POSTGRES_DB=aura \
  -v $(pwd)/migrate/migrations:/app/migrations \
  aura-migrate up
```

## Migration Files

Migration files should be placed in the `./migrate/migrations` directory with the following naming convention:

```
migrations/
├── 000001_initial_schema.up.sql
├── 000001_initial_schema.down.sql
├── 000002_add_users_table.up.sql
├── 000002_add_users_table.down.sql
└── ...
```

### Example Migration Files

**000001_initial_schema.up.sql**
```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**000001_initial_schema.down.sql**
```sql
DROP TABLE IF EXISTS users;
DROP EXTENSION IF EXISTS "uuid-ossp";
```

## Common Commands

| Command | Description |
|---------|-------------|
| `up` | Apply all pending migrations |
| `up N` | Apply next N migrations |
| `down N` | Rollback last N migrations |
| `goto V` | Migrate to version V |
| `drop` | Drop everything inside database |
| `force V` | Set version V but don't run migration |
| `version` | Print current migration version |

## Development Workflow

1. **Create a new migration**:
   ```bash
   docker compose run --rm migrate create -ext sql -dir migrations add_new_feature
   ```

2. **Edit the generated migration files** in `./migrate/migrations/`

3. **Test the migration**:
   ```bash
   # Apply the migration
   docker compose run --rm migrate up

   # Test rollback
   docker compose run --rm migrate down 1

   # Re-apply
   docker compose run --rm migrate up
   ```

4. **Check migration status**:
   ```bash
   docker compose run --rm migrate version
   ```

## Troubleshooting

### Connection Issues
- Ensure the database container is running and healthy
- Verify environment variables are correctly set
- Check network connectivity between containers

### Migration Failures
- Check migration file syntax
- Ensure migration files are properly named
- Use `force` command to manually set version if needed:
  ```bash
  docker compose run --rm migrate force <version>
  ```

### Reset Database
To completely reset the database and re-run all migrations:
```bash
docker compose run --rm migrate drop
docker compose run --rm migrate up
```

## Files Structure

```
migrate/
├── Dockerfile
├── migrate.sh          # Wrapper script
└── migrations/         # Migration files directory
    ├── 000001_*.up.sql
    ├── 000001_*.down.sql
    └── ...
```
