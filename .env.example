COMPOSE_FILE=docker-compose.dev.yml

APP_NAME=aura
APP_ENV=development
LOG_LEVEL=info
BASE_DOMAIN=local-env.com
API_PORT=1337
VITE_PORT=5173
JWT_SECRET=9e61304b503a61550d3973519a1f759b896fa3a52e08e0938bbee90fd6b9c924
NPM_AUTH_TOKEN=

POSTGRES_USER=admin
POSTGRES_PASSWORD=adminpassword
POSTGRES_DB=aura
POSTGRES_PORT=5432
POSTGRES_HOST=postgres

LLM_BASE_URL=http://llm-server:11434
LLM_MODEL=gpt-3.5-turbo
LLM_MAX_TOKENS=1000
LLM_TEMPERATURE=0.7
LLM_TIMEOUT=30s
LLM_MAX_RETRIES=3
LLM_SYSTEM_PROMPT="You are a helpful AI assistant. Provide clear, accurate, and helpful responses. Be concise but informative, and always strive to be helpful and respectful."
