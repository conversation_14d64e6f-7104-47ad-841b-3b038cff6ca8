name: aura
services:
  postgres:
    image: registry.backfielddigital.com/dev/aura/postgres:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-postgres
    build:
      context: postgres
      dockerfile: Dockerfile
      tags:
        - registry.backfielddigital.com/dev/aura/postgres:latest
    ports:
      - "${POSTGRES_PORT:-5432}:${POSTGRES_PORT:-5432}"
    command: >
      -c shared_buffers=4GB
      -c effective_cache_size=8GB
      -c work_mem=1GB
      -c maintenance_work_mem=2GB
      -c random_page_cost=2
      -c max_parallel_workers=8
      -c max_parallel_workers_per_gather=6
      -c max_locks_per_transaction=512
    restart: unless-stopped
    volumes:
      - ./data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    networks:
      - default
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U admin -d aura -h localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  ui:
    image: registry.backfielddigital.com/dev/aura/ui:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-ui
    build:
      context: ui
      dockerfile: Dockerfile
      args:
        - VITE_PORT=${VITE_PORT:-5173}
      secrets:
        - npm_token
      tags:
        - registry.backfielddigital.com/dev/aura/ui:latest
    develop:
      watch:
        - action: sync
          path: ./ui
          target: /app
          ignore:
            - node_modules
        - action: rebuild
          path: ./ui/package.json
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_NAME}-ui.tls=true"
      - "traefik.http.routers.${APP_NAME}-ui.tls.certresolver=local-env"
      - "traefik.http.routers.${APP_NAME}-ui.rule=Host(`${APP_NAME}.${BASE_DOMAIN:-backfielddigital.com}`)"
      - "traefik.http.routers.${APP_NAME}-ui.entrypoints=websecure"
      - "traefik.http.services.${APP_NAME}-ui.loadbalancer.server.port=${VITE_PORT:-5173}"
    working_dir: /app
    env_file:
      - .env
    networks:
      - default
      - ingress-backend
    command: ["npm", "run", "dev"]

  api:
    image: registry.backfielddigital.com/dev/aura/api:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-api
    build:
      context: api
      dockerfile: Dockerfile
      tags:
        - registry.backfielddigital.com/dev/aura/api:latest
      args:
        - API_PORT=${API_PORT:-3000}
    develop:
      watch:
        - action: sync+restart
          path: ./api
          target: /app
        - action: rebuild
          path: ./api/go.sum
        - action: rebuild
          path: ./api/go.mod
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_NAME}-api.tls=true"
      - "traefik.http.routers.${APP_NAME}-api.tls.certresolver=local-env"
      - "traefik.http.routers.${APP_NAME}-api.rule=Host(`api.${APP_NAME}.${BASE_DOMAIN:-backfielddigital.com}`)"
      - "traefik.http.routers.${APP_NAME}-api.entrypoints=websecure"
      - "traefik.http.services.${APP_NAME}-api.loadbalancer.server.port=${API_PORT:-3000}"
    working_dir: /app
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - .env
    networks:
      - default
      - ingress-backend
    command: ["go", "run", "cmd/server/main.go"]

  migrate:
    image: registry.backfielddigital.com/dev/aura/migrate:${TAG:-latest}
    container_name: ${COMPOSE_PROJECT_NAME}-migrate
    build:
      context: migrate
      dockerfile: Dockerfile
      tags:
        - registry.backfielddigital.com/dev/aura/migrate:latest
    volumes:
      - ./migrate/migrations:/app/migrations
    working_dir: /app
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - .env
    networks:
      - default
    command: up

  llm-server:
    build:
      context: mock-llm-server-go
      dockerfile: Dockerfile
    environment:
      - GIN_MODE=release


networks:
  default:
    name: ${COMPOSE_PROJECT_NAME}-network
    driver: bridge

  ingress-backend:
    external: true

secrets:
  npm_token:
    environment: NPM_AUTH_TOKEN
