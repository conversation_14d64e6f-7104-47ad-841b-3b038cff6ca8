#!/usr/bin/env python3
"""
Test script for Advanced Question Endpoint with SSE streaming.

Tests the new SSE-based architecture for the advanced workflow endpoint
with real-time updates for agent status and tool usage.
"""

import asyncio
import aiohttp
import json
import sys
import uuid
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_KEY = "dev-api-key-12345"

class AdvancedSSEMonitor:
    """Monitor SSE events for advanced workflow real-time updates."""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.events_received = []
        self.start_time = None
        self.final_answer = None
        
    async def test_advanced_workflow(self):
        """Test the advanced workflow endpoint with SSE streaming."""
        print(f"🚀 Testing Advanced Workflow with SSE Streaming")
        print(f"📋 Session ID: {self.session_id}")
        print("=" * 60)
        
        # Step 1: Submit advanced question
        question_data = {
            "question": "What are the environmental impacts of electric vehicles compared to gasoline vehicles?",
            "session_id": self.session_id,
            "workflow_type": "enhanced",
            "config": {
                "quality_threshold": 0.8,
                "parallel_execution": True,
                "vector_knowledge_enabled": True
            }
        }
        
        print("📤 Submitting advanced question...")
        response = await self._submit_question(question_data)
        
        if not response:
            print("❌ Failed to submit question")
            return False
        
        # Step 2: Monitor SSE stream
        print("🔗 Connecting to SSE stream...")
        return await self._monitor_sse_stream(response)
        
    async def _submit_question(self, question_data: dict) -> dict:
        """Submit advanced question to the API."""
        url = f"{BASE_URL}/api/v1/questions/advanced"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, json=question_data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ Question submitted successfully")
                        print(f"   📋 Session ID: {result.get('session_id')}")
                        print(f"   🔄 Workflow Type: {result.get('workflow_type')}")
                        print(f"   📡 SSE Endpoint: {result.get('sse_endpoint')}")
                        print(f"   🔌 WebSocket Endpoint: {result.get('websocket_endpoint')}")
                        return result
                    else:
                        error_text = await response.text()
                        print(f"❌ Failed to submit question: {response.status} - {error_text}")
                        return None
            except Exception as e:
                print(f"❌ Error submitting question: {e}")
                return None
    
    async def _monitor_sse_stream(self, response_data: dict) -> bool:
        """Monitor SSE stream for real-time updates."""
        sse_url = f"{BASE_URL}{response_data.get('sse_endpoint')}"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache"
        }
        
        print(f"🔗 Connecting to: {sse_url}")
        self.start_time = datetime.now()
        
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(sse_url, headers=headers) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return False
                    
                    print("✅ SSE connection established")
                    
                    # Process SSE events
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            
                            if data:
                                try:
                                    event = json.loads(data)
                                    completion_result = await self._handle_event(event)
                                    
                                    if completion_result:
                                        return True
                                        
                                except json.JSONDecodeError:
                                    continue
                                except Exception as e:
                                    print(f"⚠️ Error handling event: {e}")
                                    continue
                    
                    print("🔚 SSE stream ended")
                    return True
                    
            except asyncio.TimeoutError:
                print("⏰ SSE connection timed out")
                return False
            except Exception as e:
                print(f"❌ SSE connection error: {e}")
                return False
    
    async def _handle_event(self, event: dict) -> bool:
        """Handle incoming SSE event."""
        current_time = datetime.now()
        elapsed = (current_time - self.start_time).total_seconds()
        
        event_type = event.get('type', 'unknown')
        timestamp = event.get('timestamp', '')
        data = event.get('data', {})
        
        self.events_received.append({
            'type': event_type,
            'elapsed': elapsed,
            'timestamp': timestamp,
            'data': data
        })
        
        # Handle different event types
        if event_type == 'connection_established':
            print(f"🔗 [{elapsed:6.2f}s] Connection established")
            print(f"    💬 {data.get('message', '')}")
            
        elif event_type == 'phase_update':
            phase = data.get('phase', 'unknown')
            status = data.get('status', 'unknown')
            
            phase_emoji = {
                'started': '🚀',
                'completed': '✅',
                'failed': '❌'
            }.get(status, '🔄')
            
            print(f"{phase_emoji} [{elapsed:6.2f}s] Phase {phase} - {status}")
            
            if phase == 'advanced_initialization':
                print(f"    🎯 Workflow Type: {data.get('workflow_type', 'unknown')}")
                print(f"    🔧 Features: {', '.join(data.get('features', []))}")
                
        elif event_type == 'agent_status':
            agent_id = data.get('agent_id', 'unknown')
            status = data.get('status', 'unknown')
            
            agent_emoji = {
                'started': '🔄',
                'completed': '✅',
                'error': '❌'
            }.get(status, '❓')
            
            print(f"{agent_emoji} [{elapsed:6.2f}s] Agent {agent_id} - {status}")
            
            if status == 'completed':
                print(f"    📊 Result: {data.get('result_type', 'completed')}")
                
        elif event_type == 'workflow_status':
            status = data.get('status', 'unknown')
            
            if status == 'parallel_execution_started':
                specialists = data.get('specialists', [])
                print(f"⚡ [{elapsed:6.2f}s] Parallel execution started")
                print(f"    🤖 Specialists: {', '.join(specialists)}")
                
            elif status == 'parallel_execution_completed':
                specialists = data.get('specialists', [])
                results_count = data.get('results_count', 0)
                print(f"✅ [{elapsed:6.2f}s] Parallel execution completed")
                print(f"    🤖 Specialists: {', '.join(specialists)}")
                print(f"    📊 Results: {results_count}")
                
            elif status == 'progress':
                completion = data.get('completion_percentage', 0)
                print(f"📈 [{elapsed:6.2f}s] Progress: {completion:.1f}%")
                
                if completion >= 100:
                    final_answer = data.get('final_answer', '')
                    if final_answer:
                        self.final_answer = final_answer
                        print(f"🎉 [{elapsed:6.2f}s] Workflow completed!")
                        print(f"    📝 Answer: {final_answer[:100]}...")
                        print("=" * 60)
                        print("✅ Advanced workflow monitoring complete!")
                        return True
                
        elif event_type == 'tool_status':
            tool_name = data.get('tool_name', 'unknown')
            status = data.get('status', 'unknown')
            
            tool_emoji = {
                'running': '🔄',
                'completed': '✅',
                'failed': '❌'
            }.get(status, '❓')
            
            print(f"{tool_emoji} [{elapsed:6.2f}s] Tool {tool_name} - {status}")
            
        elif event_type == 'question_complete':
            final_answer = data.get('final_answer', '')
            self.final_answer = final_answer
            print(f"🎉 [{elapsed:6.2f}s] Question processing complete!")
            print(f"    📝 Answer: {final_answer[:100]}...")
            print("=" * 60)
            print("✅ Advanced workflow monitoring complete!")
            return True
            
        elif event_type == 'error':
            error_msg = data.get('error', data.get('error_message', 'Unknown error'))
            print(f"💥 [{elapsed:6.2f}s] Error occurred!")
            print(f"    ❌ Error: {error_msg}")
            return True
            
        elif event_type == 'ping':
            print(f"💓 [{elapsed:6.2f}s] Keepalive ping")
            
        return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n📊 Test Summary")
        print("=" * 50)
        print(f"Session ID: {self.session_id}")
        print(f"Events received: {len(self.events_received)}")
        print(f"Final answer received: {'✅' if self.final_answer else '❌'}")
        
        if self.final_answer:
            print(f"Answer length: {len(self.final_answer)} characters")
        
        # Count event types
        event_counts = {}
        for event in self.events_received:
            event_type = event['type']
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        print("\nEvent counts:")
        for event_type, count in sorted(event_counts.items()):
            print(f"  {event_type}: {count}")


async def main():
    """Run the advanced SSE test."""
    print("🧪 Advanced Question Endpoint SSE Test")
    print("=" * 60)
    
    # Generate unique session ID
    session_id = f"test-advanced-{uuid.uuid4().hex[:8]}"
    
    # Create monitor and run test
    monitor = AdvancedSSEMonitor(session_id)
    
    try:
        success = await monitor.test_advanced_workflow()
        
        if success:
            print("✅ Test completed successfully!")
        else:
            print("❌ Test failed!")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    finally:
        monitor.print_summary()


if __name__ == "__main__":
    asyncio.run(main()) 