package middleware

import (
	"net/http"

	"aura-api/pkg/api"

	"github.com/gin-gonic/gin"
)

type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		if len(c.Errors) > 0 {
			lastErr := c.Errors.Last()

			code := http.StatusInternalServerError
			message := lastErr.Error()

			// You can add custom error handling here based on error types
			// Example:
			// if err, ok := lastErr.Err.(YourCustomError); ok {
			//     code = err.Code
			//     message = err.Error()
			// }

			api.ErrorResponse(c, code, message)
			c.Abort()
		}
	}
}
