package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path

		c.Next()

		if path != "/health" {
			latency := time.Since(start)
			statusCode := c.Writer.Status()
			method := c.Request.Method

			if gin.Mode() != gin.ReleaseMode {
				gin.Logger()(c)
				return
			}

			gin.DefaultWriter.Write([]byte(
				time.Now().Format("2006/01/02 15:04:05") +
					" | " +
					method + " " +
					path + " | " +
					strconv.Itoa(statusCode) + " | " +
					latency.String() + "\n"))
		}
	}
}
