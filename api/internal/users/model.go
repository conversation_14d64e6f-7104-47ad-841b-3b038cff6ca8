package users

import (
	"time"

	"gorm.io/gorm"
)

// User represents the user entity
type User struct {
	ID           string         `json:"id" gorm:"primaryKey;type:uuid"`
	Email        string         `json:"email" gorm:"uniqueIndex;not null"`
	Password     string         `json:"-" gorm:"not null"`
	FirstName    string         `json:"firstName" gorm:"column:first_name"`
	LastName     string         `json:"lastName" gorm:"column:last_name"`
	RefreshToken string         `json:"-" gorm:"column:refresh_token"`
	TokenExpiry  time.Time      `json:"-" gorm:"column:token_expiry"`
	CreatedAt    time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}
