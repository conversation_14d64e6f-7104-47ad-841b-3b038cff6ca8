package users

import (
	"context"
	"time"
)

type UserService interface {
	GetByID(ctx context.Context, id string) (*User, error)
	GetByEmail(ctx context.Context, email string) (*User, error)
	Create(ctx context.Context, user *User) error
	UpdateTokens(ctx context.Context, userID, refreshToken string, tokenExpiry time.Time) error
}

type userService struct {
	repo UserRepository
}

func NewUserService(repo UserRepository) UserService {
	return &userService{repo: repo}
}

func (s *userService) GetByID(ctx context.Context, id string) (*User, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *userService) GetByEmail(ctx context.Context, email string) (*User, error) {
	return s.repo.GetByEmail(ctx, email)
}

func (s *userService) Create(ctx context.Context, user *User) error {
	return s.repo.Create(ctx, user)
}

func (s *userService) UpdateTokens(ctx context.Context, userID, refreshToken string, tokenExpiry time.Time) error {
	user, err := s.repo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	user.RefreshToken = refreshToken
	user.TokenExpiry = tokenExpiry

	return s.repo.Update(ctx, user)
}
