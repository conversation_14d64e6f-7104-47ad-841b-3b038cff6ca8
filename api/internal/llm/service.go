package llm

import (
	"context"
	"encoding/json"
	"fmt"

	"aura-api/pkg/connector"
)

// LLMService handles communication with LLM servers
type LLMService interface {
	// File management
	UploadFile(ctx context.Context, req FileUploadRequest) (*FileUploadResponse, error)
	GetFilesBySession(ctx context.Context, sessionID string) (*FilesResponse, error)

	// Question answering
	SubmitSimpleQuestion(ctx context.Context, req SimpleQuestionRequest) (*QuestionResponse, error)
	SubmitAdvancedQuestion(ctx context.Context, req AdvancedQuestionRequest) (*QuestionResponse, error)

	// Workflow management
	GetWorkflowStatus(ctx context.Context, workflowID string) (*WorkflowStatus, error)
	CancelWorkflow(ctx context.Context, workflowID string) (*WorkflowCancelResponse, error)
	ConnectWorkflowWebSocket(ctx context.Context, workflowID string) (connector.WebSocketConnection, error)

	// System health
	GetSystemHealth(ctx context.Context) (*HealthResponse, error)
	Health(ctx context.Context) error
	Close() error
}

type llmService struct {
	connector connector.Manager
	config    Config
}

// Config holds LLM service configuration
type Config struct {
	DefaultModel    string
	MaxTokens       int
	Temperature     float64
	SystemPrompt    string
	ConnectorConfig connector.Config
}

// NewLLMService creates a new LLM service
func NewLLMService(config Config) LLMService {
	connectorManager := connector.NewManager(config.ConnectorConfig)

	return &llmService{
		connector: connectorManager,
		config:    config,
	}
}

// Health performs a health check on the LLM service
func (s *llmService) Health(ctx context.Context) error {
	headers := map[string]string{}
	return s.connector.Health(ctx, EndpointHealthCheck, headers)
}

// Close closes the LLM service and its resources
func (s *llmService) Close() error {
	return s.connector.Close()
}

// UploadFile uploads a file through the LLM service
func (s *llmService) UploadFile(ctx context.Context, req FileUploadRequest) (*FileUploadResponse, error) {
	formFields := map[string]string{
		"session_id": req.SessionID,
	}
	if req.FileName != "" {
		formFields["file_name"] = req.FileName
	}

	headers := map[string]string{}

	response, err := s.connector.UploadFile(ctx, EndpointFileUpload, headers, req.FileData, req.FileName, formFields)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Parse response using JSON marshaling
	var result FileUploadResponse
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse upload response: %w", err)
	}

	return &result, nil
}

// GetFilesBySession retrieves all files for a session
func (s *llmService) GetFilesBySession(ctx context.Context, sessionID string) (*FilesResponse, error) {
	headers := map[string]string{}

	response, err := s.connector.Get(ctx, fmt.Sprintf(EndpointFilesBySession, sessionID), headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get files for session: %w", err)
	}

	var result FilesResponse
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse files response: %w", err)
	}

	return &result, nil
}

// SubmitSimpleQuestion submits a simple question for processing
func (s *llmService) SubmitSimpleQuestion(ctx context.Context, req SimpleQuestionRequest) (*QuestionResponse, error) {
	headers := map[string]string{
		HeaderContentType: ContentTypeJSON,
	}

	requestBody := map[string]interface{}{
		"question":   req.Question,
		"session_id": req.SessionID,
	}
	if req.Language != "" {
		requestBody["language"] = req.Language
	}

	response, err := s.connector.Post(ctx, EndpointSimpleQuestion, headers, requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to submit simple question: %w", err)
	}

	return s.parseQuestionResponse(response)
}

// SubmitAdvancedQuestion submits an advanced question for processing
func (s *llmService) SubmitAdvancedQuestion(ctx context.Context, req AdvancedQuestionRequest) (*QuestionResponse, error) {
	headers := map[string]string{
		HeaderContentType: ContentTypeJSON,
	}

	requestBody := map[string]interface{}{
		"question":   req.Question,
		"session_id": req.SessionID,
	}

	if req.Config != nil {
		requestBody["config"] = req.Config
	}
	if req.WorkflowType != "" {
		requestBody["workflow_type"] = req.WorkflowType
	}

	response, err := s.connector.Post(ctx, EndpointAdvancedQuestion, headers, requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to submit advanced question: %w", err)
	}

	return s.parseQuestionResponse(response)
}

// GetWorkflowStatus retrieves the status of a workflow
func (s *llmService) GetWorkflowStatus(ctx context.Context, workflowID string) (*WorkflowStatus, error) {
	headers := map[string]string{}

	response, err := s.connector.Get(ctx, fmt.Sprintf(EndpointWorkflowStatus, workflowID), headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow status: %w", err)
	}

	// Parse response using JSON marshaling
	var result WorkflowStatus
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse workflow status response: %w", err)
	}

	return &result, nil
}

// CancelWorkflow cancels a running workflow
func (s *llmService) CancelWorkflow(ctx context.Context, workflowID string) (*WorkflowCancelResponse, error) {
	headers := map[string]string{}

	response, err := s.connector.Delete(ctx, fmt.Sprintf(EndpointWorkflowCancel, workflowID), headers)
	if err != nil {
		return nil, fmt.Errorf("failed to cancel workflow: %w", err)
	}

	// Parse response using JSON marshaling
	var result WorkflowCancelResponse
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse cancel workflow response: %w", err)
	}

	return &result, nil
}

// GetSystemHealth retrieves detailed system health information
func (s *llmService) GetSystemHealth(ctx context.Context) (*HealthResponse, error) {
	headers := map[string]string{}

	response, err := s.connector.Get(ctx, EndpointSystemHealth, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get system health: %w", err)
	}

	// Parse response using JSON marshaling
	var result HealthResponse
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse system health response: %w", err)
	}

	return &result, nil
}

// ConnectWorkflowWebSocket establishes a WebSocket connection for workflow updates
func (s *llmService) ConnectWorkflowWebSocket(ctx context.Context, workflowID string) (connector.WebSocketConnection, error) {
	headers := map[string]string{}
	path := fmt.Sprintf(EndpointWorkflowWebSocket, workflowID)

	return s.connector.ConnectWebSocket(ctx, path, headers)
}

// parseQuestionResponse parses a question response from the API using JSON marshaling
func (s *llmService) parseQuestionResponse(response *connector.HTTPResponse) (*QuestionResponse, error) {
	var result QuestionResponse
	if err := json.Unmarshal(response.RawBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse question response: %w", err)
	}

	return &result, nil
}
