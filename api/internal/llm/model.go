package llm

// LLMMessage represents a message in LLM format
type LLMMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// LLMResponse represents a response from the LLM
type LLMResponse struct {
	ID      string `json:"id"`
	Content string `json:"content"`
	Model   string `json:"model"`
	Usage   Usage  `json:"usage"`
}

// Usage represents token usage
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// StreamChunk represents a streaming response chunk
type StreamChunk struct {
	ID        string `json:"id"`
	Content   string `json:"content"`
	Delta     string `json:"delta"`
	Done      bool   `json:"done"`
	SessionID string `json:"session_id,omitempty"`
	Error     error  `json:"-"`
}

// FileUploadRequest represents a file upload request to LLM service
type FileUploadRequest struct {
	FileData  []byte `json:"-" binding:"required"`
	FileName  string `json:"file_name" binding:"required,min=1,max=255"`
	SessionID string `json:"session_id" binding:"required"`
}

// FileUploadResponse represents a file upload response from LLM service
type FileUploadResponse struct {
	Success   bool   `json:"success"`
	FileID    string `json:"file_id"`
	FileName  string `json:"file_name"`
	Timestamp string `json:"timestamp"`
}

// Question Answering Request/Response Models

// SimpleQuestionRequest represents a simple question answering request
type SimpleQuestionRequest struct {
	Question  string `json:"question" binding:"required,min=10,max=1000"`
	Language  string `json:"language,omitempty"`
	SessionID string `json:"session_id" binding:"required"`
}

// AdvancedQuestionRequest represents an advanced question answering request
type AdvancedQuestionRequest struct {
	Question     string          `json:"question" binding:"required,min=10,max=2000"`
	SessionID    string          `json:"session_id" binding:"required"`
	Config       *AdvancedConfig `json:"config,omitempty"`
	WorkflowType string          `json:"workflow_type,omitempty"`
}

// AdvancedConfig holds advanced configuration options
type AdvancedConfig struct {
	EnableOptimization     bool              `json:"enable_optimization"`
	MaxIterations          int               `json:"max_iterations"`
	VectorKnowledgeEnabled bool              `json:"vector_knowledge_enabled"`
	ParallelExecution      bool              `json:"parallel_execution"`
	QualityThreshold       float64           `json:"quality_threshold"`
	SpecialistConfig       *SpecialistConfig `json:"specialist_config,omitempty"`
}

// SpecialistConfig holds specialist-specific configuration
type SpecialistConfig struct {
	IncludePredictions bool `json:"include_predictions"`
}

// QuestionResponse represents the response for question answering requests
type QuestionResponse struct {
	Success    bool   `json:"success"`
	WorkflowID string `json:"workflow_id,omitempty"`
	Error      string `json:"error,omitempty"`
	Message    string `json:"message,omitempty"`
	Timestamp  string `json:"timestamp"`
}

// Workflow Management Models

// WorkflowStatus represents the status of a workflow
type WorkflowStatus struct {
	WorkflowID      string                `json:"workflow_id"`
	Status          string                `json:"status"`
	CurrentPhase    string                `json:"current_phase"`
	FinalAnswer     string                `json:"final_answer"`
	Progress        *WorkflowProgress     `json:"progress"`
	Agents          map[string]*AgentInfo `json:"agents"`
	Metrics         *WorkflowMetrics      `json:"metrics"`
	RealTimeUpdates *RealTimeUpdates      `json:"real_time_updates"`
}

// WorkflowProgress represents the progress of a workflow
type WorkflowProgress struct {
	CompletionPercentage   float64  `json:"completion_percentage"`
	ActiveAgents           []string `json:"active_agents"`
	CompletedSteps         []string `json:"completed_steps"`
	CurrentStep            string   `json:"current_step"`
	EstimatedRemainingTime float64  `json:"estimated_remaining_time"`
}

// AgentInfo represents information about an agent
type AgentInfo struct {
	Status              string  `json:"status"`
	Progress            float64 `json:"progress"`
	Output              string  `json:"output,omitempty"`
	ExecutionTime       float64 `json:"execution_time,omitempty"`
	CurrentTask         string  `json:"current_task,omitempty"`
	EstimatedCompletion string  `json:"estimated_completion,omitempty"`
}

// WorkflowMetrics represents metrics for a workflow
type WorkflowMetrics struct {
	ElapsedTime        float64 `json:"elapsed_time"`
	EstimatedTotalTime float64 `json:"estimated_total_time"`
	CPUUsage           float64 `json:"cpu_usage"`
	MemoryUsage        float64 `json:"memory_usage"`
}

// RealTimeUpdates represents real-time update information
type RealTimeUpdates struct {
	WebSocketURL string `json:"websocket_url"`
}

// WorkflowCancelResponse represents the response for workflow cancellation
type WorkflowCancelResponse struct {
	Success          bool   `json:"success"`
	WorkflowID       string `json:"workflow_id"`
	Status           string `json:"status"`
	Message          string `json:"message"`
	CancellationTime string `json:"cancellation_time"`
}

// File Management Models

// FileInfo represents information about an uploaded file
type FileInfo struct {
	FileID     string `json:"file_id"`
	FileName   string `json:"file_name"`
	UploadTime string `json:"upload_time"`
	Processed  bool   `json:"processed"`
}

// FilesResponse represents the response for getting files by session
type FilesResponse struct {
	Files []FileInfo `json:"files"`
}

// System Health Models

// HealthResponse represents the system health check response
type HealthResponse struct {
	Status      string                    `json:"status"`
	Timestamp   string                    `json:"timestamp"`
	Version     string                    `json:"version"`
	Environment string                    `json:"environment"`
	Components  map[string]*ComponentInfo `json:"components"`
	SystemInfo  *SystemInfo               `json:"system_info"`
}

// ComponentInfo represents information about a system component
type ComponentInfo struct {
	Status           string  `json:"status"`
	ResponseTime     float64 `json:"response_time,omitempty"`
	LastCheck        string  `json:"last_check,omitempty"`
	Connections      int     `json:"connections,omitempty"`
	Size             string  `json:"size,omitempty"`
	BufferSize       int     `json:"buffer_size,omitempty"`
	LastFlush        string  `json:"last_flush,omitempty"`
	TrainingExamples int     `json:"training_examples,omitempty"`
	LastOptimization string  `json:"last_optimization,omitempty"`
}

// SystemInfo represents general system information
type SystemInfo struct {
	Uptime                int     `json:"uptime"`
	MemoryUsage           string  `json:"memory_usage"`
	CPUUsage              float64 `json:"cpu_usage"`
	ActiveWorkflows       int     `json:"active_workflows"`
	TotalQueriesProcessed int     `json:"total_queries_processed"`
}

// WebSocket Message Types

// WebSocketMessage represents a generic WebSocket message
type WebSocketMessage struct {
	Type       string `json:"type"`
	WorkflowID string `json:"workflow_id"`
	Timestamp  string `json:"timestamp"`
	Data       any    `json:"data"`
}

// StatusUpdateData represents status update message data
type StatusUpdateData struct {
	CurrentPhase        string   `json:"current_phase"`
	Progress            float64  `json:"progress"`
	ActiveAgents        []string `json:"active_agents"`
	LatestOutput        string   `json:"latest_output"`
	EstimatedCompletion string   `json:"estimated_completion"`
}

// AgentUpdateData represents agent update message data
type AgentUpdateData struct {
	AgentID       string  `json:"agent_id"`
	Status        string  `json:"status"`
	Progress      float64 `json:"progress"`
	Output        string  `json:"output"`
	ExecutionTime float64 `json:"execution_time"`
	NextAgent     string  `json:"next_agent,omitempty"`
}

// FinalResultData represents final result streaming data
type FinalResultData struct {
	Chunk string `json:"chunk"`
}

// WorkflowCompleteData represents workflow completion data
type WorkflowCompleteData struct {
	Status        string  `json:"status"`
	FinalAnswer   string  `json:"final_answer"`
	ExecutionTime float64 `json:"execution_time"`
	QualityScore  float64 `json:"quality_score"`
}

// ErrorData represents error message data
type ErrorData struct {
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

// Error Response Models

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error     string         `json:"error"`
	Message   string         `json:"message"`
	Details   map[string]any `json:"details,omitempty"`
	Timestamp string         `json:"timestamp"`
	RequestID string         `json:"request_id,omitempty"`
}
