package llm

// API endpoint constants
const (
	// Question answering endpoints
	EndpointSimpleQuestion   = "/api/v1/questions/simple"
	EndpointAdvancedQuestion = "/api/v1/questions/advanced"

	// Workflow management endpoints
	EndpointWorkflowStatus    = "/api/v1/workflows/%s/status"
	EndpointWorkflowCancel    = "/api/v1/workflows/%s"
	EndpointWorkflowWebSocket = "/api/v1/ws/workflows/%s"

	// File management endpoints
	EndpointFileUpload     = "/api/v1/files/upload"
	EndpointFilesBySession = "/api/v1/files/%s"

	// System health endpoints
	EndpointSystemHealth = "/api/v1/health"
	EndpointHealthCheck  = "/health"
)

// Validation constants
const (
	MinQuestionLengthSimple   = 10
	MaxQuestionLengthSimple   = 1000
	MinQuestionLengthAdvanced = 10
	MaxQuestionLengthAdvanced = 2000
)

// Workflow types
const (
	WorkflowTypeStandard = "standard"
	WorkflowTypeEnhanced = "enhanced"
)

// Workflow statuses
const (
	WorkflowStatusPending   = "pending"
	WorkflowStatusExecuting = "executing"
	WorkflowStatusCompleted = "completed"
	WorkflowStatusFailed    = "failed"
	WorkflowStatusCancelled = "cancelled"
)

// Agent statuses
const (
	AgentStatusPending   = "pending"
	AgentStatusWorking   = "working"
	AgentStatusCompleted = "completed"
	AgentStatusFailed    = "failed"
)

// WebSocket message types
const (
	WSMessageTypeStatusUpdate     = "status_update"
	WSMessageTypeAgentUpdate      = "agent_update"
	WSMessageTypeFinalResult      = "final_result"
	WSMessageTypeWorkflowComplete = "workflow_complete"
	WSMessageTypeError            = "error"
)

// Component names for health checks
const (
	ComponentMultiAgentSystem = "multi_agent_system"
	ComponentVectorDatabase   = "vector_database"
	ComponentMetricsCollector = "metrics_collector"
	ComponentDSPyOptimizer    = "dspy_optimizer"
)

// HTTP headers
const (
	HeaderContentType   = "Content-Type"
	HeaderAuthorization = "Authorization"
	HeaderUserAgent     = "User-Agent"
)

// Content types
const (
	ContentTypeJSON      = "application/json"
	ContentTypeMultipart = "multipart/form-data"
	ContentTypeSSE       = "text/event-stream"
)
