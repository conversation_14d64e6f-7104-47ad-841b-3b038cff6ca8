package llm

import "time"

// DefaultAdvancedConfig returns a default advanced configuration
func DefaultAdvancedConfig() *AdvancedConfig {
	return &AdvancedConfig{
		EnableOptimization:     true,
		MaxIterations:          5,
		VectorKnowledgeEnabled: true,
		ParallelExecution:      true,
		QualityThreshold:       0.7,
		SpecialistConfig: &SpecialistConfig{
			IncludePredictions: false,
		},
	}
}

// IsSuccess checks if a question response indicates success
func (r *QuestionResponse) IsSuccess() bool {
	return r.Success
}

// HasError checks if a question response has an error
func (r *QuestionResponse) HasError() bool {
	return r.Error != ""
}

// IsCompleted checks if a workflow status indicates completion (terminal state)
func (w *WorkflowStatus) IsCompleted() bool {
	return w.Status == WorkflowStatusCompleted ||
		w.Status == WorkflowStatusFailed ||
		w.Status == WorkflowStatusCancelled
}

// IsFailed checks if a workflow status indicates failure
func (w *WorkflowStatus) IsFailed() bool {
	return w.Status == WorkflowStatusFailed
}

// IsCancelled checks if a workflow status indicates cancellation
func (w *WorkflowStatus) IsCancelled() bool {
	return w.Status == WorkflowStatusCancelled
}

// IsRunning checks if a workflow is currently running
func (w *WorkflowStatus) IsRunning() bool {
	return w.Status == WorkflowStatusExecuting
}

// GetActiveAgentCount returns the number of currently active agents
func (w *WorkflowStatus) GetActiveAgentCount() int {
	if w.Progress == nil {
		return 0
	}
	return len(w.Progress.ActiveAgents)
}

// GetEstimatedTimeRemaining returns estimated time remaining in seconds
func (w *WorkflowStatus) GetEstimatedTimeRemaining() float64 {
	if w.Progress == nil {
		return 0
	}
	return w.Progress.EstimatedRemainingTime
}

// FormatTimestamp formats a timestamp string for display
func FormatTimestamp(timestamp string) string {
	if timestamp == "" {
		return "Unknown"
	}

	t, err := time.Parse(time.RFC3339, timestamp)
	if err != nil {
		return timestamp // Return original if parsing fails
	}

	return t.Format("2006-01-02 15:04:05")
}

// IsHealthy checks if a component is healthy
func (c *ComponentInfo) IsHealthy() bool {
	return c.Status == "healthy"
}

// IsSystemHealthy checks if the overall system is healthy
func (h *HealthResponse) IsSystemHealthy() bool {
	if h.Status != "healthy" {
		return false
	}

	// Check all components
	for _, component := range h.Components {
		if !component.IsHealthy() {
			return false
		}
	}

	return true
}

// GetUnhealthyComponents returns a list of unhealthy component names
func (h *HealthResponse) GetUnhealthyComponents() []string {
	var unhealthy []string

	for name, component := range h.Components {
		if !component.IsHealthy() {
			unhealthy = append(unhealthy, name)
		}
	}

	return unhealthy
}
