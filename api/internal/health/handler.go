package health

import (
	"net/http"

	"aura-api/internal/config"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Status    string             `json:"status"`
	Version   string             `json:"version"`
	Timestamp string             `json:"timestamp"`
	Details   map[string]Details `json:"details,omitempty"`
}

type Details struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}

func Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		versionInfo := config.GetVersionInfo()

		response := Response{
			Status:    "UP",
			Version:   versionInfo.Version,
			Timestamp: versionInfo.BuildTime.Format(http.TimeFormat),
			Details: map[string]Details{
				"api": {
					Status:  "UP",
					Message: "API is running normally",
				},
			},
		}

		c.JSON(http.StatusOK, response)
	}
}
