package auth

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

var (
	ErrInvalidToken = errors.New("invalid token")
	ErrExpiredToken = errors.New("token has expired")
)

// TokenManager defines JWT token operations
type TokenManager interface {
	GenerateTokenPair(userID, email string) (*TokenPair, error)
	ValidateToken(tokenString string) (jwt.MapClaims, error)
	GetUserIDFromToken(tokenString string) (string, error)
}

type tokenManager struct {
	secretKey     []byte
	accessExpiry  time.Duration
	refreshExpiry time.Duration
}

// NewTokenManager creates a new JWT token manager
func NewTokenManager(secretKey string, accessExpiryMinutes, refreshExpiryDays int) TokenManager {
	return &tokenManager{
		secretKey:     []byte(secretKey),
		accessExpiry:  time.Duration(accessExpiryMinutes) * time.Minute,
		refreshExpiry: time.Duration(refreshExpiryDays) * 24 * time.Hour,
	}
}

// GenerateTokenPair creates a new JWT access and refresh token pair
func (tm *tokenManager) GenerateTokenPair(userID, email string) (*TokenPair, error) {
	if userID == "" {
		return nil, ErrInvalidToken
	}

	accessToken, expiresAt, err := tm.generateAccessToken(userID, email)
	if err != nil {
		return nil, err
	}

	refreshToken, err := tm.generateRefreshToken(userID)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	}, nil
}

// ValidateToken validates a JWT token and returns the claims
func (tm *tokenManager) ValidateToken(tokenString string) (jwt.MapClaims, error) {
	if tokenString == "" {
		return nil, ErrInvalidToken
	}

	token, err := jwt.Parse(tokenString, tm.getSigningKey)
	if err != nil {
		return nil, tm.mapParseError(err)
	}

	if !token.Valid {
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, ErrInvalidToken
	}

	return claims, nil
}

// GetUserIDFromToken extracts the user ID from a token
func (tm *tokenManager) GetUserIDFromToken(tokenString string) (string, error) {
	claims, err := tm.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	userID, ok := claims["userId"].(string)
	if !ok {
		return "", ErrInvalidToken
	}

	return userID, nil
}

func (tm *tokenManager) generateAccessToken(userID, email string) (string, time.Time, error) {
	expiresAt := time.Now().Add(tm.accessExpiry)
	claims := jwt.MapClaims{
		"userId": userID,
		"email":  email,
		"exp":    expiresAt.Unix(),
	}

	token, err := tm.createSignedToken(claims)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create access token: %w", err)
	}

	return token, expiresAt, nil
}

func (tm *tokenManager) generateRefreshToken(userID string) (string, error) {
	expiresAt := time.Now().Add(tm.refreshExpiry)
	claims := jwt.MapClaims{
		"userId": userID,
		"exp":    expiresAt.Unix(),
	}

	token, err := tm.createSignedToken(claims)
	if err != nil {
		return "", fmt.Errorf("failed to create refresh token: %w", err)
	}

	return token, nil
}

func (tm *tokenManager) createSignedToken(claims jwt.MapClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(tm.secretKey)
}

func (tm *tokenManager) getSigningKey(token *jwt.Token) (interface{}, error) {
	if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
		return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
	}
	return tm.secretKey, nil
}

func (tm *tokenManager) mapParseError(err error) error {
	if errors.Is(err, jwt.ErrTokenExpired) {
		return ErrExpiredToken
	}
	return ErrInvalidToken
}
