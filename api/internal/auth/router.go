package auth

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"aura-api/internal/config"
	"aura-api/internal/users"
)

const basePath = "auth"

// SetupRoutes registers auth-related routes
func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	cfg := config.LoadAppConfig()

	userRepo := users.NewUserRepository(db)
	userService := users.NewUserService(userRepo)
	authService := NewAuthService(userService, cfg.JWTSecret)

	handler := NewAuthHandler(authService)

	authGroup := router.Group(basePath)
	{
		authGroup.POST("/register", handler.Register)
		authGroup.POST("/login", handler.Login)
		authGroup.POST("/refresh-token", handler.RefreshToken)
		authGroup.POST("/logout", handler.Logout)
	}
}
