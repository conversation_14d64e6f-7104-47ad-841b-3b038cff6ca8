package auth

import (
	"net/http"
	"time"

	"aura-api/pkg/api"

	"github.com/gin-gonic/gin"
)

type AuthHandler interface {
	Register(c *gin.Context)
	Login(c *gin.Context)
	RefreshToken(c *gin.Context)
	Logout(c *gin.Context)
}

type authHandler struct {
	service AuthService
}

func NewAuthHandler(service AuthService) AuthHandler {
	return &authHandler{service: service}
}

func (h *authHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.Register(c.Request.Context(), req)
	if err != nil {
		h.handleAuthError(c, err)
		return
	}

	h.setAuthCookies(c, resp)
	api.SuccessResponse(c, http.StatusCreated, resp, "User registered successfully")
}

func (h *authHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.Login(c.Request.Context(), req)
	if err != nil {
		h.handleAuthError(c, err)
		return
	}

	h.setAuthCookies(c, resp)
	api.SuccessResponse(c, http.StatusOK, resp, "User logged in successfully")
}

func (h *authHandler) RefreshToken(c *gin.Context) {
	refreshToken, err := h.getRefreshToken(c)
	if err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.RefreshToken(c.Request.Context(), refreshToken)
	if err != nil {
		h.handleAuthError(c, err)
		return
	}

	h.setAuthCookies(c, resp)
	api.SuccessResponse(c, http.StatusOK, resp, "Token refreshed successfully")
}

func (h *authHandler) Logout(c *gin.Context) {
	h.clearAuthCookies(c)
	api.SuccessResponse(c, http.StatusNoContent, (*interface{})(nil), "User logged out successfully")
}

func (h *authHandler) setAuthCookies(c *gin.Context, resp *AuthResponse) {
	maxAge := int(resp.ExpiresAt - time.Now().Unix())

	//TODO: kivenni ha jo a forro ujratoltes
	c.SetSameSite(http.SameSiteNoneMode)
	c.SetCookie("access_token", resp.Token, maxAge, "/", "", true, true)
	c.SetCookie("refresh_token", resp.RefreshToken, 60*60*24*7, "/", "", true, true)
}

func (h *authHandler) clearAuthCookies(c *gin.Context) {
	c.SetCookie("access_token", "", -1, "/", "", false, true)
	c.SetCookie("refresh_token", "", -1, "/", "", false, true)
}

func (h *authHandler) getRefreshToken(c *gin.Context) (string, error) {
	refreshToken, err := c.Cookie("refresh_token")
	if err == nil {
		return refreshToken, nil
	}

	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		return "", err
	}
	return req.RefreshToken, nil
}

func (h *authHandler) handleAuthError(c *gin.Context, err error) {
	switch err {
	case ErrUserAlreadyExists:
		api.ErrorResponse(c, http.StatusConflict, "User with this email already exists")
	case ErrInvalidCredentials:
		api.ErrorResponse(c, http.StatusUnauthorized, "Invalid credentials")
	case ErrInvalidRefreshToken, ErrExpiredToken:
		api.ErrorResponse(c, http.StatusUnauthorized, "Invalid or expired refresh token")
	default:
		api.ErrorResponse(c, http.StatusInternalServerError, err.Error())
	}
}
