package auth

import (
	"net/http"
	"time"

	"aura-api/pkg/api"

	"github.com/gin-gonic/gin"
)

func AuthMiddleware(authService AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		accessToken, accessErr := c.<PERSON>("access_token")
		if accessErr != nil {
			handleExpiredToken(c, authService)
			return
		}

		// Validate the access token
		userID, err := authService.ValidateToken(accessToken)
		if err == nil {
			c.Set("userID", userID)
			c.Next()
			return
		}

		// Handle expired access token
		if isExpiredTokenError(err) {
			handleExpiredToken(c, authService)
			return
		}

		// All other token errors
		api.UnauthorizedResponse(c, "Invalid or expired token")
	}
}

// isExpiredTokenError checks if the error indicates an expired token
func isExpiredTokenError(err error) bool {
	return err == ErrExpiredToken || err.Error() == "token has expired"
}

func handleExpiredToken(c *gin.Context, authService AuthService) {
	refreshToken, err := c.<PERSON>("refresh_token")
	if err != nil {
		api.UnauthorizedResponse(c, "Invalid or missing refresh token")
		return
	}

	resp, err := authService.RefreshToken(c.Request.Context(), refreshToken)
	if err != nil {
		api.UnauthorizedResponse(c, "Session expired, please login again")
		return
	}

	maxAge := int(resp.ExpiresAt - time.Now().Unix())
	c.SetSameSite(http.SameSiteNoneMode)
	c.SetCookie("access_token", resp.Token, maxAge, "/", "", true, true)
	c.SetCookie("refresh_token", resp.RefreshToken, 60*60*24*7, "/", "", true, true)
	c.Set("userID", resp.UserID)

	c.Next()
}

func MustGetUserID(c *gin.Context) string {
	userID, exists := c.Get("userID")
	if !exists {
		api.UnauthorizedResponse(c)
		return ""
	}

	if userIDStr, ok := userID.(string); ok {
		return userIDStr
	}

	api.UnauthorizedResponse(c)
	return ""
}
