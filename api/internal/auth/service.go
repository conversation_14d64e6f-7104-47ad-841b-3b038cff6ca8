package auth

import (
	"context"
	"errors"

	"aura-api/internal/users"

	"github.com/google/uuid"
)

var (
	ErrInvalidCredentials  = errors.New("invalid credentials")
	ErrUserAlreadyExists   = errors.New("user with this email already exists")
	ErrInvalidRefreshToken = errors.New("invalid refresh token")
)

type AuthService interface {
	Register(ctx context.Context, req RegisterRequest) (*AuthResponse, error)
	Login(ctx context.Context, req LoginRequest) (*AuthResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*AuthResponse, error)
	ValidateToken(token string) (string, error)
}

type authService struct {
	userService  users.UserService
	tokenManager TokenManager
}

func NewAuthService(userService users.UserService, secretKey string) AuthService {
	return &authService{
		userService:  userService,
		tokenManager: NewTokenManager(secretKey, 60, 7),
	}
}

func (s *authService) Register(ctx context.Context, req RegisterRequest) (*AuthResponse, error) {
	if err := s.checkUserExists(ctx, req.Email); err != nil {
		return nil, err
	}

	hashedPassword, err := HashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	user := &users.User{
		ID:        uuid.New().String(),
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: "",
		LastName:  "",
	}

	if err := s.userService.Create(ctx, user); err != nil {
		return nil, err
	}

	return s.generateAuthResponse(ctx, user)
}

func (s *authService) checkUserExists(ctx context.Context, email string) error {
	_, err := s.userService.GetByEmail(ctx, email)
	if err == nil {
		return ErrUserAlreadyExists
	}

	if err == users.ErrUserNotFound {
		return nil
	}

	return err
}

func (s *authService) generateAuthResponse(ctx context.Context, user *users.User) (*AuthResponse, error) {
	tokenPair, err := s.tokenManager.GenerateTokenPair(user.ID, user.Email)
	if err != nil {
		return nil, err
	}

	if err := s.userService.UpdateTokens(ctx, user.ID, tokenPair.RefreshToken, tokenPair.ExpiresAt); err != nil {
		return nil, err
	}

	return &AuthResponse{
		Token:        tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt.Unix(),
		UserID:       user.ID,
		Email:        user.Email,
	}, nil
}

func (s *authService) Login(ctx context.Context, req LoginRequest) (*AuthResponse, error) {
	user, err := s.userService.GetByEmail(ctx, req.Email)
	if err == users.ErrUserNotFound {
		return nil, ErrInvalidCredentials
	}

	if err != nil {
		return nil, err
	}

	if err := CheckPassword(user.Password, req.Password); err != nil {
		return nil, ErrInvalidCredentials
	}

	return s.generateAuthResponse(ctx, user)
}

func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*AuthResponse, error) {
	userID, err := s.tokenManager.GetUserIDFromToken(refreshToken)
	if err != nil {
		return nil, ErrInvalidRefreshToken
	}

	user, err := s.userService.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	if err := s.validateRefreshToken(user, refreshToken); err != nil {
		return nil, err
	}

	return s.generateAuthResponse(ctx, user)
}

func (s *authService) validateRefreshToken(user *users.User, refreshToken string) error {
	if user.RefreshToken != refreshToken {
		return ErrInvalidRefreshToken
	}

	// Validate the refresh token itself using JWT validation
	// The TokenExpiry field in the database is for access tokens, not refresh tokens
	_, err := s.tokenManager.ValidateToken(refreshToken)
	if err != nil {
		return ErrExpiredToken
	}

	return nil
}

func (s *authService) ValidateToken(token string) (string, error) {
	return s.tokenManager.GetUserIDFromToken(token)
}
