package chat

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"aura-api/internal/auth"
	"aura-api/pkg/api"
	"aura-api/pkg/streaming"
)

type ChatHand<PERSON> interface {
	GetChats(c *gin.Context)
	GetChat(c *gin.Context)
	UpdateChat(c *gin.Context)
	DeleteChat(c *gin.Context)
	DeleteAllChats(c *gin.Context)
	ReceiveChatMessage(c *gin.Context)
}

type chatHandler struct {
	service       ChatService
	streamManager streaming.StreamManager
}

func NewChatHandler(service ChatService, streamManager streaming.StreamManager) ChatHandler {
	return &chatHandler{
		service:       service,
		streamManager: streamManager,
	}
}

func (h *chatHandler) GetChats(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	// Parse query parameter for including messages (default: false)
	includeMessages := c.<PERSON>fault<PERSON>("include_messages", "false") == "true"

	// Check if client wants SSE streaming
	acceptHeader := c.Get<PERSON>eader("Accept")
	if strings.Contains(acceptHeader, "text/event-stream") {
		h.GetStreamingChats(c, userID)
		return
	}

	chats, err := h.service.GetChatsByUserID(c.Request.Context(), userID, includeMessages)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusOK, chats, "Chats retrieved successfully")
}

// GetStreamingChats retrieves chats and streams them to the client
func (h *chatHandler) GetStreamingChats(c *gin.Context, userID string) {
	api.SetupSSEHeaders(c)

	eventChannel, connectionID := h.streamManager.AddConnection(userID, c)
	defer h.streamManager.RemoveConnection(userID, connectionID)

	if err := api.SendSSEEvent(c, string(streaming.EventTypeConnected), map[string]string{"connectionId": connectionID}); err != nil {
		return
	}

	h.streamChats(c, eventChannel)
}

func (h *chatHandler) GetChat(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	var params ChatRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.GetChat(c.Request.Context(), params.ChatID, userID)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusOK, resp, "Chat retrieved successfully")
}

func (h *chatHandler) UpdateChat(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	var params ChatRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	var req UpdateChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.UpdateChat(c.Request.Context(), params.ChatID, userID, req)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusOK, resp, "Chat updated successfully")
}

func (h *chatHandler) DeleteChat(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	var params ChatRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	err := h.service.DeleteChat(c.Request.Context(), params.ChatID, userID)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusNoContent, (*any)(nil), "Chat deleted successfully")
}

func (h *chatHandler) DeleteAllChats(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	err := h.service.DeleteAllChats(c.Request.Context(), userID)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusNoContent, (*any)(nil), "Chats deleted successfully")
}

func (h *chatHandler) ReceiveChatMessage(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	initNewChat := false

	var params ChatRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		initNewChat = true
	}

	var chatID *string = nil
	if !initNewChat {
		chatID = &params.ChatID
	}

	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	// Check if client wants SSE streaming
	acceptHeader := c.GetHeader("Accept")
	if strings.Contains(acceptHeader, "text/event-stream") {
		h.ReceiveStreamingChatMessage(c, chatID, userID, req)
		return
	}

	resp, err := h.service.ReceiveChatMessage(c.Request.Context(), chatID, userID, req)
	if err != nil {
		handleChatError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusOK, resp, "Message received successfully")
}

// ReceiveStreamingChatMessage creates a chat and streams the LLM response
func (h *chatHandler) ReceiveStreamingChatMessage(c *gin.Context, chatID *string, userID string, req ChatRequest) {
	api.SetupSSEHeaders(c)

	eventChannel, connectionID := h.streamManager.AddConnection(userID, c)
	defer h.streamManager.RemoveConnection(userID, connectionID)

	if err := api.SendSSEEvent(c, string(streaming.EventTypeConnected), map[string]string{"connectionId": connectionID}); err != nil {
		return
	}

	// Create the chat
	_, err := h.service.ReceiveChatMessage(c.Request.Context(), chatID, userID, req)
	if err != nil {
		api.SendSSEEvent(c, string(streaming.EventTypeError), map[string]string{"message": err.Error()})
		return
	}

	// Listen for streaming events and relay them to the client
	h.streamChatMessages(c, eventChannel)
}

// streamChatMessages listens for streaming events and relays them to the client
func (h *chatHandler) streamChatMessages(c *gin.Context, eventChannel <-chan streaming.Event) {
	messageEvents := []streaming.EventType{
		streaming.EventTypeMessageCreated,
		streaming.EventTypeMessageStreaming,
		streaming.EventTypeMessageCompleted,
		streaming.EventTypeMessageFailed,
	}

	terminalEvents := []streaming.EventType{
		streaming.EventTypeMessageCompleted,
		streaming.EventTypeMessageFailed,
	}

	h.streamEvents(c, eventChannel, messageEvents, terminalEvents)
}

// streamChats listens for chat-related events (create, update, delete)
func (h *chatHandler) streamChats(c *gin.Context, eventChannel <-chan streaming.Event) {
	chatEvents := []streaming.EventType{
		streaming.EventTypeChatCreated,
		streaming.EventTypeChatUpdated,
		streaming.EventTypeChatDeleted,
		streaming.EventTypeChatListUpdated,
	}

	// No terminal events for chat entities - stream until context is done
	h.streamEvents(c, eventChannel, chatEvents, nil)
}

// streamEvents provides a generic streaming implementation for any event types
func (h *chatHandler) streamEvents(c *gin.Context, eventChannel <-chan streaming.Event,
	allowedEvents, terminalEvents []streaming.EventType,
) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.Request.Context().Done():
			return
		case event := <-eventChannel:
			if h.shouldProcessEvent(event.Type, allowedEvents) {
				if err := api.SendSSEEventWithID(c, event.Type, event.ID, event.Data); err != nil {
					return
				}

				if h.isTerminalEvent(event.Type, terminalEvents) {
					return
				}
			}
		case <-ticker.C:
			if err := api.SendSSEHeartbeat(c); err != nil {
				return
			}
		}
	}
}

func (h *chatHandler) shouldProcessEvent(eventType string, allowedEvents []streaming.EventType) bool {
	for _, allowed := range allowedEvents {
		if string(allowed) == eventType {
			return true
		}
	}
	return false
}

func (h *chatHandler) isTerminalEvent(eventType string, terminalEvents []streaming.EventType) bool {
	for _, terminal := range terminalEvents {
		if string(terminal) == eventType {
			return true
		}
	}
	return false
}

func handleChatError(c *gin.Context, err error) {
	statusCode, message := getChatError(err)
	api.ErrorResponse(c, statusCode, message)
}

func getChatError(err error) (int, string) {
	switch err {
	case ErrChatNotFound:
		return http.StatusNotFound, "Chat not found"
	default:
		return http.StatusInternalServerError, err.Error()
	}
}
