package chat

import (
	"time"

	"gorm.io/gorm"

	"aura-api/internal/message"
)

// ChatMode represents the different chat modes
type ChatMode string

const (
	ModeDefault      ChatMode = "general"
	ModeDeepResearch ChatMode = "deep_research"
)

// Chat represents the chat entity
type Chat struct {
	ID        string            `json:"id" gorm:"primaryKey;type:uuid"`
	Title     string            `json:"title" gorm:"not null"`
	Mode      ChatMode          `json:"mode" gorm:"type:varchar(50);default:'general'"`
	UserID    string            `json:"userId" gorm:"type:uuid;not null;index"`
	Messages  []message.Message `json:"messages,omitempty" gorm:"foreignKey:ChatID;constraint:OnDelete:CASCADE"`
	CreatedAt time.Time         `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time         `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt    `json:"-" gorm:"index"`
}

type ChatRequestUriParams struct {
	ChatID string `uri:"id" binding:"required,uuid"`
}

// ChatRequest represents the request body for creating a chat
type ChatRequest struct {
	Title   string   `json:"title" binding:"omitempty,max=255"`
	Mode    ChatMode `json:"mode" binding:"omitempty,oneof=general deep_research"`
	Message string   `json:"message" binding:"required,min=10,max=10000"`
}

// UpdateChatRequest represents the request body for updating a chat
type UpdateChatRequest struct {
	Title string `json:"title" binding:"required,min=1,max=255"`
}

// ChatResponse represents the response for chat operations
type ChatResponse struct {
	ID       string        `json:"id"`
	Title    string        `json:"title"`
	Mode     ChatMode      `json:"mode"`
	UserID   string        `json:"userId"`
	Messages []ChatMessage `json:"messages,omitempty"`
}

// ChatMessage represents a message within a chat response
type ChatMessage struct {
	ID      string `json:"id"`
	Role    string `json:"role"`
	Content string `json:"content"`
	Status  string `json:"status"`
}

func (c *Chat) ToChatResponse(includeMessages bool) *ChatResponse {
	response := &ChatResponse{
		ID:     c.ID,
		Title:  c.Title,
		Mode:   c.Mode,
		UserID: c.UserID,
	}

	if includeMessages {
		chatMessages := make([]ChatMessage, len(c.Messages))
		for i, msg := range c.Messages {
			chatMessages[i] = ChatMessage{
				ID:      msg.ID,
				Role:    string(msg.Role),
				Content: msg.Content,
				Status:  string(msg.Status),
			}
		}
		response.Messages = chatMessages
	}

	return response
}
