package chat

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"aura-api/internal/auth"
	"aura-api/internal/config"
	"aura-api/internal/llm"
	"aura-api/internal/message"
	"aura-api/internal/users"
	"aura-api/pkg/streaming"
)

const basePath = "chat"

// SetupRoutes registers chat-related routes
func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	cfg := config.LoadAppConfig()

	userRepo := users.NewUserRepository(db)
	userService := users.NewUserService(userRepo)
	authService := auth.NewAuthService(userService, cfg.JWTSecret)

	streamManager := streaming.NewStreamManager()

	llmService := llm.NewLLMService(config.LoadLLMConfig())

	chatRepo := NewChatRepository(db)
	messageRepo := message.NewMessageRepository(db)

	messageService := message.NewMessageService(messageRepo, streamManager, llmService)
	chatService := NewChatService(chatRepo, messageService, streamManager)
	handler := NewChatHandler(chatService, streamManager)

	authMiddleware := auth.AuthMiddleware(authService)

	chatGroup := router.Group(basePath)
	chatGroup.Use(authMiddleware)
	{
		chatGroup.GET("", handler.GetChats)
		chatGroup.GET("/:id", handler.GetChat)
		chatGroup.PUT("/:id", handler.UpdateChat)
		chatGroup.DELETE("", handler.DeleteAllChats)
		chatGroup.DELETE("/:id", handler.DeleteChat)
		chatGroup.POST("", handler.ReceiveChatMessage)
		chatGroup.POST("/:id", handler.ReceiveChatMessage)
	}
}
