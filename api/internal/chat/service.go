package chat

import (
	"context"
	"fmt"
	"strings"

	"aura-api/internal/message"
	"aura-api/pkg/streaming"
)

type ChatService interface {
	GetChatsByUserID(ctx context.Context, userID string, includeMessages bool) ([]ChatResponse, error)
	GetChat(ctx context.Context, id, userID string) (*ChatResponse, error)
	UpdateChat(ctx context.Context, id, userID string, req UpdateChatRequest) (*ChatResponse, error)
	DeleteChat(ctx context.Context, id, userID string) error
	DeleteAllChats(ctx context.Context, userID string) error
	ReceiveChatMessage(ctx context.Context, id *string, userID string, req ChatRequest) (*ChatResponse, error)
	ProcessMessage(ctx context.Context, chat *Chat, message string) (*ChatResponse, error)
	PublishChatListUpdate(ctx context.Context, userID string) error
}

type chatService struct {
	repo           ChatRepository
	messageService message.MessageService
	streamManager  streaming.StreamManager
}

func NewChatService(repo ChatRepository, messageService message.MessageService, streamManager streaming.StreamManager) ChatService {
	return &chatService{
		repo:           repo,
		messageService: messageService,
		streamManager:  streamManager,
	}
}

func (s *chatService) GetChatsByUserID(ctx context.Context, userID string, includeMessages bool) ([]ChatResponse, error) {
	chats, err := s.repo.GetByUserID(ctx, userID, includeMessages)
	if err != nil {
		return nil, err
	}

	chatResponses := make([]ChatResponse, len(chats))
	for i, chat := range chats {
		chatResponses[i] = *chat.ToChatResponse(includeMessages)
	}

	return chatResponses, nil
}

func (s *chatService) GetChat(ctx context.Context, id, userID string) (*ChatResponse, error) {
	chat, err := s.repo.GetByID(ctx, id, userID)
	if err != nil {
		return nil, err
	}

	return chat.ToChatResponse(true), nil
}

func (s *chatService) UpdateChat(ctx context.Context, id, userID string, req UpdateChatRequest) (*ChatResponse, error) {
	chat, err := s.repo.GetByID(ctx, id, userID)
	if err != nil {
		return nil, err
	}

	if req.Title != "" {
		chat.Title = req.Title
	}

	if err := s.repo.Update(ctx, chat); err != nil {
		return nil, err
	}

	response := chat.ToChatResponse(false)

	if err := s.PublishChatListUpdate(ctx, userID); err != nil {
		// Log error but don't fail the operation
		// TODO: Add proper logging
	}

	return response, nil
}

func (s *chatService) DeleteChat(ctx context.Context, id, userID string) error {
	if err := s.repo.Delete(ctx, id, userID); err != nil {
		return err
	}

	if err := s.PublishChatListUpdate(ctx, userID); err != nil {
		// Log error but don't fail the operation
		// TODO: Add proper logging
	}

	return nil
}

func (s *chatService) DeleteAllChats(ctx context.Context, userID string) error {
	if err := s.repo.DeleteAll(ctx, userID); err != nil {
		return fmt.Errorf("failed to delete all chats for user %s: %w", userID, err)
	}

	if err := s.PublishChatListUpdate(ctx, userID); err != nil {
		// Log error but don't fail the operation
		// TODO: Add proper logging
	}

	return nil
}

func (s *chatService) ReceiveChatMessage(ctx context.Context, id *string, userID string, req ChatRequest) (*ChatResponse, error) {
	// Create a new chat if chatID is nil, otherwise continue existing chat
	var chat *Chat
	var err error

	if id != nil {
		chat, err = s.repo.GetByID(ctx, *id, userID)
	}

	if err != nil && id != nil {
		return nil, fmt.Errorf("failed to get chat by ID %s: %w", *id, err)
	}

	if chat == nil {
		chat = &Chat{
			UserID: userID,
			Mode:   ModeDefault,
			Title:  s.generateTitleFromMessage(req.Message),
		}

		if req.Title != "" {
			chat.Title = req.Title
		}

		if req.Mode != "" {
			chat.Mode = req.Mode
		}
	}

	var createErr error
	if id == nil && s.repo.Create(ctx, chat) != nil {
		createErr = s.repo.Create(ctx, chat)
	}

	if createErr != nil {
		return nil, fmt.Errorf("failed to create chat: %w", createErr)
	}

	if err := s.PublishChatListUpdate(ctx, userID); err != nil {
		// Log error but don't fail the operation
		// TODO: Add proper logging
	}

	response, err := s.ProcessMessage(ctx, chat, req.Message)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *chatService) ProcessMessage(ctx context.Context, chat *Chat, messageContent string) (*ChatResponse, error) {
	// Create the user message
	userMessage, err := s.messageService.CreateMessage(ctx, message.CreateMessageRequest{
		Role:    message.RoleUser,
		ChatID:  chat.ID,
		UserID:  chat.UserID,
		Content: messageContent,
		Status:  message.StatusCompleted,
	})
	if err != nil {
		return nil, err
	}

	if err := s.messageService.ProcessMessageWithStreaming(ctx, userMessage, string(chat.Mode)); err != nil {
		return nil, err
	}

	return chat.ToChatResponse(true), nil
}

func (s *chatService) generateTitleFromMessage(message string) string {
	// Remove extra whitespace and limit to first 50 characters
	title := strings.TrimSpace(message)
	if len(title) > 50 {
		title = title[:47] + "..."
	}
	return title
}

func (s *chatService) PublishChatListUpdate(ctx context.Context, userID string) error {
	chats, err := s.GetChatsByUserID(ctx, userID, false)
	if err != nil {
		return err
	}

	s.streamManager.PublishEvent(streaming.EventTypeChatListUpdated, userID, "chat-list", chats)
	return nil
}
