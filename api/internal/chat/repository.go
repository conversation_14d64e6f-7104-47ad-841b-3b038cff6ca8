package chat

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var ErrChatNotFound = errors.New("chat not found")

type ChatRepository interface {
	Create(ctx context.Context, chat *Chat) error
	GetByID(ctx context.Context, id, userID string) (*Chat, error)
	GetByUserID(ctx context.Context, userID string, includeMessages bool) ([]Chat, error)
	Update(ctx context.Context, chat *Chat) error
	Delete(ctx context.Context, id, userID string) error
	DeleteAll(ctx context.Context, userID string) error
}

type chatRepository struct {
	db *gorm.DB
}

func NewChatRepository(db *gorm.DB) ChatRepository {
	return &chatRepository{db: db}
}

func (r *chatRepository) Create(ctx context.Context, chat *Chat) error {
	chat.ID = uuid.New().String()
	return r.db.WithContext(ctx).Create(chat).Error
}

func (r *chatRepository) GetByID(ctx context.Context, id, userID string) (*Chat, error) {
	var chat Chat
	err := r.db.WithContext(ctx).
		Preload("Messages", func(db *gorm.DB) *gorm.DB {
			return db.Order("messages.created_at ASC")
		}).
		First(&chat, "id = ? AND user_id = ?", id, userID).
		Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrChatNotFound
	}

	if err != nil {
		return nil, err
	}

	return &chat, nil
}

func (r *chatRepository) GetByUserID(ctx context.Context, userID string, includeMessages bool) ([]Chat, error) {
	var chats []Chat

	query := r.db.WithContext(ctx)

	if includeMessages {
		query = query.Preload("Messages", func(db *gorm.DB) *gorm.DB {
			return db.Order("messages.created_at ASC")
		})
	}

	err := query.
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Find(&chats).
		Error
	if err != nil {
		return nil, err
	}

	return chats, nil
}

func (r *chatRepository) Update(ctx context.Context, chat *Chat) error {
	result := r.db.WithContext(ctx).Save(chat)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return ErrChatNotFound
	}

	return nil
}

func (r *chatRepository) Delete(ctx context.Context, id, userID string) error {
	result := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).Delete(&Chat{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return ErrChatNotFound
	}

	return nil
}

func (r *chatRepository) DeleteAll(ctx context.Context, userID string) error {
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&Chat{})
	if result.Error != nil {
		return result.Error
	}

	return nil
}
