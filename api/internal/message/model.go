package message

import (
	"time"

	"aura-api/internal/llm"

	"gorm.io/gorm"
)

// MessageRole represents the role of who sent the message
type MessageRole string

const (
	RoleUser      MessageRole = "user"
	RoleAssistant MessageRole = "assistant"
	RoleSystem    MessageRole = "system"
)

// MessageStatus represents the status of a message
type MessageStatus string

const (
	StatusPending   MessageStatus = "pending"
	StatusStreaming MessageStatus = "streaming"
	StatusCompleted MessageStatus = "completed"
	StatusFailed    MessageStatus = "failed"
)

// Message represents a message in a chat
type Message struct {
	ID        string         `json:"id" gorm:"primaryKey;type:uuid"`
	ChatID    string         `json:"chatId" gorm:"type:uuid;not null;index"`
	Role      MessageRole    `json:"role" gorm:"type:varchar(20);not null"`
	Content   string         `json:"content" gorm:"type:text"`
	Status    MessageStatus  `json:"status" gorm:"type:varchar(20);default:'completed'"`
	UserID    string         `json:"userId" gorm:"type:uuid;not null"`
	CreatedAt time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

type CreateMessageRequest struct {
	Role    MessageRole   `json:"role" binding:"required,oneof=user assistant system"`
	ChatID  string        `json:"chatId" binding:"required,uuid"`
	UserID  string        `json:"userId" binding:"required,uuid"`
	Content string        `json:"content" binding:"required,min=1,max=10000"`
	Status  MessageStatus `json:"status" binding:"required,oneof=pending streaming completed failed"`
}

// MessageResponse represents the response for message operations
type MessageResponse struct {
	ID        string        `json:"id"`
	ChatID    string        `json:"chatId"`
	Role      MessageRole   `json:"role"`
	Content   string        `json:"content"`
	Status    MessageStatus `json:"status"`
	UserID    string        `json:"userId"`
	CreatedAt time.Time     `json:"createdAt"`
}

// StreamingResponse represents a chunk of streaming response
type StreamingResponse struct {
	MessageID string `json:"messageId"`
	Content   string `json:"content"`
	Delta     string `json:"delta,omitempty"`
	Done      bool   `json:"done"`
}

func (m *Message) ToMessageResponse() *MessageResponse {
	return &MessageResponse{
		ID:        m.ID,
		ChatID:    m.ChatID,
		Role:      m.Role,
		Content:   m.Content,
		Status:    m.Status,
		UserID:    m.UserID,
		CreatedAt: m.CreatedAt,
	}
}

func (m *Message) ToLLMMessage() *llm.LLMMessage {
	return &llm.LLMMessage{
		Role:    string(m.Role),
		Content: m.Content,
	}
}
