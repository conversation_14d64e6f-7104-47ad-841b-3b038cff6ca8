package message

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var ErrMessageNotFound = errors.New("message not found")

type MessageRepository interface {
	Create(ctx context.Context, message *Message) error
	Update(ctx context.Context, message *Message) error
	GetChatHistoryForContext(ctx context.Context, chatID, userID string) ([]Message, error)
}

type messageRepository struct {
	db *gorm.DB
}

func NewMessageRepository(db *gorm.DB) MessageRepository {
	return &messageRepository{db: db}
}

func (r *messageRepository) Create(ctx context.Context, message *Message) error {
	message.ID = uuid.New().String()
	return r.db.WithContext(ctx).Create(message).Error
}

func (r *messageRepository) Update(ctx context.Context, message *Message) error {
	result := r.db.WithContext(ctx).Save(message)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return ErrMessageNotFound
	}

	return nil
}

func (r *messageRepository) GetChatHistoryForContext(ctx context.Context, chatID, userID string) ([]Message, error) {
	var messages []Message

	err := r.db.WithContext(ctx).
		Where("chat_id = ? AND user_id = ? AND status = ?", chatID, userID, StatusCompleted).
		Order("created_at ASC").
		Limit(20). // Limit to last 20 messages for context
		Find(&messages).Error
	if err != nil {
		return nil, err
	}

	if len(messages) == 0 {
		return messages, nil
	}

	// Check if the latest message is from assistant and remove it if so
	latestMessage := messages[len(messages)-1]
	if latestMessage.Role == RoleAssistant {
		return messages[:len(messages)-1], nil
	}

	return messages, nil
}
