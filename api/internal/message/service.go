package message

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"aura-api/internal/llm"
	"aura-api/pkg/connector"
	"aura-api/pkg/streaming"
)

type MessageService interface {
	CreateMessage(ctx context.Context, req CreateMessageRequest) (*Message, error)
	ProcessMessageWithStreaming(ctx context.Context, userMessage *Message, chatMode string) error
	ProcessLLMResponse(ctx context.Context, assistantMessage *Message, userContent string, chatMode string)
}

type messageService struct {
	repo          MessageRepository
	streamManager streaming.StreamManager
	llmService    llm.LLMService
}

func NewMessageService(repo MessageRepository, streamManager streaming.StreamManager, llmService llm.LLMService) MessageService {
	return &messageService{
		repo:          repo,
		streamManager: streamManager,
		llmService:    llmService,
	}
}

func (s *messageService) CreateMessage(ctx context.Context, req CreateMessageRequest) (*Message, error) {
	message := &Message{
		ChatID:  req.ChatID,
		Role:    req.Role,
		Content: req.Content,
		Status:  req.Status,
		UserID:  req.UserID,
	}

	if err := s.repo.Create(ctx, message); err != nil {
		return nil, err
	}

	response := message.ToMessageResponse()

	// Publish message created event
	s.streamManager.PublishEvent(streaming.EventTypeMessageCreated, message.UserID, message.ID, response)

	return message, nil
}

func (s *messageService) ProcessMessageWithStreaming(ctx context.Context, userMessage *Message, chatMode string) error {
	// Create assistant message with pending status
	responseMsg := &Message{
		ChatID:  userMessage.ChatID,
		Role:    RoleAssistant,
		Content: "",
		Status:  StatusPending,
		UserID:  userMessage.UserID,
	}

	if err := s.repo.Create(ctx, responseMsg); err != nil {
		return fmt.Errorf("failed to create assistant message: %w", err)
	}

	// Publish message created event for assistant message
	s.streamManager.PublishEvent(streaming.EventTypeMessageCreated, responseMsg.UserID, responseMsg.ID, responseMsg.ToMessageResponse())

	// Start real LLM streaming response in a goroutine
	go s.ProcessLLMResponse(ctx, responseMsg, userMessage.Content, chatMode)

	return nil
}

func (s *messageService) ProcessLLMResponse(ctx context.Context, assistantMessage *Message, userContent string, chatMode string) {
	// Use background context to prevent cancellation when HTTP request completes
	bgCtx := context.Background()

	// Update status to streaming
	assistantMessage.Status = StatusStreaming
	if err := s.repo.Update(bgCtx, assistantMessage); err != nil {
		s.handleStreamingError(bgCtx, assistantMessage, fmt.Errorf("failed to update message status: %w", err))
		return
	}

	// Publish streaming started event
	s.streamManager.PublishEvent(streaming.EventTypeMessageStreaming, assistantMessage.UserID, assistantMessage.ID, &StreamingResponse{
		MessageID: assistantMessage.ID,
		Content:   "",
		Done:      false,
	})

	// Decide whether to submit simple or advanced question based on chat mode
	var response *llm.QuestionResponse
	var err error

	if chatMode == "deep_research" {
		// Submit advanced question for deep research mode
		questionReq := llm.AdvancedQuestionRequest{
			Question:     userContent,
			SessionID:    assistantMessage.ChatID,
			Config:       llm.DefaultAdvancedConfig(),
			WorkflowType: llm.WorkflowTypeEnhanced,
		}
		response, err = s.llmService.SubmitAdvancedQuestion(bgCtx, questionReq)
	} else {
		// Submit simple question for general mode (default)
		questionReq := llm.SimpleQuestionRequest{
			Question:  userContent,
			SessionID: assistantMessage.ChatID,
		}
		response, err = s.llmService.SubmitSimpleQuestion(bgCtx, questionReq)
	}

	if err != nil {
		s.handleStreamingError(bgCtx, assistantMessage, fmt.Errorf("failed to submit question: %w", err))
		return
	}

	if !response.IsSuccess() {
		s.handleStreamingError(bgCtx, assistantMessage, fmt.Errorf("question submission failed: %s", response.Message))
		return
	}

	// Monitor workflow status for streaming updates
	s.monitorWorkflowWithStreaming(bgCtx, assistantMessage, response.WorkflowID)
}

func (s *messageService) monitorWorkflowWithStreaming(ctx context.Context, assistantMessage *Message, workflowID string) {
	wsConn, err := s.llmService.ConnectWorkflowWebSocket(ctx, workflowID)
	if err != nil {
		// Fall back to polling if WebSocket connection fails
		s.monitorWorkflowWithPolling(ctx, assistantMessage, workflowID)
		return
	}
	defer wsConn.Close()

	var lastContent string

	for {
		select {
		case <-ctx.Done():
			return
		default:
			shouldContinue, err := s.processWebSocketMessage(ctx, wsConn, assistantMessage, &lastContent)
			if err != nil {
				s.handleStreamingError(ctx, assistantMessage, err)
				return
			}
			if !shouldContinue {
				return // Workflow completed successfully
			}
		}
	}
}

func (s *messageService) processWebSocketMessage(ctx context.Context, wsConn connector.WebSocketConnection, assistantMessage *Message, lastContent *string) (shouldContinue bool, err error) {
	var wsMessage llm.WebSocketMessage
	if err := wsConn.ReadJSON(ctx, &wsMessage); err != nil {
		return false, fmt.Errorf("failed to read WebSocket message: %w", err)
	}

	switch wsMessage.Type {
	case llm.WSMessageTypeStatusUpdate, llm.WSMessageTypeAgentUpdate:
		return true, nil

	case llm.WSMessageTypeFinalResult:
		if err := s.handleFinalResultMessage(wsMessage, assistantMessage, lastContent); err != nil {
			return false, err
		}
		return true, nil

	case llm.WSMessageTypeWorkflowComplete:
		if err := s.handleWorkflowComplete(ctx, wsMessage, assistantMessage); err != nil {
			return false, err
		}
		return false, nil // Workflow completed successfully, stop processing

	case llm.WSMessageTypeError:
		return false, s.handleWorkflowError(wsMessage)

	default:
		return true, nil
	}
}

func (s *messageService) handleFinalResultMessage(wsMessage llm.WebSocketMessage, assistantMessage *Message, lastContent *string) error {
	var data llm.FinalResultData
	dataBytes, err := json.Marshal(wsMessage.Data)
	if err != nil {
		return nil
	}

	if err := json.Unmarshal(dataBytes, &data); err != nil {
		return nil
	}

	if data.Chunk == "" {
		return nil
	}

	assistantMessage.Content += data.Chunk
	delta := assistantMessage.Content[len(*lastContent):]
	*lastContent = assistantMessage.Content

	if delta != "" {
		s.streamManager.PublishEvent(streaming.EventTypeMessageStreaming, assistantMessage.UserID, assistantMessage.ID, &StreamingResponse{
			MessageID: assistantMessage.ID,
			Content:   assistantMessage.Content,
			Delta:     delta,
			Done:      false,
		})
	}

	return nil
}

func (s *messageService) handleWorkflowComplete(ctx context.Context, wsMessage llm.WebSocketMessage, assistantMessage *Message) error {
	var data llm.WorkflowCompleteData
	dataBytes, err := json.Marshal(wsMessage.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal workflow complete data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &data); err != nil {
		return fmt.Errorf("failed to unmarshal workflow complete data: %w", err)
	}

	if data.FinalAnswer != "" {
		assistantMessage.Content = data.FinalAnswer
	}

	if data.Status == "completed" {
		s.completeStreamingResponse(ctx, assistantMessage)
	}

	return nil
}

func (s *messageService) handleWorkflowError(wsMessage llm.WebSocketMessage) error {
	var data llm.ErrorData
	dataBytes, err := json.Marshal(wsMessage.Data)
	if err != nil {
		return fmt.Errorf("workflow failed with unknown error")
	}

	if err := json.Unmarshal(dataBytes, &data); err != nil {
		return fmt.Errorf("workflow failed with unknown error")
	}

	if data.Message != "" {
		return fmt.Errorf("workflow error: %s", data.Message)
	}

	return fmt.Errorf("workflow failed with unknown error")
}

func (s *messageService) completeStreamingResponse(ctx context.Context, assistantMessage *Message) {
	// Mark as completed
	assistantMessage.Status = StatusCompleted
	err := s.repo.Update(ctx, assistantMessage)
	if err != nil {
		fmt.Printf("Failed to update message status: %v\n", err)
	}

	// Publish completion event
	s.streamManager.PublishEvent(streaming.EventTypeMessageCompleted, assistantMessage.UserID, assistantMessage.ID, &StreamingResponse{
		MessageID: assistantMessage.ID,
		Content:   assistantMessage.Content,
		Done:      true,
	})

	// Update message in stream
	finalResponse := assistantMessage.ToMessageResponse()
	s.streamManager.PublishEvent(streaming.EventTypeMessageUpdated, assistantMessage.UserID, assistantMessage.ID, finalResponse)
}

func (s *messageService) handleStreamingError(ctx context.Context, assistantMessage *Message, err error) {
	fmt.Printf("Error processing message %s: %v\n", assistantMessage.ID, err)

	// Mark as failed
	assistantMessage.Status = StatusFailed
	assistantMessage.Content = fmt.Sprintf("I apologize, but I encountered an error while processing your request. Please try again. %v", err)

	err = s.repo.Update(ctx, assistantMessage)
	if err != nil {
		fmt.Printf("Failed to update message status: %v\n", err)
	}

	// Publish error event
	s.streamManager.PublishEvent(streaming.EventTypeMessageFailed, assistantMessage.UserID, assistantMessage.ID, &StreamingResponse{
		MessageID: assistantMessage.ID,
		Content:   assistantMessage.Content,
		Done:      true,
	})

	// Update message in stream
	finalResponse := assistantMessage.ToMessageResponse()
	s.streamManager.PublishEvent(streaming.EventTypeMessageUpdated, assistantMessage.UserID, assistantMessage.ID, finalResponse)
}

func (s *messageService) monitorWorkflowWithPolling(ctx context.Context, assistantMessage *Message, workflowID string) {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	var lastContent string

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			shouldContinue, err := s.pollWorkflowStatus(ctx, assistantMessage, workflowID, &lastContent)
			if err != nil {
				s.handleStreamingError(ctx, assistantMessage, err)
				return
			}
			if !shouldContinue {
				return // Workflow completed successfully
			}
		}
	}
}

func (s *messageService) pollWorkflowStatus(ctx context.Context, assistantMessage *Message, workflowID string, lastContent *string) (shouldContinue bool, err error) {
	status, err := s.llmService.GetWorkflowStatus(ctx, workflowID)
	if err != nil {
		return false, fmt.Errorf("failed to get workflow status: %w", err)
	}

	// Process based on workflow status
	switch status.Status {
	case llm.WorkflowStatusCompleted:
		if status.FinalAnswer != "" {
			assistantMessage.Content = status.FinalAnswer
		}
		s.completeStreamingResponse(ctx, assistantMessage)
		return false, nil // Workflow completed successfully

	case llm.WorkflowStatusFailed:
		return false, fmt.Errorf("workflow failed")

	case llm.WorkflowStatusCancelled:
		return false, fmt.Errorf("workflow was cancelled")

	case llm.WorkflowStatusExecuting:
		// Check if there's new content to stream
		if status.FinalAnswer != "" && status.FinalAnswer != *lastContent {
			delta := status.FinalAnswer[len(*lastContent):]
			assistantMessage.Content = status.FinalAnswer
			*lastContent = status.FinalAnswer

			if delta != "" {
				s.streamManager.PublishEvent(streaming.EventTypeMessageStreaming, assistantMessage.UserID, assistantMessage.ID, &StreamingResponse{
					MessageID: assistantMessage.ID,
					Content:   assistantMessage.Content,
					Delta:     delta,
					Done:      false,
				})
			}
		}
		return true, nil // Continue polling

	default:
		return true, nil // Continue polling for other statuses
	}
}
