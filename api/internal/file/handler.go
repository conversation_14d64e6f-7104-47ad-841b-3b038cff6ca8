package file

import (
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"

	"aura-api/internal/auth"
	"aura-api/pkg/api"
)

type FileHandler interface {
	CreateFile(c *gin.Context)
	GetFile(c *gin.Context)
	DeleteFile(c *gin.Context)
}

type fileHandler struct {
	service FileService
}

func NewFileHandler(service FileService) FileHandler {
	return &fileHandler{
		service: service,
	}
}

// CreateFile handles multipart file upload
// Expected form fields:
//   - file: the file to upload (required)
//   - chatId: UUID of the chat to associate the file with (required)
//
// Supported file types: .pdf, .doc, .docx, .txt, .md, .json, .csv, .xlsx, .xls
// Max file size: 100MB
func (h *fileHandler) CreateFile(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32 MB max
		api.ErrorResponse(c, http.StatusBadRequest, "Failed to parse multipart form")
		return
	}

	// Get the file from form
	var file multipart.File
	var header *multipart.FileHeader
	var err error

	file, header, err = c.Request.FormFile("file")
	if err != nil {
		api.ErrorResponse(c, http.StatusBadRequest, "File is required")
		return
	}
	defer file.Close()

	// Get chat ID from form
	chatID := c.Request.FormValue("chatId")
	if chatID == "" {
		api.ErrorResponse(c, http.StatusBadRequest, "chatId is required")
		return
	}

	// Validate file size (optional - adjust as needed)
	if header.Size > 100<<20 { // 100 MB max
		api.ErrorResponse(c, http.StatusBadRequest, "File size too large (max 100MB)")
		return
	}

	// Validate filename security
	if strings.Contains(header.Filename, "..") || strings.Contains(header.Filename, "/") || strings.Contains(header.Filename, "\\") {
		api.ErrorResponse(c, http.StatusBadRequest, "Invalid filename")
		return
	}

	// Validate file type (optional - adjust as needed)
	allowedTypes := map[string]bool{
		".pdf":  true,
		".doc":  true,
		".docx": true,
		".txt":  true,
		".md":   true,
		".json": true,
		".csv":  true,
		".xlsx": true,
		".xls":  true,
		".png":  true,
		".jpg":  true,
		".jpeg": true,
		".gif":  true,
	}

	ext := filepath.Ext(header.Filename)
	if ext == "" || !allowedTypes[ext] {
		api.ErrorResponse(c, http.StatusBadRequest, "File type not allowed. Supported: .pdf, .doc, .docx, .txt, .md, .json, .csv, .xlsx, .xls, .png, .jpg, .jpeg, .gif")
		return
	}

	// Create request with file data
	req := CreateFileRequest{
		ChatID:   chatID,
		FileName: header.Filename,
	}

	resp, err := h.service.CreateFile(c.Request.Context(), req, userID, file, header)
	if err != nil {
		handleFileError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusCreated, resp, "File uploaded successfully")
}

func (h *fileHandler) GetFile(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	var params FileRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	resp, err := h.service.GetFile(c.Request.Context(), params.FileID, userID)
	if err != nil {
		handleFileError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusOK, resp, "File retrieved successfully")
}

func (h *fileHandler) DeleteFile(c *gin.Context) {
	userID := auth.MustGetUserID(c)

	var params FileRequestUriParams
	if err := c.ShouldBindUri(&params); err != nil {
		api.ValidationErrorResponse(c, err)
		return
	}

	err := h.service.DeleteFile(c.Request.Context(), params.FileID, userID)
	if err != nil {
		handleFileError(c, err)
		return
	}

	api.SuccessResponse(c, http.StatusNoContent, (*any)(nil), "File deleted successfully")
}

func handleFileError(c *gin.Context, err error) {
	statusCode, message := getFileError(err)
	api.ErrorResponse(c, statusCode, message)
}

func getFileError(err error) (int, string) {
	switch err {
	case ErrFileNotFound:
		return http.StatusNotFound, "File not found"
	default:
		return http.StatusInternalServerError, err.Error()
	}
}
