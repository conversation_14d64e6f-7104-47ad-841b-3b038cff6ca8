package file

import (
	"time"

	"gorm.io/gorm"
)

// File represents a file entity associated with a chat
type File struct {
	ID        string         `json:"id" gorm:"primaryKey;type:uuid"`
	ChatID    string         `json:"chatId" gorm:"type:uuid;not null;index"`
	FileId    string         `json:"fileId" gorm:"not null;uniqueIndex"`
	FileName  string         `json:"fileName" gorm:"not null"`
	CreatedAt time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// CreateFileRequest represents the request body for creating a file
type CreateFileRequest struct {
	ChatID   string `json:"chatId" binding:"required,uuid"`
	FileName string `json:"fileName" binding:"required,min=1,max=255"`
}

// FileResponse represents the response for file operations
type FileResponse struct {
	ID       string `json:"id"`
	ChatID   string `json:"chatId"`
	FileId   string `json:"fileId"`
	FileName string `json:"fileName"`
}

// FileRequestUriParams represents URI parameters for file operations
type FileRequestUriParams struct {
	FileID string `uri:"id" binding:"required,uuid"`
}

// ChatFileRequestUriParams represents URI parameters for chat file operations
type ChatFileRequestUriParams struct {
	ChatID string `uri:"chatId" binding:"required,uuid"`
}

// ToFileResponse converts a File entity to FileResponse
func (f *File) ToFileResponse() *FileResponse {
	return &FileResponse{
		ID:       f.ID,
		ChatID:   f.ChatID,
		FileId:   f.FileId,
		FileName: f.FileName,
	}
}
