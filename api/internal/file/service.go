package file

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"

	"aura-api/internal/llm"
)

type FileService interface {
	CreateFile(ctx context.Context, req CreateFileRequest, userID string, file multipart.File, header *multipart.FileHeader) (*FileResponse, error)
	GetFile(ctx context.Context, id, userID string) (*FileResponse, error)
	GetFilesByChatID(ctx context.Context, chatID, userID string) ([]FileResponse, error)
	DeleteFile(ctx context.Context, id, userID string) error
}

type fileService struct {
	repo       FileRepository
	llmService llm.LLMService
}

func NewFileService(repo FileRepository, llmService llm.LLMService) FileService {
	return &fileService{
		repo:       repo,
		llmService: llmService,
	}
}

func (s *fileService) CreateFile(ctx context.Context, req CreateFileRequest, userID string, file multipart.File, header *multipart.FileHeader) (*FileResponse, error) {
	// Read the file content
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	// Upload file to LLM provider using the connector
	uploadRequest := llm.FileUploadRequest{
		FileData:  fileContent,
		FileName:  header.Filename,
		SessionID: req.ChatID,
	}
	uploadResponse, err := s.llmService.UploadFile(ctx, uploadRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file to LLM provider: %w", err)
	}

	// Create file entity with the LLM provider's file ID
	fileEntity := &File{
		ChatID:   req.ChatID,
		FileId:   uploadResponse.FileID, // Use the file ID returned by LLM provider
		FileName: req.FileName,
	}

	if err := s.repo.Create(ctx, fileEntity); err != nil {
		return nil, fmt.Errorf("failed to save file metadata: %w", err)
	}

	return fileEntity.ToFileResponse(), nil
}

func (s *fileService) GetFile(ctx context.Context, id, userID string) (*FileResponse, error) {
	file, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return file.ToFileResponse(), nil
}

func (s *fileService) GetFilesByChatID(ctx context.Context, chatID, userID string) ([]FileResponse, error) {
	files, err := s.repo.GetByChatID(ctx, chatID)
	if err != nil {
		return nil, err
	}

	fileResponses := make([]FileResponse, len(files))
	for i, file := range files {
		fileResponses[i] = *file.ToFileResponse()
	}

	return fileResponses, nil
}

func (s *fileService) DeleteFile(ctx context.Context, id, userID string) error {
	return s.repo.Delete(ctx, id)
}
