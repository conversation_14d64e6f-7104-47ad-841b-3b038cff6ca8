package file

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var ErrFileNotFound = errors.New("file not found")

type FileRepository interface {
	Create(ctx context.Context, file *File) error
	GetByID(ctx context.Context, id string) (*File, error)
	GetByChatID(ctx context.Context, chatID string) ([]File, error)
	Delete(ctx context.Context, id string) error
}

type fileRepository struct {
	db *gorm.DB
}

func NewFileRepository(db *gorm.DB) FileRepository {
	return &fileRepository{db: db}
}

func (r *fileRepository) Create(ctx context.Context, file *File) error {
	file.ID = uuid.New().String()
	return r.db.WithContext(ctx).Create(file).Error
}

func (r *fileRepository) GetByID(ctx context.Context, id string) (*File, error) {
	var file File
	err := r.db.WithContext(ctx).First(&file, "id = ?", id).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrFileNotFound
	}

	if err != nil {
		return nil, err
	}

	return &file, nil
}

func (r *fileRepository) GetByChatID(ctx context.Context, chatID string) ([]File, error) {
	var files []File

	err := r.db.WithContext(ctx).Where("chat_id = ?", chatID).Order("created_at ASC").Find(&files).Error
	if err != nil {
		return nil, err
	}

	return files, nil
}

func (r *fileRepository) Delete(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Where("id = ?", id).Delete(&File{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return ErrFileNotFound
	}

	return nil
}
