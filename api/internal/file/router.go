package file

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"aura-api/internal/auth"
	"aura-api/internal/config"
	"aura-api/internal/llm"
	"aura-api/internal/users"
)

const basePath = "files"

// SetupRoutes registers file-related routes
func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	cfg := config.LoadAppConfig()

	userRepo := users.NewUserRepository(db)
	userService := users.NewUserService(userRepo)
	authService := auth.NewAuthService(userService, cfg.JWTSecret)

	// Create LLM service for file uploads
	llmConfig := config.LoadLLMConfig()
	llmService := llm.NewLLMService(llmConfig)

	fileRepo := NewFileRepository(db)
	fileService := NewFileService(fileRepo, llmService)
	handler := NewFileHandler(fileService)

	authMiddleware := auth.AuthMiddleware(authService)

	fileGroup := router.Group(basePath)
	fileGroup.Use(authMiddleware)
	{
		fileGroup.POST("", handler.CreateFile)
		fileGroup.GET("/:id", handler.GetFile)
		fileGroup.DELETE("/:id", handler.DeleteFile)
	}
}
