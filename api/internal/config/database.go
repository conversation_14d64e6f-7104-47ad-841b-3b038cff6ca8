package config

import "aura-api/pkg/utils"

type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Db       string
}

func LoadDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Host:     utils.GetEnv("POSTGRES_HOST", "postgres"),
		Port:     utils.GetEnvAsInt("POSTGRES_PORT", 5432),
		User:     utils.GetEnv("POSTGRES_USER", "admin"),
		Password: utils.GetEnv("POSTGRES_PASSWORD", "adminpassword"),
		Db:       utils.GetEnv("POSTGRES_DB", "aura"),
	}
}
