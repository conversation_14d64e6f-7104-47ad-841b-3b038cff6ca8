package config

import (
	"time"

	"aura-api/internal/llm"
	"aura-api/pkg/connector"
	"aura-api/pkg/utils"
)

type LLMConfig struct {
	Provider    string
	BaseURL     string
	Model       string
	MaxTokens   int
	Temperature string
	Timeout     string
	MaxRetries  int
}

func LoadLLMConfig() llm.Config {
	baseURL := utils.GetEnv("LLM_BASE_URL", "https://api.openai.com")
	model := utils.GetEnv("LLM_MODEL", "gpt-3.5-turbo")
	maxTokens := utils.GetEnvAsInt("LLM_MAX_TOKENS", 1000)
	temperature := utils.GetEnvAsFloat("LLM_TEMPERATURE", 0.7)
	maxRetries := utils.GetEnvAsInt("LLM_MAX_RETRIES", 3)

	timeoutStr := utils.GetEnv("LLM_TIMEOUT", "30s")
	timeout, err := time.ParseDuration(timeoutStr)
	if err != nil {
		timeout = 30 * time.Second
	}

	systemPrompt := utils.GetEnv("LLM_SYSTEM_PROMPT",
		"You are a helpful AI assistant. Provide clear, accurate, and helpful responses. "+
			"Be concise but informative, and always strive to be helpful and respectful.")

	return llm.Config{
		DefaultModel: model,
		MaxTokens:    maxTokens,
		Temperature:  temperature,
		SystemPrompt: systemPrompt,
		ConnectorConfig: connector.Config{
			BaseURL:    baseURL,
			Timeout:    timeout,
			MaxRetries: maxRetries,
		},
	}
}
