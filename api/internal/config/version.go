package config

import (
	"fmt"
	"runtime"
	"time"
)

// Version information
var (
	Version   = "0.1.0"
	BuildTime = ""
	GitCommit = ""
)

// VersionInfo holds the version information
type VersionInfo struct {
	Version      string    `json:"version"`
	BuildTime    time.Time `json:"build_time,omitempty"`
	GitCommit    string    `json:"git_commit,omitempty"`
	GoVersion    string    `json:"go_version"`
	OS           string    `json:"os"`
	Architecture string    `json:"architecture"`
}

// GetVersionInfo returns the version information
func GetVersionInfo() VersionInfo {
	var buildTime time.Time
	if BuildTime != "" {
		var err error
		buildTime, err = time.Parse(time.RFC3339, BuildTime)
		if err != nil {
			buildTime = time.Time{}
		}
	}

	return VersionInfo{
		Version:      Version,
		BuildTime:    buildTime,
		GitCommit:    GitCommit,
		GoVersion:    runtime.Version(),
		OS:           runtime.GOOS,
		Architecture: runtime.GOARCH,
	}
}

// GetVersionString returns a formatted version string
func GetVersionString() string {
	vi := GetVersionInfo()
	return fmt.Sprintf("Version: %s, Build: %s, Go: %s",
		vi.Version,
		vi.BuildTime.Format(time.RFC3339),
		vi.GoVersion)
}
