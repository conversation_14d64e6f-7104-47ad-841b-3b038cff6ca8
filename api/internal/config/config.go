package config

import "aura-api/pkg/utils"

type AppConfig struct {
	AppEnv      string
	ApiPort     int
	Environment string
	JWTSecret   string
}

func LoadAppConfig() *AppConfig {
	return &AppConfig{
		AppEnv:      utils.GetEnv("APP_ENV", "development"),
		ApiPort:     utils.GetEnvAsInt("API_PORT", 3000),
		Environment: utils.GetEnv("API_ENV", "development"),
		JWTSecret:   utils.GetEnv("JWT_SECRET", "your-256-bit-secret"),
	}
}
