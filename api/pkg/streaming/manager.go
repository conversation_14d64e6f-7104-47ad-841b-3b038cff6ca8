package streaming

import (
	"sync"

	"github.com/asaskevich/EventBus"
	"github.com/gin-gonic/gin"
)

// EventType represents the type of event
type EventType string

const (
	EventTypeChatCreated     EventType = "chat.created"
	EventTypeChatUpdated     EventType = "chat.updated"
	EventTypeChatDeleted     EventType = "chat.deleted"
	EventTypeChatListUpdated EventType = "chat.list_updated"

	EventTypeMessageCreated   EventType = "message.created"
	EventTypeMessageUpdated   EventType = "message.updated"
	EventTypeMessageDeleted   EventType = "message.deleted"
	EventTypeMessageStreaming EventType = "message.streaming"
	EventTypeMessageCompleted EventType = "message.completed"
	EventTypeMessageFailed    EventType = "message.failed"

	EventTypeConnected EventType = "connected"
	EventTypeHeartbeat EventType = "heartbeat"
	EventTypeError     EventType = "error"
)

// Event represents a generic event
type Event struct {
	Type string `json:"type"`
	ID   string `json:"id,omitempty"`
	Data any    `json:"data"`
}

// StreamManager manages SSE connections and event distribution
type StreamManager interface {
	AddConnection(userID string, c *gin.Context) (<-chan Event, string)
	RemoveConnection(userID, connectionID string)
	PublishEvent(eventType EventType, userID, eventID string, data any)
	GetActiveConnections(userID string) int
}

type streamManager struct {
	mu          sync.RWMutex
	connections map[string]map[string]chan Event // userID -> connectionID -> event channel
	eventBus    EventBus.Bus
	handlers    map[string]func(EventType, string, any) // userID -> handler function
	idCounter   int64
}

// NewStreamManager creates a new StreamManager instance
func NewStreamManager() StreamManager {
	sm := &streamManager{
		connections: make(map[string]map[string]chan Event),
		eventBus:    EventBus.New(),
		handlers:    make(map[string]func(EventType, string, any)),
	}

	sm.setupEventSubscriptions()
	return sm
}

// AddConnection adds a new SSE connection for a user
func (sm *streamManager) AddConnection(userID string, c *gin.Context) (<-chan Event, string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Subscribe to user events if this is their first connection
	if sm.connections[userID] == nil {
		sm.connections[userID] = make(map[string]chan Event)
		sm.subscribeUserEvents(userID)
	}

	sm.idCounter++
	connectionID := generateConnectionID(sm.idCounter)

	// Create a buffered channel for this connection
	eventChannel := make(chan Event, 10)
	sm.connections[userID][connectionID] = eventChannel

	// Clean up connection when context is done
	go func() {
		<-c.Request.Context().Done()
		sm.RemoveConnection(userID, connectionID)
	}()

	return eventChannel, connectionID
}

// RemoveConnection removes an SSE connection
func (sm *streamManager) RemoveConnection(userID, connectionID string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if userConnections, exists := sm.connections[userID]; exists {
		if eventChannel, channelExists := userConnections[connectionID]; channelExists {
			close(eventChannel)
			delete(userConnections, connectionID)
		}

		// Unsubscribe from user events if this was their last connection
		if len(userConnections) == 0 {
			delete(sm.connections, userID)
			sm.unsubscribeUserEvents(userID)
		}
	}
}

// PublishEvent publishes a generic event
func (sm *streamManager) PublishEvent(eventType EventType, userID, eventID string, data any) {
	sm.eventBus.Publish("events:"+userID, eventType, eventID, data)
}

// GetActiveConnections returns the number of active connections for a user
func (sm *streamManager) GetActiveConnections(userID string) int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if userConnections, exists := sm.connections[userID]; exists {
		return len(userConnections)
	}
	return 0
}

// setupEventSubscriptions sets up event subscriptions for all users
func (sm *streamManager) setupEventSubscriptions() {
	// Subscribe to chat events with a wildcard pattern
	// Since EventBus doesn't support wildcards directly, we'll subscribe per user when they connect
}

// subscribeUserEvents subscribes to events for a specific user
func (sm *streamManager) subscribeUserEvents(userID string) {
	eventTopic := "events:" + userID

	handler := func(eventType EventType, eventID string, data any) {
		sm.broadcastToUser(userID, eventType, eventID, data)
	}

	sm.handlers[userID] = handler
	sm.eventBus.Subscribe(eventTopic, handler)
}

// unsubscribeUserEvents unsubscribes from events for a specific user
func (sm *streamManager) unsubscribeUserEvents(userID string) {
	eventTopic := "events:" + userID

	if handler, exists := sm.handlers[userID]; exists {
		sm.eventBus.Unsubscribe(eventTopic, handler)
		delete(sm.handlers, userID)
	}
}

// broadcastToUser sends an event to all connections for a specific user
func (sm *streamManager) broadcastToUser(userID string, eventType EventType, eventID string, data any) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	userConnections, exists := sm.connections[userID]
	if !exists {
		return
	}

	event := Event{
		Type: string(eventType),
		ID:   eventID,
		Data: data,
	}

	for _, eventChannel := range userConnections {
		go sm.sendEventToChannel(eventChannel, event)
	}
}

// sendEventToChannel safely sends an event to a channel
func (sm *streamManager) sendEventToChannel(ch chan Event, event Event) {
	defer func() {
		if r := recover(); r != nil {
			// Channel might be closed, ignore error
		}
	}()

	ch <- event
}

func generateConnectionID(counter int64) string {
	return string(rune('A'+(counter%26))) + string(rune('A'+((counter/26)%26))) + string(rune('0'+(counter%10)))
}
