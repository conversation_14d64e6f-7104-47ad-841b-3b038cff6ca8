package api

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SSEEvent represents a Server-Sent Event
type SSEEvent struct {
	Event string `json:"-"`
	ID    string `json:"-"`
	Data  any    `json:"data,omitempty"`
	Retry int    `json:"-"` // Retry interval in milliseconds
}

// SetupSSEHeaders sets the necessary headers for Server-Sent Events
func SetupSSEHeaders(c *gin.Context) {
	c.<PERSON><PERSON>("Content-Type", "text/event-stream")
	c.<PERSON><PERSON>("Cache-Control", "no-cache")
	c.<PERSON><PERSON>("Connection", "keep-alive")
	c<PERSON><PERSON>("Access-Control-Allow-Origin", c.<PERSON><PERSON>ead<PERSON>("Origin"))
	c.<PERSON>("Access-Control-Allow-Headers", "Cache-Control")
}

// SendSSEEvent sends a formatted SSE event to the client
func SendSSEEvent(c *gin.Context, eventType string, data any) error {
	event := SSEEvent{
		Event: eventType,
		Data:  data,
	}
	return WriteSSEEvent(c, event)
}

// SendSSEEventWithID sends a formatted SSE event with an ID to the client
func SendSSEEventWithID(c *gin.Context, eventType, id string, data any) error {
	event := SSEEvent{
		Event: eventType,
		ID:    id,
		Data:  data,
	}
	return WriteSSEEvent(c, event)
}

// SendSSEHeartbeat sends a heartbeat event
func SendSSEHeartbeat(c *gin.Context) error {
	return SendSSEEvent(c, "heartbeat", map[string]any{
		"timestamp": time.Now().Unix(),
	})
}

// WriteSSEEvent writes a complete SSE event to the response writer
func WriteSSEEvent(c *gin.Context, event SSEEvent) error {
	var builder strings.Builder

	if event.Event != "" {
		builder.WriteString(fmt.Sprintf("event: %s\n", event.Event))
	}

	if event.ID != "" {
		builder.WriteString(fmt.Sprintf("id: %s\n", event.ID))
	}

	if event.Retry > 0 {
		builder.WriteString(fmt.Sprintf("retry: %d\n", event.Retry))
	}

	if event.Data != nil {
		jsonData, err := json.Marshal(event.Data)
		if err != nil {
			return fmt.Errorf("failed to marshal SSE event data: %w", err)
		}

		// Handle multi-line data by prefixing each line with "data: "
		dataStr := string(jsonData)
		lines := strings.SplitSeq(dataStr, "\n")
		for line := range lines {
			builder.WriteString(fmt.Sprintf("data: %s\n", line))
		}
	}

	// Add final newline to complete the event
	builder.WriteString("\n")

	_, err := c.Writer.WriteString(builder.String())
	if err != nil {
		return fmt.Errorf("failed to write SSE event: %w", err)
	}

	c.Writer.Flush()
	return nil
}
