package api

import (
	"net/http"

	"aura-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ResponseStatus string

const (
	StatusOK    ResponseStatus = "ok"
	StatusError ResponseStatus = "error"
)

// ErrorDetail represents a single validation or error detail
type ErrorDetail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

// APIResponse represents the unified JSON response structure
type APIResponse[T any] struct {
	Status  ResponseStatus `json:"status"`
	Message *string        `json:"message,omitempty"`
	Error   *string        `json:"error,omitempty"`
	Details []ErrorDetail  `json:"details,omitempty"`
	Code    *string        `json:"code,omitempty"`
	Data    *T             `json:"data,omitempty"`
}

// SuccessResponse creates a successful API response
func SuccessResponse[T any](c *gin.Context, statusCode int, data T, message ...string) {
	response := APIResponse[T]{
		Status: StatusOK,
	}

	if !utils.IsNilOrZero(data) {
		response.Data = &data
	}

	if len(message) > 0 && message[0] != "" {
		response.Message = &message[0]
	}

	c.JSON(statusCode, response)
}

// ErrorResponse creates an error API response
func ErrorResponse(c *gin.Context, statusCode int, errorMsg string, code ...string) {
	response := APIResponse[any]{
		Status: StatusError,
		Error:  &errorMsg,
	}

	if len(code) > 0 && code[0] != "" {
		response.Code = &code[0]
	}

	c.JSON(statusCode, response)
}

// ValidationErrorResponse creates an error response with validation details
func ValidationErrorResponse(c *gin.Context, err error) {
	details := formatValidationErrors(err)
	errorMsg := "Validation failed"

	response := APIResponse[any]{
		Status:  StatusError,
		Error:   &errorMsg,
		Details: details,
	}

	c.JSON(http.StatusBadRequest, response)
}

// ErrorResponseWithDetails creates an error response with custom details
func ErrorResponseWithDetails(c *gin.Context, statusCode int, errorMsg string, details []ErrorDetail, code ...string) {
	response := APIResponse[any]{
		Status:  StatusError,
		Error:   &errorMsg,
		Details: details,
	}

	if len(code) > 0 && code[0] != "" {
		response.Code = &code[0]
	}

	c.JSON(statusCode, response)
}

// UnauthorizedResponse creates an unauthorized access response
func UnauthorizedResponse(c *gin.Context, message ...string) {
	errorMsg := "Unauthorized access"
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}

	response := APIResponse[any]{
		Status: StatusError,
		Error:  &errorMsg,
		Code:   utils.StringPtr("UNAUTHORIZED"),
	}

	c.JSON(http.StatusUnauthorized, response)
	c.Abort()
}

// formatValidationErrors converts validator errors to ErrorDetail slice
func formatValidationErrors(err error) []ErrorDetail {
	var details []ErrorDetail

	validationErr, ok := err.(validator.ValidationErrors)
	if !ok {
		details = append(details, ErrorDetail{
			Message: err.Error(),
		})
		return details
	}

	for _, fieldErr := range validationErr {
		details = append(details, ErrorDetail{
			Field:   fieldErr.Field(),
			Message: getValidationErrorMessage(fieldErr),
		})
	}

	return details
}

// getValidationErrorMessage returns user-friendly validation error messages
func getValidationErrorMessage(fieldErr validator.FieldError) string {
	switch fieldErr.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Must be a valid email address"
	case "uuid":
		return "Must be a valid UUID"
	case "min":
		if fieldErr.Kind().String() == "string" {
			return "Must be at least " + fieldErr.Param() + " characters long"
		}
		return "Must be at least " + fieldErr.Param()
	case "max":
		if fieldErr.Kind().String() == "string" {
			return "Must be no more than " + fieldErr.Param() + " characters long"
		}
		return "Must be no more than " + fieldErr.Param()
	case "len":
		return "Must be exactly " + fieldErr.Param() + " characters long"
	case "oneof":
		return "Must be one of: " + fieldErr.Param()
	case "numeric":
		return "Must be a valid number"
	case "alpha":
		return "Must contain only letters"
	case "alphanum":
		return "Must contain only letters and numbers"
	default:
		return "Invalid value"
	}
}
