package utils

import "reflect"

// StringPtr is a helper function to get a pointer to a string
func StringPtr(s string) *string {
	return &s
}

// IsNilOrZero checks if a value is nil or the zero value for its type
func IsNilOrZero[T any](value T) bool {
	if any(value) == nil {
		return true
	}

	v := reflect.ValueOf(value)

	switch v.Kind() {
	case reflect.Ptr, reflect.Interface, reflect.Slice, reflect.Map, reflect.Chan, reflect.Func:
		return v.IsNil()
	case reflect.Invalid:
		return true
	default:
		return v.IsZero()
	}
}
