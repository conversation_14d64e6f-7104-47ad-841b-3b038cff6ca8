package connector

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// HTTPClient defines the interface for HTTP communication
type HTTPClient interface {
	// HTTP methods
	Post(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error)
	PostStream(ctx context.Context, path string, headers map[string]string, body any) (<-chan StreamChunk, error)
	Get(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error)
	Put(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error)
	Delete(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error)

	// Multipart file upload
	UploadFile(ctx context.Context, path string, headers map[string]string, fileData []byte, fileName string, formFields map[string]string) (*HTTPResponse, error)

	// WebSocket methods
	ConnectWebSocket(ctx context.Context, path string, headers map[string]string) (WebSocketConnection, error)

	// Lifecycle
	Close() error
}

// Config holds HTTP client configuration
type Config struct {
	BaseURL    string        // Base URL without trailing slash
	Timeout    time.Duration // Request timeout
	MaxRetries int           // Maximum retry attempts
}

// HTTPResponse represents a generic HTTP response
type HTTPResponse struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       map[string]any    `json:"body"`
	RawBody    []byte            `json:"-"`
}

// StreamChunk represents a chunk of streaming response
type StreamChunk struct {
	Data  map[string]any `json:"data"`
	Done  bool           `json:"done"`
	Error error          `json:"-"`
}

// WebSocketConnection defines the interface for WebSocket communication
type WebSocketConnection interface {
	SendJSON(ctx context.Context, data any) error
	ReadJSON(ctx context.Context, data any) error
	ReadMessage(ctx context.Context) ([]byte, error)
	SendMessage(ctx context.Context, data []byte) error
	Close() error
}

type httpClient struct {
	config     Config
	httpClient *http.Client
	wsConn     *websocket.Conn
}

// NewHTTPClient creates a new HTTP client instance
func NewHTTPClient(config Config) HTTPClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &httpClient{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// Post sends a POST request to the specified path
func (c *httpClient) Post(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error) {
	return c.makeRequest(ctx, http.MethodPost, path, headers, body)
}

// PostStream sends a streaming POST request to the specified path
func (c *httpClient) PostStream(ctx context.Context, path string, headers map[string]string, body any) (<-chan StreamChunk, error) {
	reqBody, err := c.marshalBody(body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	fullURL := c.buildURL(path)
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(httpReq, headers)
	httpReq.Header.Set("Accept", "text/event-stream")
	httpReq.Header.Set("Cache-Control", "no-cache")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		resp.Body.Close()
		return nil, c.handleErrorResponse(resp)
	}

	chunks := make(chan StreamChunk, 10)

	go func() {
		defer resp.Body.Close()
		defer close(chunks)
		c.processStreamResponse(ctx, resp.Body, chunks)
	}()

	return chunks, nil
}

// Get sends a GET request to the specified path
func (c *httpClient) Get(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error) {
	return c.makeRequest(ctx, http.MethodGet, path, headers, nil)
}

// Put sends a PUT request to the specified path
func (c *httpClient) Put(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error) {
	return c.makeRequest(ctx, http.MethodPut, path, headers, body)
}

// Delete sends a DELETE request to the specified path
func (c *httpClient) Delete(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error) {
	return c.makeRequest(ctx, http.MethodDelete, path, headers, nil)
}

// UploadFile uploads a file using multipart form data
func (c *httpClient) UploadFile(ctx context.Context, path string, headers map[string]string, fileData []byte, fileName string, formFields map[string]string) (*HTTPResponse, error) {
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add the file
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(fileData); err != nil {
		return nil, fmt.Errorf("failed to write file data: %w", err)
	}

	// Add other form fields
	for key, value := range formFields {
		if err := writer.WriteField(key, value); err != nil {
			return nil, fmt.Errorf("failed to write form field %s: %w", key, err)
		}
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	fullURL := c.buildURL(path)
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set custom headers first (but skip Content-Type for multipart)
	httpReq.Header.Set("User-Agent", "aura-api/1.0")
	for key, value := range headers {
		if key != "Content-Type" { // Skip Content-Type from custom headers for file upload
			httpReq.Header.Set(key, value)
		}
	}

	// Set multipart content type (this should be the final Content-Type)
	httpReq.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	return c.parseResponse(resp)
}

// ConnectWebSocket establishes a WebSocket connection
func (c *httpClient) ConnectWebSocket(ctx context.Context, path string, headers map[string]string) (WebSocketConnection, error) {
	u, err := url.Parse(c.config.BaseURL)
	if err != nil {
		return nil, fmt.Errorf("invalid base URL: %w", err)
	}

	// Convert HTTP(S) to WS(S)
	scheme := "ws"
	if u.Scheme == "https" {
		scheme = "wss"
	}

	wsURL := fmt.Sprintf("%s://%s%s", scheme, u.Host, path)

	reqHeaders := make(http.Header)
	for key, value := range headers {
		reqHeaders.Set(key, value)
	}
	reqHeaders.Set("User-Agent", "aura-api/1.0")

	dialer := websocket.Dialer{
		HandshakeTimeout: c.config.Timeout,
	}

	conn, _, err := dialer.DialContext(ctx, wsURL, reqHeaders)
	if err != nil {
		return nil, fmt.Errorf("failed to connect WebSocket: %w", err)
	}

	c.wsConn = conn
	return &wsConnection{conn: conn}, nil
}

// Close closes the HTTP client and any active connections
func (c *httpClient) Close() error {
	if c.wsConn != nil {
		return c.wsConn.Close()
	}
	return nil
}

// makeRequest is a helper method for making HTTP requests
func (c *httpClient) makeRequest(ctx context.Context, method, path string, headers map[string]string, body any) (*HTTPResponse, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = c.marshalBody(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	fullURL := c.buildURL(path)
	httpReq, err := http.NewRequestWithContext(ctx, method, fullURL, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(httpReq, headers)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	return c.parseResponse(resp)
}

// buildURL builds the full URL from base URL and path
func (c *httpClient) buildURL(path string) string {
	baseURL := strings.TrimSuffix(c.config.BaseURL, "/")
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}
	return baseURL + path
}

// marshalBody marshals the request body to JSON
func (c *httpClient) marshalBody(body any) ([]byte, error) {
	if body == nil {
		return nil, nil
	}
	return json.Marshal(body)
}

// setHeaders sets HTTP headers on the request
func (c *httpClient) setHeaders(req *http.Request, headers map[string]string) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "aura-api/1.0")

	for key, value := range headers {
		req.Header.Set(key, value)
	}
}

// parseResponse parses the HTTP response into our generic format
func (c *httpClient) parseResponse(resp *http.Response) (*HTTPResponse, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse headers
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	response := &HTTPResponse{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		RawBody:    body,
	}

	// Try to parse JSON body
	if len(body) > 0 {
		var jsonBody map[string]any
		if err := json.Unmarshal(body, &jsonBody); err == nil {
			response.Body = jsonBody
		}
	}

	// Handle error status codes
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return response, c.handleErrorResponse(resp)
	}

	return response, nil
}

// handleErrorResponse handles non-success HTTP responses
func (c *httpClient) handleErrorResponse(resp *http.Response) error {
	body, _ := io.ReadAll(resp.Body)

	var errorResp struct {
		Error   string `json:"error"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(body, &errorResp); err == nil {
		if errorResp.Message != "" {
			return fmt.Errorf("HTTP %d: %s", resp.StatusCode, errorResp.Message)
		}
		if errorResp.Error != "" {
			return fmt.Errorf("HTTP %d: %s", resp.StatusCode, errorResp.Error)
		}
	}

	return fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
}

// processStreamResponse processes streaming response data
func (c *httpClient) processStreamResponse(ctx context.Context, body io.ReadCloser, chunks chan<- StreamChunk) {
	scanner := bufio.NewScanner(body)

	for scanner.Scan() {
		select {
		case <-ctx.Done():
			chunks <- StreamChunk{Error: ctx.Err(), Done: true}
			return
		default:
		}

		line := scanner.Text()
		if line == "" {
			continue
		}

		// Handle Server-Sent Events format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			if data == "[DONE]" {
				chunks <- StreamChunk{Done: true}
				return
			}

			var chunkData map[string]any
			if err := json.Unmarshal([]byte(data), &chunkData); err != nil {
				chunks <- StreamChunk{Error: fmt.Errorf("failed to parse chunk: %w", err), Done: true}
				return
			}

			chunks <- StreamChunk{Data: chunkData}
		}
	}

	if err := scanner.Err(); err != nil {
		chunks <- StreamChunk{Error: fmt.Errorf("scanner error: %w", err), Done: true}
		return
	}

	chunks <- StreamChunk{Done: true}
}

// wsConnection implements WebSocketConnection
type wsConnection struct {
	conn *websocket.Conn
}

func (ws *wsConnection) SendJSON(ctx context.Context, data any) error {
	return ws.conn.WriteJSON(data)
}

func (ws *wsConnection) ReadJSON(ctx context.Context, data any) error {
	return ws.conn.ReadJSON(data)
}

func (ws *wsConnection) ReadMessage(ctx context.Context) ([]byte, error) {
	_, message, err := ws.conn.ReadMessage()
	return message, err
}

func (ws *wsConnection) SendMessage(ctx context.Context, data []byte) error {
	return ws.conn.WriteMessage(websocket.TextMessage, data)
}

func (ws *wsConnection) Close() error {
	return ws.conn.Close()
}
