package connector

import (
	"context"
	"fmt"
	"time"
)

// Manager provides high-level HTTP operations with retry logic and connection pooling
type Manager interface {
	// HTTP methods with retry
	Post(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error)
	PostStream(ctx context.Context, path string, headers map[string]string, body any) (<-chan StreamChunk, error)
	Get(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error)
	Put(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error)
	Delete(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error)

	// File upload with retry
	UploadFile(ctx context.Context, path string, headers map[string]string, fileData []byte, fileName string, formFields map[string]string) (*HTTPResponse, error)

	// WebSocket with connection pooling
	ConnectWebSocket(ctx context.Context, path string, headers map[string]string) (WebSocketConnection, error)

	// Health and lifecycle
	Health(ctx context.Context, healthPath string, headers map[string]string) error
	Close() error
}

// StreamingManager provides additional streaming-specific functionality
type StreamingManager interface {
	Manager
	StreamToChannel(ctx context.Context, path string, headers map[string]string, body any, output chan<- StreamChunk) error
}

type manager struct {
	client HTTPClient
	config Config
	pool   *ConnectionPool
}

// NewManager creates a new connector manager
func NewManager(config Config) Manager {
	client := NewHTTPClient(config)

	return &manager{
		client: client,
		config: config,
		pool:   NewConnectionPool(10), // Default pool size of 10
	}
}

// Post sends a POST request with retry logic
func (m *manager) Post(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		response, err := m.client.Post(ctx, path, headers, body)
		if err == nil {
			return response, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("failed after %d attempts", retryPolicy.MaxRetries)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// PostStream sends a streaming POST request with retry logic
func (m *manager) PostStream(ctx context.Context, path string, headers map[string]string, body any) (<-chan StreamChunk, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		chunks, err := m.client.PostStream(ctx, path, headers, body)
		if err == nil {
			return chunks, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("max retries exceeded: %w", err)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// Get sends a GET request with retry logic
func (m *manager) Get(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		response, err := m.client.Get(ctx, path, headers)
		if err == nil {
			return response, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("max retries exceeded: %w", err)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// Put sends a PUT request with retry logic
func (m *manager) Put(ctx context.Context, path string, headers map[string]string, body any) (*HTTPResponse, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		response, err := m.client.Put(ctx, path, headers, body)
		if err == nil {
			return response, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("max retries exceeded: %w", err)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// Delete sends a DELETE request with retry logic
func (m *manager) Delete(ctx context.Context, path string, headers map[string]string) (*HTTPResponse, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		response, err := m.client.Delete(ctx, path, headers)
		if err == nil {
			return response, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("max retries exceeded: %w", err)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// UploadFile uploads a file with retry logic
func (m *manager) UploadFile(ctx context.Context, path string, headers map[string]string, fileData []byte, fileName string, formFields map[string]string) (*HTTPResponse, error) {
	retryPolicy := DefaultRetryPolicy()
	retryPolicy.MaxRetries = m.config.MaxRetries

	for attempt := 1; attempt <= retryPolicy.MaxRetries; attempt++ {
		response, err := m.client.UploadFile(ctx, path, headers, fileData, fileName, formFields)
		if err == nil {
			return response, nil
		}

		// Check if error is retryable
		if connErr, ok := err.(*ConnectorError); ok && !connErr.IsRetryable() {
			return nil, err
		}

		// Don't retry on last attempt
		if attempt == retryPolicy.MaxRetries {
			return nil, fmt.Errorf("max retries exceeded: %w", err)
		}

		// Wait before retrying
		delay := time.Duration(retryPolicy.BackoffFn(attempt)) * time.Millisecond

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return nil, fmt.Errorf("unexpected retry loop exit")
}

// ConnectWebSocket establishes a WebSocket connection with connection pooling
func (m *manager) ConnectWebSocket(ctx context.Context, path string, headers map[string]string) (WebSocketConnection, error) {
	return m.pool.Get(m.client, path, headers)
}

// Health performs a health check
func (m *manager) Health(ctx context.Context, healthPath string, headers map[string]string) error {
	_, err := m.client.Get(ctx, healthPath, headers)
	return err
}

// Close closes the manager and all its resources
func (m *manager) Close() error {
	var lastErr error

	if err := m.pool.Close(); err != nil {
		lastErr = err
	}

	if err := m.client.Close(); err != nil {
		lastErr = err
	}

	return lastErr
}

type streamingManager struct {
	Manager
}

// NewStreamingManager creates a new streaming manager
func NewStreamingManager(config Config) StreamingManager {
	return &streamingManager{
		Manager: NewManager(config),
	}
}

// StreamToChannel streams data to the provided channel
func (sm *streamingManager) StreamToChannel(ctx context.Context, path string, headers map[string]string, body any, output chan<- StreamChunk) error {
	chunks, err := sm.PostStream(ctx, path, headers, body)
	if err != nil {
		return err
	}

	go func() {
		defer close(output)

		for chunk := range chunks {
			select {
			case <-ctx.Done():
				return
			case output <- chunk:
				if chunk.Done || chunk.Error != nil {
					return
				}
			}
		}
	}()

	return nil
}
