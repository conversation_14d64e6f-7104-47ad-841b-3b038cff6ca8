package connector

import (
	"context"
	"fmt"
)

// RetryPolicy defines retry behavior for failed requests
type RetryPolicy struct {
	MaxRetries int
	BackoffFn  func(attempt int) int // Returns delay in milliseconds
}

// DefaultRetryPolicy returns a sensible default retry policy
func DefaultRetryPolicy() RetryPolicy {
	return RetryPolicy{
		MaxRetries: 3,
		BackoffFn: func(attempt int) int {
			// Exponential backoff: 1s, 2s, 4s
			return 1000 << (attempt - 1)
		},
	}
}

// ConnectionPool manages a pool of connections for better performance
type ConnectionPool struct {
	maxConns int
	conns    chan WebSocketConnection
}

// NewConnectionPool creates a new connection pool
func NewConnectionPool(maxConns int) *ConnectionPool {
	// Ensure non-negative capacity for the channel
	capacity := maxConns
	if capacity < 0 {
		capacity = 0
	}

	return &ConnectionPool{
		maxConns: maxConns,
		conns:    make(chan WebSocketConnection, capacity),
	}
}

// Get retrieves a connection from the pool or creates a new one
func (p *ConnectionPool) Get(client HTTPClient, path string, headers map[string]string) (WebSocketConnection, error) {
	select {
	case conn := <-p.conns:
		return conn, nil
	default:
		// No available connections, create a new one
		return client.ConnectWebSocket(context.TODO(), path, headers)
	}
}

// Put returns a connection to the pool
func (p *ConnectionPool) Put(conn WebSocketConnection) {
	select {
	case p.conns <- conn:
		// Connection returned to pool
	default:
		// Pool is full, close the connection
		conn.Close()
	}
}

// Close closes all connections in the pool
func (p *ConnectionPool) Close() error {
	close(p.conns)

	var lastErr error
	for conn := range p.conns {
		if err := conn.Close(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// ErrorType represents different types of errors that can occur
type ErrorType string

const (
	ErrorTypeNetwork      ErrorType = "network"
	ErrorTypeAuth         ErrorType = "auth"
	ErrorTypeRateLimit    ErrorType = "rate_limit"
	ErrorTypeServerError  ErrorType = "server_error"
	ErrorTypeTimeout      ErrorType = "timeout"
	ErrorTypeInvalidInput ErrorType = "invalid_input"
)

// ConnectorError represents an error from the connector
type ConnectorError struct {
	Type    ErrorType
	Message string
	Code    int
	Retry   bool
}

// Error implements the error interface
func (e *ConnectorError) Error() string {
	return fmt.Sprintf("%s: %s (code: %d, retry: %t)", e.Type, e.Message, e.Code, e.Retry)
}

// IsRetryable returns true if the error is retryable
func (e *ConnectorError) IsRetryable() bool {
	return e.Retry
}

// ClassifyError classifies an error into appropriate error types
func ClassifyError(err error, statusCode int) *ConnectorError {
	if err == nil {
		return nil
	}

	switch statusCode {
	case 401, 403:
		return &ConnectorError{
			Type:    ErrorTypeAuth,
			Message: err.Error(),
			Code:    statusCode,
			Retry:   false,
		}
	case 429:
		return &ConnectorError{
			Type:    ErrorTypeRateLimit,
			Message: err.Error(),
			Code:    statusCode,
			Retry:   true,
		}
	case 400, 422:
		return &ConnectorError{
			Type:    ErrorTypeInvalidInput,
			Message: err.Error(),
			Code:    statusCode,
			Retry:   false,
		}
	case 500, 502, 503, 504:
		return &ConnectorError{
			Type:    ErrorTypeServerError,
			Message: err.Error(),
			Code:    statusCode,
			Retry:   true,
		}
	default:
		return &ConnectorError{
			Type:    ErrorTypeNetwork,
			Message: err.Error(),
			Code:    statusCode,
			Retry:   true,
		}
	}
}
