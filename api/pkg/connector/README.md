# HTTP Connector Package

The connector package provides a generic HTTP client interface for communicating with any HTTP/WebSocket-based APIs. It supports streaming responses, connection pooling, retry logic, and comprehensive error handling without being tied to any specific service or protocol.

## Features

- **Generic HTTP Client**: Support for GET, POST, PUT, DELETE requests to any endpoint
- **Path-Based Requests**: Services specify the endpoint paths, connector handles the HTTP mechanics
- **Streaming Responses**: Real-time streaming with Server-Sent Events (SSE)
- **File Upload Support**: Multipart file upload with custom form fields
- **WebSocket Support**: Generic WebSocket connections with JSON and raw message support
- **Connection Pooling**: Efficient connection management for WebSocket connections
- **Retry Logic**: Configurable retry policies with exponential backoff
- **Error Handling**: Comprehensive error classification and handling
- **Provider Agnostic**: Works with any HTTP-based API
- **Thread-Safe**: Concurrent-safe operations

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   API Service   │───▶│ HTTP Client  │───▶│   Connector     │
│  (defines paths)│    │  (generic)   │    │   (generic)     │
└─────────────────┘    └──────────────┘    └─────────────────┘
                                                    │
                                           ┌────────┴────────┐
                                           │                 │
                                      ┌────▼────┐    ┌──────▼──────┐
                                      │  HTTP   │    │  WebSocket  │
                                      │ Methods │    │ Connection  │
                                      └─────────┘    └─────────────┘
```

## Components

### 1. HTTPClient Interface
Generic interface for HTTP communication:
- `Post()` - Send POST requests to any path
- `PostStream()` - Send streaming POST requests
- `Get()` - Send GET requests to any path
- `Put()` - Send PUT requests to any path
- `Delete()` - Send DELETE requests to any path
- `UploadFile()` - Upload files with custom form fields
- `ConnectWebSocket()` - Establish WebSocket connections to any path
- `Close()` - Clean up resources

### 2. Manager Interface
High-level management with retry logic:
- Automatic retry with exponential backoff for all HTTP methods
- Health checking capabilities on any endpoint
- File upload with retry logic
- Connection pooling for WebSocket connections

### 3. StreamingManager Interface
Extended streaming functionality:
- Callback-based streaming for any endpoint
- Channel-based streaming for any endpoint
- Stream processing utilities

## Core Principles

The connector is **completely generic** and makes no assumptions about:
- API endpoints or paths (these are provided by the calling service)
- Request/response formats (handled as generic JSON or raw data)
- Authentication schemes (headers are provided by the calling service)
- Service-specific logic (handled by the calling service)

## Usage Examples

### Basic HTTP Communication

```go
package main

import (
    "context"
    "fmt"
    "time"

    "aura-api/pkg/connector"
)

func main() {
    config := connector.Config{
        BaseURL: "https://api.example.com",  // Only base URL, no paths
        Timeout: 30 * time.Second,
        MaxRetries: 3,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    // Service decides the path and request format
    requestBody := map[string]interface{}{
        "question": "What are the latest developments in renewable energy?",
        "session_id": "sess_abc123def456",
    }

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
        "Content-Type": "application/json",
    }

    ctx := context.Background()
    response, err := manager.Post(ctx, "/api/v1/questions/simple", headers, requestBody)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Status: %d\n", response.StatusCode)
    fmt.Printf("Response: %+v\n", response.Body)
}
```

### Streaming Communication

```go
func streamingExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewStreamingManager(config)
    defer manager.Close()

    requestBody := map[string]interface{}{
        "question": "Analyze the economic impact of AI",
        "session_id": "sess_abc123def456",
        "config": map[string]interface{}{
            "enable_optimization": true,
            "max_iterations": 5,
        },
        "workflow_type": "enhanced",
    }

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
    }

    ctx := context.Background()
    err := manager.StreamWithCallback(ctx, "/api/v1/questions/advanced", headers, requestBody, func(chunk connector.StreamChunk) {
        if chunk.Error != nil {
            fmt.Printf("Error: %v\n", chunk.Error)
            return
        }

        // Process the chunk data based on your API's format
        if chunkData, ok := chunk.Data["data"].(map[string]interface{}); ok {
            if chunkText, exists := chunkData["chunk"]; exists {
                fmt.Print(chunkText)
            }
        }

        if chunk.Done {
            fmt.Println("\n[Stream completed]")
        }
    })

    if err != nil {
        panic(err)
    }
}
```

### WebSocket Communication

```go
func websocketExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        Timeout: 30 * time.Second,
    }

    client := connector.NewHTTPClient(config)
    defer client.Close()

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
    }

    ctx := context.Background()
    wsConn, err := client.ConnectWebSocket(ctx, "/api/v1/ws/workflows/wf_abc123def456", headers)
    if err != nil {
        panic(err)
    }
    defer wsConn.Close()

    // Send JSON message
    message := map[string]interface{}{
        "type": "subscribe",
        "workflow_id": "wf_abc123def456",
    }

    if err := wsConn.SendJSON(ctx, message); err != nil {
        panic(err)
    }

    // Read JSON responses
    for {
        var response map[string]interface{}
        if err := wsConn.ReadJSON(ctx, &response); err != nil {
            break
        }

        fmt.Printf("Received: %+v\n", response)

        // Check for completion
        if msgType, ok := response["type"].(string); ok && msgType == "workflow_complete" {
            break
        }
    }
}
```

### File Upload Communication

```go
func fileUploadExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    // Read file data
    fileContent := []byte("This is the content of my document.")

    // Prepare form fields - these depend on your API
    formFields := map[string]string{
        "session_id": "sess_abc123def456",
        "file_name": "my_document.txt",  // Optional custom filename
    }

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
    }

    ctx := context.Background()
    response, err := manager.UploadFile(ctx, "/api/v1/files/upload", headers, fileContent, "my_document.txt", formFields)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Upload successful! Response: %+v\n", response.Body)
}
```

### Get Request Example

```go
func getExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
    }

    ctx := context.Background()

    // Get workflow status
    response, err := manager.Get(ctx, "/api/v1/workflows/wf_abc123def456/status", headers)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Workflow Status: %+v\n", response.Body)

    // Get uploaded files for session
    response2, err := manager.Get(ctx, "/api/v1/files/sess_abc123def456", headers)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Session Files: %+v\n", response2.Body)
}
```

### Delete Request Example

```go
func deleteExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    headers := map[string]string{
        "Authorization": "Bearer your-api-key",
    }

    ctx := context.Background()

    // Cancel workflow
    response, err := manager.Delete(ctx, "/api/v1/workflows/wf_abc123def456", headers)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Workflow cancelled: %+v\n", response.Body)
}
```

## Configuration

### Environment Variables

```bash
# Configuration can be set via environment or programmatically
HTTP_BASE_URL=https://api.example.com
HTTP_TIMEOUT=30s
HTTP_MAX_RETRIES=3
```

### Programmatic Configuration

```go
config := connector.Config{
    BaseURL:    "https://api.example.com",  // Only base URL
    Timeout:    30 * time.Second,
    MaxRetries: 3,
}
```

## Error Handling

The connector provides comprehensive error handling:

```go
response, err := manager.Post(ctx, "/api/v1/questions/simple", headers, requestBody)
if err != nil {
    if connErr, ok := err.(*connector.ConnectorError); ok {
        switch connErr.Type {
        case connector.ErrorTypeAuth:
            // Handle authentication errors (401, 403)
        case connector.ErrorTypeRateLimit:
            // Handle rate limiting (429)
        case connector.ErrorTypeServerError:
            // Handle server errors (5xx)
        case connector.ErrorTypeNetwork:
            // Handle network issues
        default:
            // Handle other errors
        }

        if connErr.IsRetryable() {
            // Error can be retried automatically
        }
    }
}

// Check response status
if response.StatusCode >= 400 {
    // Handle error response
    if errorMsg, exists := response.Body["message"]; exists {
        fmt.Printf("API Error: %s\n", errorMsg)
    }
}
```

## Response Format

All HTTP responses are returned in a generic format:

```go
type HTTPResponse struct {
    StatusCode int                    `json:"status_code"`  // HTTP status code
    Headers    map[string]string      `json:"headers"`      // Response headers
    Body       map[string]interface{} `json:"body"`         // Parsed JSON body (if applicable)
    RawBody    []byte                 `json:"-"`            // Raw response body
}
```

## Stream Chunks

Streaming responses provide chunks in this format:

```go
type StreamChunk struct {
    Data  map[string]interface{} `json:"data"`  // Parsed chunk data
    Done  bool                   `json:"done"`  // Whether stream is complete
    Error error                  `json:"-"`     // Any error that occurred
}
```

## Integration Guidelines

### Service Implementation

When using this connector in your service:

1. **Define your paths**: The service defines all endpoint paths
2. **Prepare headers**: Include authentication, content-type, etc.
3. **Format requests**: Structure your request bodies according to your API
4. **Handle responses**: Parse the generic HTTPResponse according to your API format

### Example Service Layer

```go
type MyAPIService struct {
    manager connector.Manager
    apiKey  string
}

func (s *MyAPIService) AskQuestion(ctx context.Context, question, sessionID string) (*QuestionResponse, error) {
    requestBody := map[string]interface{}{
        "question": question,
        "session_id": sessionID,
    }

    headers := map[string]string{
        "Authorization": "Bearer " + s.apiKey,
        "Content-Type": "application/json",
    }

    response, err := s.manager.Post(ctx, "/api/v1/questions/simple", headers, requestBody)
    if err != nil {
        return nil, err
    }

    // Parse response according to your API format
    var result QuestionResponse
    if workflowID, exists := response.Body["workflow_id"]; exists {
        result.WorkflowID = workflowID.(string)
    }

    return &result, nil
}
```

## Performance Considerations

### Connection Pooling
WebSocket connections are pooled for efficiency:
- Default pool size: 10 connections
- Automatic cleanup of stale connections
- Load balancing across available connections

### Retry Logic
Configurable retry with exponential backoff:
- Default: 3 retries with 1s, 2s, 4s delays
- Respects HTTP status codes for retry decisions
- Circuit breaker pattern for persistent failures

### Memory Management
- Streaming responses use buffered channels
- Automatic cleanup of resources
- Context-based cancellation support

## Security Considerations

- No API keys or sensitive data stored in the connector
- HTTPS/WSS enforced for external communications
- Headers are passed through without modification
- Request/response data handled generically
- Timeout enforcement prevents resource leaks
        MaxRetries: 3,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    req := &connector.MessageRequest{
        Messages: []connector.Message{
            {Role: "user", Content: "Hello, world!"},
        },
        Model:       "gpt-3.5-turbo",
        MaxTokens:   100,
        Temperature: 0.7,
    }

    ctx := context.Background()
    response, err := manager.SendMessage(ctx, req)
    if err != nil {
        panic(err)
    }

    fmt.Println("Response:", response.Choices[0].Message.Content)
}
```

### Streaming Communication

```go
func streamingExample() {
    config := connector.Config{
        BaseURL: "https://api.openai.com",
        APIKey:  "your-api-key",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewStreamingManager(config)
    defer manager.Close()

    req := &connector.MessageRequest{
        Messages: []connector.Message{
            {Role: "user", Content: "Tell me a story"},
        },
        Model:       "gpt-3.5-turbo",
        MaxTokens:   200,
        Temperature: 0.8,
    }

    ctx := context.Background()
    err := manager.StreamWithCallback(ctx, req, func(chunk connector.StreamChunk) {
        if chunk.Error != nil {
            fmt.Printf("Error: %v\n", chunk.Error)
            return
        }

        if len(chunk.Choices) > 0 && chunk.Choices[0].Delta.Content != "" {
            fmt.Print(chunk.Choices[0].Delta.Content)
        }

        if chunk.Done {
            fmt.Println("\n[Stream completed]")
        }
    })

    if err != nil {
        panic(err)
    }
}
```

### WebSocket Communication

```go
func websocketExample() {
    config := connector.Config{
        BaseURL: "wss://api.openai.com",
        APIKey:  "your-api-key",
        Timeout: 30 * time.Second,
    }

    client := connector.NewLLMClient(config)
    defer client.Close()

    ctx := context.Background()
    wsConn, err := client.ConnectWebSocket(ctx)
    if err != nil {
        panic(err)
    }
    defer wsConn.Close()

    req := &connector.MessageRequest{
        Messages: []connector.Message{
            {Role: "user", Content: "Hello via WebSocket!"},
        },
        Model: "gpt-3.5-turbo",
    }

    if err := wsConn.SendMessage(ctx, req); err != nil {
        panic(err)
    }

    for {
        chunk, err := wsConn.ReadMessage(ctx)
        if err != nil {
            break
        }

        if chunk.Done {
            break
        }

        // Process chunk...
    }
}
```

### File Upload Communication

```go
func fileUploadExample() {
    config := connector.Config{
        BaseURL: "https://api.example.com",
        APIKey:  "your-api-key",
        Timeout: 30 * time.Second,
    }

    manager := connector.NewManager(config)
    defer manager.Close()

    // Example 1: Upload from byte slice
    fileContent := []byte("This is the content of my document.")
    req := &connector.FileUploadRequest{
        File:      fileContent,
        FileName:  "my_document.txt",
        SessionID: "session_abc123",
    }

    ctx := context.Background()
    response, err := manager.UploadFile(ctx, req)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Upload successful! File ID: %s\n", response.FileID)

    // Example 2: Upload from file on disk
    fileData, err := os.ReadFile("local_file.txt")
    if err != nil {
        panic(err)
    }

    req2 := &connector.FileUploadRequest{
        File:      fileData,
        FileName:  "uploaded_file.txt",
        SessionID: "session_def456",
    }

    response2, err := manager.UploadFile(ctx, req2)
    if err != nil {
        panic(err)
    }

    fmt.Printf("Upload successful! File ID: %s, Timestamp: %s\n",
        response2.FileID, response2.Timestamp)
}
```

## Configuration

### Environment Variables

```bash
# Primary configuration
LLM_BASE_URL=https://api.openai.com
LLM_MODEL=gpt-3.5-turbo
LLM_MAX_TOKENS=1000
LLM_TEMPERATURE=0.7
LLM_TIMEOUT=30s
LLM_MAX_RETRIES=3
```

### Programmatic Configuration

```go
config := connector.Config{
    BaseURL:    "https://api.openai.com",
    APIKey:     "your-api-key",
    Timeout:    30 * time.Second,
    MaxRetries: 3,
}
```

## Error Handling

The connector provides comprehensive error handling with different error types:

```go
response, err := manager.SendMessage(ctx, req)
if err != nil {
    if connErr, ok := err.(*connector.ConnectorError); ok {
        switch connErr.Type {
        case connector.ErrorTypeAuth:
            // Handle authentication errors
        case connector.ErrorTypeRateLimit:
            // Handle rate limiting
        case connector.ErrorTypeServerError:
            // Handle server errors
        default:
            // Handle other errors
        }

        if connErr.IsRetryable() {
            // Error can be retried
        }
    }
}
```

### Error Types

- `ErrorTypeNetwork` - Network connectivity issues
- `ErrorTypeAuth` - Authentication/authorization failures
- `ErrorTypeRateLimit` - Rate limiting (retryable)
- `ErrorTypeServerError` - Server-side errors (retryable)
- `ErrorTypeTimeout` - Request timeouts
- `ErrorTypeInvalidInput` - Invalid request data

## Provider Support

### OpenAI
- Full API compatibility
- Streaming support
- WebSocket support (where available)

### Anthropic
- Claude models support
- Streaming support
- Custom API format handling

### Ollama (Local LLM)
- Local deployment support
- Extended timeout handling
- No API key required

### Custom Providers
The connector can be extended to support additional providers by implementing the appropriate request/response format handling.

## Performance Considerations

### Connection Pooling
WebSocket connections are pooled for efficiency:
- Default pool size: 10 connections
- Automatic cleanup of stale connections
- Load balancing across available connections

### Retry Logic
Configurable retry with exponential backoff:
- Default: 3 retries with 1s, 2s, 4s delays
- Respects HTTP status codes for retry decisions
- Circuit breaker pattern for persistent failures

### Memory Management
- Streaming responses use buffered channels
- Automatic cleanup of resources
- Context-based cancellation support

## Security Considerations

- No API keys or sensitive data stored in the connector
- HTTPS/WSS enforced for external communications
- Headers are passed through without modification
- Request/response data handled generically
- Timeout enforcement prevents resource leaks

## Migration from LLM-Specific Connector

If you're upgrading from the previous LLM-specific connector, here are the key changes:

### Before (LLM-Specific):
```go
// Old LLM-specific approach
config := connector.Config{
    BaseURL: "https://api.openai.com",
    APIKey:  "your-api-key",
}

manager := connector.NewManager(config)
req := &connector.MessageRequest{
    Messages: []connector.Message{{Role: "user", Content: "Hello"}},
    Model: "gpt-3.5-turbo",
}

response, err := manager.SendMessage(ctx, req)
```

### After (Generic):
```go
// New generic approach
config := connector.Config{
    BaseURL: "https://api.openai.com",  // Just base URL, no API key
}

manager := connector.NewManager(config)
requestBody := map[string]interface{}{
    "messages": []map[string]interface{}{
        {"role": "user", "content": "Hello"},
    },
    "model": "gpt-3.5-turbo",
}

headers := map[string]string{
    "Authorization": "Bearer your-api-key",
    "Content-Type": "application/json",
}

response, err := manager.Post(ctx, "/v1/chat/completions", headers, requestBody)
```

### Key Changes:
1. **No more API key in config** - Pass as Authorization header instead
2. **Path-based requests** - Services specify the endpoint path
3. **Generic request/response** - No LLM-specific types
4. **Flexible headers** - Full control over HTTP headers
5. **Service-layer adaptation** - LLM-specific logic moved to higher-level services

